package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "DEMAND_FORECASTING_STRATEGY_METADATA")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DemandForecastingStrategyMetadata {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "STRATEGY_NAME", nullable = false, length = 50)
    private String strategyName;

    @Column(name = "HISTORICAL_DAYS", nullable = false)
    private Integer historicalDays;

    @Column(name = "MINIMUM_DATA_POINTS_NEEDED", nullable = false)
    private Integer minimumDataPointsNeeded;

    @Column(name = "PAST_WEEKS_LOOKUP_TYPE", length = 50)
    private String pastWeeksLookupType;

    @Column(name = "NO_OF_PAST_WEEKS")
    private Integer noOfPastWeeks;

    @Column(name = "SAFETY_WEEKS")
    private Integer safetyWeeks;

    @Column(name = "SAFETY_STOCK_STRATEGY", length = 50)
    private String safetyStockStrategy;

    @Column(name = "HIGH_PRODUCT_SAFETY_VALUE", precision = 18, scale = 4)
    private BigDecimal highProductSafetyValue;

    @Column(name = "HIGH_MAX_STD_DEVIATION", precision = 18, scale = 4)
    private BigDecimal highMaxStdDeviation;

    @Column(name = "MEDIUM_PRODUCT_SAFETY_VALUE", precision = 18, scale = 4)
    private BigDecimal mediumProductSafetyValue;

    @Column(name = "MEDIUM_MAX_STD_DEVIATION", precision = 18, scale = 4)
    private BigDecimal mediumMaxStdDeviation;

    @Column(name = "LOW_PRODUCT_SAFETY_VALUE", precision = 18, scale = 4)
    private BigDecimal lowProductSafetyValue;

    @Column(name = "LOW_MAX_STD_DEVIATION", precision = 18, scale = 4)
    private BigDecimal lowMaxStdDeviation;

    @Column(name = "NEXT_DAY_SALES_CONSIDERATION_PERCENTAGE", precision = 18, scale = 4)
    private BigDecimal nextDaySalesConsiderationPercentage;

    @Column(name = "ENABLE_HIGH_DAY_WISE_SALE_EDIT", length = 1)
    private String enableHighDayWiseSaleEdit;

    @Column(name = "ENABLE_MEDIUM_DAY_WISE_SALE_EDIT", length = 1)
    private String enableMediumDayWiseSaleEdit;

    @Column(name = "ENABLE_LOW_DAY_WISE_SALE_EDIT", length = 1)
    private String enableLowDayWiseSaleEdit;

    @Column(name = "ENABLE_HIGH_SAFETY_STOCK_EDIT", length = 1)
    private String enableHighSafetyStockEdit;

    @Column(name = "ENABLE_MEDIUM_SAFETY_STOCK_EDIT", length = 1)
    private String enableMediumSafetyStockEdit;

    @Column(name = "ENABLE_LOW_SAFETY_STOCK_EDIT", length = 1)
    private String enableLowSafetyStockEdit;

    @Column(name = "HIGH_QUANTILE", precision = 18, scale = 4)
    private BigDecimal highQuantile;

    @Column(name = "MEDIUM_QUANTILE", precision = 18, scale = 4)
    private BigDecimal mediumQuantile;

    @Column(name = "LOW_QUANTILE", precision = 18, scale = 4)
    private BigDecimal lowQuantile;
}
