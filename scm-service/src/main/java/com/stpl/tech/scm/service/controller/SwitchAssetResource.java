package com.stpl.tech.scm.service.controller;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.SwitchAssetService;
import com.stpl.tech.scm.data.transport.model.SwitchAssetBody;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.SwitchAsset;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.ws.rs.QueryParam;


@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR+SCMServiceConstants.SWITCH_ASSET_ROOT_CONTEXT)
public class SwitchAssetResource extends AbstractSCMResources{

    @Autowired
    SwitchAssetService switchAssetService;

    @PostMapping("/upload-asset-image")
    Integer uploadSwitchAssetImage(@RequestParam(value = "type") FileType type,
                                               @RequestParam(value = "mimeType") MimeType mimeType,
                                               @RequestParam(value = "docType") DocUploadType docType,
                                               @RequestParam(value = "file") final MultipartFile file,
                                               @RequestParam(value = "userId") Integer userId,
                                               @RequestParam(value = "isNewAsset") Boolean isNewAsset
                                              ) throws SumoException {
        return switchAssetService.uploadAssetImage(type,mimeType,docType,file,userId,isNewAsset);
    }

    @GetMapping("/get-valid-asset")
    SwitchAsset getValidAsset(@QueryParam("unitId") Integer unitId, @QueryParam("tagValue") String tagValue) throws SumoException {
        return switchAssetService.getValidAssetFromUnitAndTag(unitId,tagValue);
    }

    @GetMapping("/send-otp")
    Boolean sendOtp(@QueryParam("userId") Integer userId) throws SumoException {
        return switchAssetService.sendOtp(userId);

    }

    @PostMapping("/start-switch-asset-process")
    Boolean startProcess(@RequestBody SwitchAssetBody switchAssetBody) throws SumoException {
        return switchAssetService.startProcess(switchAssetBody);
    }

    @Scheduled(cron = "0 0 0 15 * *", zone = "GMT+05:30")
    @GetMapping("/clean-switch-asset-temp-files")
    void cleanTempFiles(){
        switchAssetService.deleteTempFiles();
    }
}
