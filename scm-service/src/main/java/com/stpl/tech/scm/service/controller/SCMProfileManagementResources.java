package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.SCMProfileManagementService;
import com.stpl.tech.scm.domain.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.util.List;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.PROFILE_MANAGEMENT_ROOT_CONTEXT)
public class SCMProfileManagementResources extends AbstractResources {

	private final Logger LOG = LoggerFactory.getLogger(SCMProfileManagementResources.class);

	@Autowired
	private SCMProfileManagementService scmProfileManagementService;


	@Autowired
	EnvProperties env;


	@RequestMapping(method = RequestMethod.GET, value = "profile", produces = MediaType.APPLICATION_JSON)
	public ProfileDefinition viewProfile(@RequestParam(value = "profileId") final int profileId) {
		return scmProfileManagementService.viewProfile(profileId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "profiles", produces = MediaType.APPLICATION_JSON)
	public List<ProfileDefinition> viewAllProfiles() {
		return scmProfileManagementService.viewAllProfiles();
	}



	@RequestMapping(method = RequestMethod.POST, value = "profile", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public ProfileDefinition addNewProfile(@RequestBody final ProfileDefinition profileDefinition)
			throws DataUpdationException, SumoException {
		return scmProfileManagementService.addNewProfile(profileDefinition);
	}

	@RequestMapping(method = RequestMethod.POST, value = "profile-attributes-mappings", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public List<ProfileAttributeMapping> addProfileMappings(@RequestBody final List<ProfileAttributeMapping> profileAttributeMappings) throws SumoException {
		return scmProfileManagementService.addProfileAttributeMappings(profileAttributeMappings);
	}

	@RequestMapping(method = RequestMethod.GET, value = "profile-attributes-mappings", produces = MediaType.APPLICATION_JSON)
	public List<ProfileAttributeMapping> getAllAttributeMappingsForProfile(@RequestParam final int profileId)  {
		return scmProfileManagementService.getAllAttributeMappingsForProfile(profileId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "profile-attributes-mappings-sku", produces = MediaType.APPLICATION_JSON)
	public List<ProfileAttributeMapping> getAllAttributeMappingsWithProfileIdAndSKU(@RequestParam final int profileId)  {
		return scmProfileManagementService.getAllAttributeMappingsForProfileAndSKU(profileId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "profile-attributes-mappings-asset", produces = MediaType.APPLICATION_JSON)
	public List<ProfileAttributeMapping> getAllAttributeMappingsWithProfileIdAndAsset(@RequestParam final int profileId)  {
		return scmProfileManagementService.getAllAttributeMappingsForProfileAndAsset(profileId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "generic-attribute-value-mappings", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public List<EntityAttributeValueMapping> addGenericAttributeValueMappings(@RequestBody final List<EntityAttributeValueMapping> entityAttributeValueMappings) throws SumoException {
		return scmProfileManagementService.addGenericAttributeValueMappings(entityAttributeValueMappings);
	}

	@RequestMapping(method = RequestMethod.GET, value = "generic-attribute-value-mappings", produces = MediaType.APPLICATION_JSON)
	public List<EntityAttributeValueMapping> getEntityAttributeMappings(@RequestParam final int entityId,@RequestParam String entityType) {
		return scmProfileManagementService.getEntityAttributeMappings(entityId, entityType);
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload-profile-attribute-image", consumes = MediaType.MULTIPART_FORM_DATA)
	public String uploadProductImage(HttpServletRequest request,
									 @RequestParam(value = "mimeType") MimeType mimeType,
									 @RequestParam(value = "profileAttributeMappingId") Integer profileAttributeMappingId,
									 @RequestParam(value = "file") final MultipartFile file) throws SumoException {
		return scmProfileManagementService.uploadProfileMappingImage(mimeType, profileAttributeMappingId, file);
	}



}