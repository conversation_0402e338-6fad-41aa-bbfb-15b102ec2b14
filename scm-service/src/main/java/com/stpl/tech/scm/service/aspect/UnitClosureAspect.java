package com.stpl.tech.scm.service.aspect;

import com.stpl.tech.master.core.UnitClosureStateEnum;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.impl.JWTToken;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.util.webservice.MasterServiceClientEndpoints;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.notification.email.UnitClosureSuspendNotification;
import com.stpl.tech.scm.notification.email.template.UnitClosureSuspendNotificationTemplate;
import com.stpl.tech.scm.service.annotation.UnitClosureCheck;
import lombok.extern.log4j.Log4j2;
import org.apache.http.HttpResponse;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;


@Aspect
@Component
@Log4j2
public class UnitClosureAspect {

    @Autowired
    private MasterDataCache masterDataCache;
    @Autowired
    private EnvProperties props;
    @Autowired
    private TokenService<JWTToken> jwtService;

    @Autowired
    private SCMAssetManagementService scmAssetManagementService;

    @Around("@annotation(com.stpl.tech.scm.service.annotation.UnitClosureCheck)")
    public Object checkTransactionIsAvailable(ProceedingJoinPoint joinPoint) throws Throwable {

            log.info("::::::::::Inside check transaction available aspect:::::::");
            HttpServletRequest request = getRequestArgument(joinPoint);
            log.info(":::::::::: unit closure required : {} , request : {} :::::::", props.checkUnitClosureRequired(), request.getPathInfo());
            if (props.checkUnitClosureRequired() && request != null) {
                log.info("::::::::::Found request parameters in the request:::::::");
                JWTToken jwtToken = new JWTToken();
                jwtService.parseToken(jwtToken, request.getHeader("auth"));
                int unitId = jwtToken.getUnitId();
                Unit unit = masterDataCache.getUnit(unitId);
                MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
                UnitClosureCheck annotation = methodSignature.getMethod().getAnnotation(UnitClosureCheck.class);
                UnitClosureStateEnum unitClosureStateEnum = annotation.closeState();
                boolean byPassCloseState = annotation.bypassCloseState();
                if(unit.getClosure()==null){
                    return joinPoint.proceed();
                }
                Boolean isSuspended = false;

                if(UnitClosureStateEnum.PROCESSING.name().equalsIgnoreCase(unit.getClosure())){
                    boolean res =  scmAssetManagementService.checkLiveInvWithDayCloseClosing(unitId);
                    if(!res){
                       HttpResponse response =  WebServiceHelper.postRequestWithAuthInternal(props.getMasterServiceBasePath()+ MasterServiceClientEndpoints.SUSPEND_UNIT_CLOSURE_EVENT,props.getAuthToken(),unitId);
                       if(response.getStatusLine().getStatusCode()!=200){
                           log.info("####### Failed to suspend the unit closure event  ##########");
                           throw new SumoException("UNIT CLOSURE ERROR", "Error while suspending unit closure " + unit.getClosure());
                       }
                       isSuspended = true;
                    }
                }

                if (byPassCloseState) {
                    if ((UnitClosureStateEnum.PROCESSING.name().equalsIgnoreCase(unit.getClosure()) ||
                            UnitClosureStateEnum.CLOSED.name().equalsIgnoreCase(unit.getClosure())) && !isSuspended) {
                        throw new SumoException("UNIT CLOSURE ERROR", "Unit Closure found in " + unit.getClosure());
                    }
                }

                if (unitClosureStateEnum == UnitClosureStateEnum.NO) {
                    return joinPoint.proceed();
                }

                if (UnitClosureStateEnum.PROCESSING == unitClosureStateEnum && UnitClosureStateEnum.PROCESSING.name().equalsIgnoreCase(unit.getClosure()) && !isSuspended ) {
                    throw new SumoException("UNIT CLOSURE ERROR", "Unit Closure found in " + unit.getClosure());

                }

                if (UnitClosureStateEnum.CLOSED == unitClosureStateEnum && UnitClosureStateEnum.CLOSED.name().equalsIgnoreCase(unit.getClosure()) && !isSuspended) {
                    throw new SumoException("UNIT CLOSURE ERROR", "Unit Closure found in " + unit.getClosure());
                }

                if(isSuspended){
                    return joinPoint.proceed();
                }
            }
            return joinPoint.proceed();
    }


    private HttpServletRequest getRequestArgument(ProceedingJoinPoint pjp) {
        for (Object object : pjp.getArgs()) {
            if (object instanceof HttpServletRequest) {
                return (HttpServletRequest) object;
            }
        }
        return null;
    }

}
