package com.stpl.tech.scm.service.controller;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.master.core.external.excel.impl.GenericExcelManagementServiceImpl;
import com.stpl.tech.master.data.model.ExcelRequestData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.PaymentRequestManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.MimeType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.GENERIC_RESOURCE_MANAGEMENT_ROOT_CONTEXT)
public class GenericResource extends AbstractSCMResources{

    @Autowired
    private PaymentRequestManagementService paymentRequestManagementService;

    @Autowired
    private GenericExcelManagementServiceImpl genericExcelManagementService;

    @RequestMapping(method = RequestMethod.POST, value = "upload-document-generic", consumes = MediaType.MULTIPART_FORM_DATA )
    public DocumentDetail uploadDocument(HttpServletRequest request,
                                         @RequestParam(value = "type") FileType type,
                                         @RequestParam(value = "mimeType") MimeType mimeType,
                                         @RequestParam(value = "userId") Integer userId,
                                         @RequestParam(value = "docType") DocUploadType docType,
                                         @RequestParam(value = "fileName") String fileName,
                                         @RequestParam(value = "baseDir") String baseDir,
                                         @RequestParam(value = "file") final MultipartFile file) throws DocumentException, IOException, SumoException {
        return paymentRequestManagementService.uploadDocument(type, mimeType, docType, userId, file, (fileName + SCMUtil.getCurrentTimeISTStringWithNoColons()), baseDir);
    }
}
