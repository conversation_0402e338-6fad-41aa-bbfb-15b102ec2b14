/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.service.core;

import javax.sql.DataSource;

import org.apache.commons.lang.NotImplementedException;

import com.stpl.tech.master.util.DataSourceConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.ExecutionEnvironment;

public class ReportingUtil extends AppUtils {



	public static DataSource getDataSourceBean(ExecutionEnvironment env) {
		switch (env) {
		case DEV:
			return ReportingServiceContextFactory.getBean(DataSourceConstants.DEV_DATA_SOURCE, DataSource.class);
		case UAT:
			return ReportingServiceContextFactory.getBean(DataSourceConstants.UAT_DATA_SOURCE, DataSource.class);
		case PROD:
		case SPROD:
			return ReportingServiceContextFactory.getBean(DataSourceConstants.PROD_DATA_SOURCE, DataSource.class);
		default:
			throw new NotImplementedException("Do not have Data Source setup for " + env.name());
		}
	}
}