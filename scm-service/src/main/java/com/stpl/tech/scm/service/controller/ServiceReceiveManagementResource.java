package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.PaymentRequestManagementService;
import com.stpl.tech.scm.core.service.ServiceReceiveManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.ServiceReceiveVO;
import com.stpl.tech.scm.domain.model.BulkRequestVO;
import com.stpl.tech.scm.domain.model.BusinessCostCenter;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.ServiceReceive;
import com.stpl.tech.scm.domain.model.ServiceReceiveShort;
import com.stpl.tech.scm.domain.model.WastageAggregatedData;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.service.model.MbEmailVO;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.SERVICE_RECEIVE_MANAGEMENT_ROOT_CONTEXT)
public class ServiceReceiveManagementResource extends AbstractSCMResources {

	private static final Logger LOG = LoggerFactory.getLogger(ServiceReceiveManagementResource.class);

	@Autowired
	private ServiceReceiveManagementService serviceReceiveManagementService;

	@Autowired
	private PaymentRequestManagementService paymentRequestManagementService;

	@Autowired
	private ExcelViewGenerator excelViewGenerator;


	@RequestMapping(method = RequestMethod.GET, value = "bcc", produces = MediaType.APPLICATION_JSON)
	public List<BusinessCostCenter> getBusinessCostCentersData() throws SumoException {
		return serviceReceiveManagementService.getBusinessCostCentersData();
	}

	@RequestMapping(method = RequestMethod.POST, value = "bcc/create", produces = MediaType.APPLICATION_JSON)
	public boolean createBusinessCostCentersData(@RequestBody BusinessCostCenter businessCostCenter)
			throws SumoException {
		return serviceReceiveManagementService.createBusinessCostCentersData(businessCostCenter);
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-service-receives", produces = MediaType.APPLICATION_JSON)
	public List<Integer> createServiceReceives(@RequestBody final List<ServiceReceiveVO> srVOs) throws SumoException {
		return serviceReceiveManagementService.createServiceReceive(srVOs);
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-service-receive", produces = MediaType.APPLICATION_JSON)
	public Integer createServiceReceive(@RequestBody final ServiceReceiveVO srVO) throws SumoException {
		return serviceReceiveManagementService.createServiceReceive(srVO);
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-service-receive", produces = MediaType.APPLICATION_JSON)
	public Integer updateProvisionalServiceReceive(@RequestBody final ServiceReceiveVO srVO, @RequestParam Integer srId) throws SumoException {
		return serviceReceiveManagementService.updateProvisionalServiceReceive(srVO, srId, srVO.getRemoveDrillDownIdList());
	}

	@RequestMapping(method = RequestMethod.POST, value = "cancel-service-receive/{srId}/{userId}", produces = MediaType.APPLICATION_JSON)
	public boolean cancelServiceReceive(@PathVariable(value = "srId") final int srId,
			@PathVariable(value = "userId") final int userId) throws SumoException {
		return serviceReceiveManagementService.cancelServiceReceive(srId, userId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload-invoice", consumes = MediaType.MULTIPART_FORM_DATA)
	public DocumentDetail uploadDocument(@RequestParam(value = "type") FileType type,
			@RequestParam(value = "mimeType") MimeType mimeType, @RequestParam(value = "userId") Integer userId,
			@RequestParam(value = "docType") DocUploadType docType,
			@RequestParam(value = "file") final MultipartFile file) {
		return serviceReceiveManagementService.uploadDocument(type, mimeType, docType, userId, file);
	}

	@RequestMapping(method = RequestMethod.GET, value = "find-service-receive", produces = MediaType.APPLICATION_JSON)
	public List<ServiceReceive> findServiceReceive(@RequestParam(required = false) Integer vendorId,
												   @RequestParam(required = false) Integer bccId,
			@RequestParam(required = false) Integer locationId, @RequestParam(required = false) Integer userId,
			@RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate, @RequestParam(required = false) Integer serviceOrderId) {
		return serviceReceiveManagementService.findServiceReceive(vendorId, locationId, userId,
				SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), serviceOrderId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "find-service-receive-short", produces = MediaType.APPLICATION_JSON)
	public List<ServiceReceiveShort> findServiceReceiveShort(@RequestParam(required = false) Integer vendorId,
															 @RequestParam(required = false) Integer bccId,
															 @RequestParam(required = false) Integer locationId, @RequestParam(required = false) Integer userId,
															 @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate, @RequestParam(required = false) Integer serviceOrderId) {
		return serviceReceiveManagementService.findServiceReceiveShort(bccId,vendorId, locationId, userId,
				SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), serviceOrderId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-Sr-For-So", produces = MediaType.APPLICATION_JSON)
	public List<ServiceReceive> getLinkedSrForSo(@RequestParam(required = false) Integer vendorId,
												   @RequestParam(required = false) Integer locationId, @RequestParam(required = false) Integer userId,
												   @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate,
												 	@RequestParam(required = false) Integer prId, @RequestParam(required = false) Integer capexId) throws SumoException {
		return serviceReceiveManagementService.getLinkedSrForSo(vendorId, locationId, userId,
				SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), prId,capexId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "find-service-receive-for-payment", produces = MediaType.APPLICATION_JSON)
	public List<ServiceReceive> findServiceReceiveForPayment(@RequestParam Integer vendorId,
			@RequestParam Integer companyId, @RequestParam(required = false) Integer stateId, @RequestParam Integer userId,
			@RequestParam String startDate, @RequestParam String endDate) {
		return serviceReceiveManagementService.findServiceReceiveForPayment(vendorId, companyId, userId, stateId,
				SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate));
	}

	@RequestMapping(method = RequestMethod.POST, value = "set-service-receive-for-no-payment", produces = MediaType.APPLICATION_JSON)
	public Boolean setVendorGRsForNoPayment(@RequestBody BulkRequestVO request) throws SumoException {
		return serviceReceiveManagementService.setServiceReceiveForNoPayment(request);
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-payment-request", produces = MediaType.APPLICATION_JSON)
	public PaymentRequest createPaymentRequest(@RequestBody PaymentRequest request) throws SumoException {
		LOG.info(JSONSerializer.toJSON(request));
		PaymentRequest result = paymentRequestManagementService.createPaymentRequest(request,null);
		return result;
	}

	@RequestMapping(method = RequestMethod.GET, value = "find-service-receive-for-payment-request", produces = MediaType.APPLICATION_JSON)
	public List<ServiceReceive> searchServiceReceivingForPaymentRequest(@RequestParam Integer paymentRequestId)
			throws SumoException {
		LOG.info("Searching Service Receivings for Payment Request Id {}", paymentRequestId);
		List<ServiceReceive> result = new ArrayList<>();
		result = serviceReceiveManagementService.searchServiceReceivingForPaymentRequest(paymentRequestId);
		return result;
	}

	@RequestMapping(method = RequestMethod.POST, value = "approve-sr", produces = MediaType.APPLICATION_JSON)
	public Boolean approveSR(@RequestParam Integer srId) throws SumoException {
		return serviceReceiveManagementService.approveSR(srId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-min-max-date-for-srs", produces = MediaType.APPLICATION_JSON)
	public Map<String,Date> getMinMaxDateForSrs(@RequestParam List<Integer> srIds) throws SumoException {
		return serviceReceiveManagementService.getMinMaxDateForSrs(srIds);
	}

	@RequestMapping(method = RequestMethod.GET, value = "recheck-all-srs-for-advance", produces = MediaType.APPLICATION_JSON)
	public List<Integer> reCheckAllSrsForAdvance(@RequestParam Integer srId) throws SumoException {
		return serviceReceiveManagementService.reCheckAllSrsForAdvance(srId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "send-mb-email", produces = MediaType.APPLICATION_JSON)
	public View sendEmail(HttpServletResponse response,@RequestBody MbEmailVO mbEmailVO) throws SumoException {
			 return excelViewGenerator.generateMeasurementBook(response ,mbEmailVO.getSrId() , mbEmailVO.getToEmails(),mbEmailVO.getCcEmails(),mbEmailVO.getInitiatedBy());
	}

	@PostMapping("generate-sr-sheet")
	public View generateWastageSheet(@RequestBody List<Integer> srIds) throws SumoException {
 return serviceReceiveManagementService.generateSRExcelSheet(srIds);
	}

}
