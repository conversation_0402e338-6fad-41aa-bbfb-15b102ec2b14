package com.stpl.tech.scm.service.model;

import com.stpl.tech.scm.domain.model.EstimationSalesDataRequest;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class UnitReferenceData {

	private int unitId;
	private Date fulfillmentDate;
	private int noOfDays;
	private String brandName;
	private List<ProductCategoryData> categoryList;
    private List<EstimationSalesDataRequest> salesData;
    private List<Integer> bufferedCategoryList;
    private Integer categoryBufferPercentage;
	private Map<String,BigDecimal> dineInSale;
	private Map<String,BigDecimal> deliverySale;
	private String refreshDate;
	private Float totalSale;
	private String requestedBy;
	private Map<String, Map<Integer, BigDecimal>> scmSuggestions;

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public Date getFulfillmentDate() {
		return fulfillmentDate;
	}

	public void setFulfillmentDate(Date fulfillmentDate) {
		this.fulfillmentDate = fulfillmentDate;
	}

	public int getNoOfDays() {
		return noOfDays;
	}

	public void setNoOfDays(int noOfDays) {
		this.noOfDays = noOfDays;
	}

	public List<ProductCategoryData> getCategoryList() {
		return categoryList;
	}

	public void setCategoryList(List<ProductCategoryData> categoryList) {
		this.categoryList = categoryList;
	}


	public List<EstimationSalesDataRequest> getSalesData() {
		return salesData;
	}

	public void setSalesData(List<EstimationSalesDataRequest> salesData) {
		this.salesData = salesData;
	}

	public List<Integer> getBufferedCategoryList() {
		return bufferedCategoryList;
	}

	public void setBufferedCategoryList(List<Integer> bufferedCategoryList) {
		this.bufferedCategoryList = bufferedCategoryList;
	}

	public Integer getCategoryBufferPercentage() {
		return categoryBufferPercentage;
	}

	public void setCategoryBufferPercentage(Integer categoryBufferPercentage) {
		this.categoryBufferPercentage = categoryBufferPercentage;
	}

	public Map<String, BigDecimal> getDineInSale() {
		return dineInSale;
	}

	public void setDineInSale(Map<String, BigDecimal> dineInSale) {
		this.dineInSale = dineInSale;
	}

	public Map<String, BigDecimal> getDeliverySale() {
		return deliverySale;
	}

	public void setDeliverySale(Map<String, BigDecimal> deliverySale) {
		this.deliverySale = deliverySale;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getRefreshDate() {
		return refreshDate;
	}

	public void setRefreshDate(String refreshDate) {
		this.refreshDate = refreshDate;
	}

	public Float getTotalSale() {
		return totalSale;
	}

	public void setTotalSale(Float totalSale) {
		this.totalSale = totalSale;
	}

	public String getRequestedBy() {
		return requestedBy;
	}

	public void setRequestedBy(String requestedBy) {
		this.requestedBy = requestedBy;
	}

	public Map<String, Map<Integer, BigDecimal>> getScmSuggestions() {
		return scmSuggestions;
	}

	public void setScmSuggestions(Map<String, Map<Integer, BigDecimal>> scmSuggestions) {
		this.scmSuggestions = scmSuggestions;
	}

	@Override
	public String toString() {
		return "UnitReferenceData [unitId=" + unitId + ", fulfillmentDate=" + fulfillmentDate + ", noOfDays=" + noOfDays
				+ ", categoryListSize=" + (categoryList != null ? categoryList.size() : 0) + "]";
	}

}
