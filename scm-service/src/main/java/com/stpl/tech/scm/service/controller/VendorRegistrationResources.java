package com.stpl.tech.scm.service.controller;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import javax.ws.rs.core.MediaType;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.model.VendorDetailRequestChanges;
import com.stpl.tech.scm.data.model.VendorDetailChangeRequestData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.multipart.MultipartFile;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.service.SCMVendorManagementService;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.VendorAccountDetail;
import com.stpl.tech.scm.domain.model.VendorCompanyDetail;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorRegistrationRequest;
import com.stpl.tech.scm.service.model.LocationsVO;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 25-04-2017.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.VENDOR_REGISTRATION_ROOT_CONTEXT)
public class VendorRegistrationResources {

    @Autowired
    private SCMVendorManagementService scmVendorManagementService;

    @Autowired
    private MasterDataCache masterDataCache;

    @RequestMapping(method = RequestMethod.GET, value = "get-auth", produces = MediaType.APPLICATION_JSON)
    public String getAuthorization() {
        return scmVendorManagementService.getRegistrationAuthToken();
    }

    @RequestMapping(method = RequestMethod.POST, value = "validate", produces = MediaType.APPLICATION_JSON)
    public VendorRegistrationRequest validateRegistration(@RequestBody final Map<String,String> tokenMap)
            throws VendorRegistrationException, UnsupportedEncodingException {
        return scmVendorManagementService.validateRequest(tokenMap.get("token"));
    }

    @RequestMapping(method = RequestMethod.POST, value = "save-basic-detail", produces = MediaType.APPLICATION_JSON)
    public VendorDetail saveBasicDetails(@RequestBody final VendorDetail detail) throws VendorRegistrationException{
        return scmVendorManagementService.saveBasicDetails(detail);
    }
    @RequestMapping(method = RequestMethod.POST, value = "save-company-detail", produces = MediaType.APPLICATION_JSON)
    public VendorDetail saveCompanyDetails(@RequestBody final VendorCompanyDetail detail) throws VendorRegistrationException, SumoException {
        return scmVendorManagementService.saveCompanyDetails(detail);
    }
    @RequestMapping(method = RequestMethod.POST, value = "save-account-detail", produces = MediaType.APPLICATION_JSON)
    public VendorDetail saveAccountDetails(@RequestBody final VendorAccountDetail detail) throws VendorRegistrationException, SumoException {
        return scmVendorManagementService.saveAccountDetails(detail);
    }
    @RequestMapping(method = RequestMethod.POST, value = "save-locations", produces = MediaType.APPLICATION_JSON)
    public VendorDetail saveLocationDetails(@RequestBody final LocationsVO locationsVO) throws VendorRegistrationException, SumoException {
        if(locationsVO.getVendorId()!=null && locationsVO.getLocations()!=null && !locationsVO.getLocations().isEmpty()){
            Integer vendorId = locationsVO.getVendorId();
            return scmVendorManagementService.saveDispatchLocations(locationsVO.getLocations(),locationsVO.getRemovedLocations(), vendorId);
        }
        return null;
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-vendor/{id}", produces = MediaType.APPLICATION_JSON)
    public VendorDetail getVendor(@PathVariable(value = "id") final Integer vendorId) throws VendorRegistrationException{
        return scmVendorManagementService.getVendor(vendorId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-document", consumes = MediaType.MULTIPART_FORM_DATA)
    public DocumentDetail uploadDocument(@RequestParam(value = "type") FileType type,
                                         @RequestParam(value = "mimeType") MimeType mimeType,
                                         @RequestParam(value = "docType") DocUploadType docType,
                                         @RequestParam(value = "vendorId") Integer vendorId,
                                         @RequestParam(value = "file") final MultipartFile file)
            throws VendorRegistrationException{
        return scmVendorManagementService.uploadDocument(type, mimeType, docType, vendorId, file);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-tds-document", consumes = MediaType.MULTIPART_FORM_DATA)
    public DocumentDetail uploadTdsForVendor(@RequestParam(value = "type") FileType type,
                                             @RequestParam(value = "mimeType") MimeType mimeType,
                                             @RequestParam(value = "docType") DocUploadType docType,
                                             @RequestParam(value = "vendorId") Integer vendorId,
                                             @RequestParam(value = "file") final MultipartFile file,
                                             @RequestParam(value = "tdsStatus") Boolean tdsStatus)
        throws VendorRegistrationException {
        return scmVendorManagementService.uploadTdsDocument(type, mimeType, docType, vendorId, file,tdsStatus);
    }


    @RequestMapping(method = RequestMethod.GET, value = "delivery-locations", produces = MediaType.APPLICATION_JSON)
    public Collection<Location> getAllDeliveryLocationMetadata() {
        List<Location> locations = new ArrayList<Location>(masterDataCache.getAllLocations().values());
        locations.forEach(o -> {
            o.getState().getLocations().clear();
        });
        return locations;
    }

    @RequestMapping(method = RequestMethod.GET, value = "vendor-duplicate-pan-check", produces = MediaType.APPLICATION_JSON)
	public boolean getVendorDuplicatePanCheck(@RequestParam final String vendorType,@RequestParam(required = false) final String vendorPan) {
		return scmVendorManagementService.checkDuplicatePanCheck(vendorType,vendorPan);
	}

    @RequestMapping(method = RequestMethod.GET, value = "vendor-duplicate-name-check", produces = MediaType.APPLICATION_JSON)
    public boolean getVendorDuplicateNamecheck(@RequestParam final String vendorName,@RequestParam final String vendorType,@RequestParam final String city ,
                                              @RequestParam final  String state) {
        return scmVendorManagementService.checkDuplicateVendorName(vendorName,vendorType,city,state);
    }

    @RequestMapping(method = RequestMethod.GET, value = "check-for-vendor-approved", produces = MediaType.APPLICATION_JSON)
    public Boolean checkForApprovedVendor(@RequestParam  Integer vendorId) {
        return scmVendorManagementService.checkForApprovedVendorAtleastOnce(vendorId);
    }

    @GetMapping  (value = "get-requested-changes", produces = MediaType.APPLICATION_JSON)
    public List<VendorDetailRequestChanges> getRequestedChanges(@RequestParam Integer vendorId) throws Exception {
        return scmVendorManagementService.getRequestedChanges(vendorId);
    }


}
