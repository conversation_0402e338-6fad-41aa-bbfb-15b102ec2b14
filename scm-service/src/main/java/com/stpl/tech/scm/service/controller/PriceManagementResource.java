package com.stpl.tech.scm.service.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.domain.model.ScmRecipeProductCost;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.service.PriceManagementService;
import com.stpl.tech.scm.domain.model.CostDetail;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.PRICE_MANAGEMENT_ROOT_CONTEXT)
@Log4j2
public class PriceManagementResource extends AbstractSCMResources {
	
	@Autowired
    private PriceManagementService priceManagementService;

    @RequestMapping(method = RequestMethod.GET, value = "price-details-for-product", produces = MediaType.APPLICATION_JSON)
    public List<CostDetail> getPriceDetailsForProduct(@RequestParam(required = true) final int unitId,
            @RequestParam(required = true) final String keyType,
            @RequestParam(required = true) final int keyId) {
        return priceManagementService.getPriceDetails(unitId,keyType,keyId);
    }
    
    @RequestMapping(method = RequestMethod.POST, value = "add-price-details", produces = MediaType.APPLICATION_JSON)
    public boolean addPriceDetails(@RequestBody CostDetail costDetail) throws InventoryUpdateException {
        return priceManagementService.addPriceDetails(costDetail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-price-details-in-bulk", consumes = MediaType.MULTIPART_FORM_DATA)
    public boolean addPriceDetailsInBulk(HttpServletRequest request, @RequestParam(value = "file") final MultipartFile file) throws InventoryUpdateException, SumoException {
        Map<Integer, List<CostDetail>> uploadedCostDetailList = priceManagementService.readUploadedCostDetails(file);
        List<String> errorMessages = new ArrayList<>();
        uploadedCostDetailList.forEach((unitId, costDetails) -> priceManagementService.addPriceDetailsInBulk(unitId, costDetails, errorMessages));
        if (!errorMessages.isEmpty()) {
            throw new SumoException("Error Occurred While Creating Cost Details" , "Error Occurred for below Unit Ids <br> " + Arrays.asList(errorMessages.toArray()));
        }
        return true;
    }
    
    @RequestMapping(method = RequestMethod.PUT, value = "update-price-details", produces = MediaType.APPLICATION_JSON)
    public boolean upatePriceDetails(@RequestBody CostDetail costDetail) {
        return priceManagementService.updatePriceDetails(costDetail);
    }
    
    @RequestMapping(method = RequestMethod.GET, value = "price-details-for-unit", produces = MediaType.APPLICATION_JSON)
    public List<CostDetail> getPriceDetailsForUnit(@RequestParam(required = true) final int unitId) {
        return priceManagementService.getPriceDetailsForUnit(unitId);
    }
    
    @RequestMapping(method = RequestMethod.GET, value = "price-details-for-product-list", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, BigDecimal> getPriceDetailsForProductList(@RequestParam(required = true) final int unitId,
            @RequestParam(required = true) final String keyType,
            @RequestParam(required = true) final List<Integer> productIdList) {
        return priceManagementService.getWeightedPriceDetailsForProducts(unitId,keyType,productIdList);
    }

    @RequestMapping(method = RequestMethod.GET, value = "scm-recipe-cost", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, ScmRecipeProductCost> getScmRecipeCost(@RequestParam(required = false, defaultValue = "NCR") String region ,
                                                                               @RequestParam(required = true) final List<Integer> productIdList) {
        return priceManagementService.getScmRecipeCost(region, productIdList);
    }

//    @Scheduled(cron="0 15 13 * * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value = "reset-expiry-date-of-expired-stock")
    public void resetExpiryDateOfExpiredStock() {
        log.info("::: Started Resetting Expiry Date of Expired Stock :::");
        priceManagementService.resetExpiryDateOfExpiredStockForCafes();
        priceManagementService.resetExpiryDateOfExpiredStockForKWh();
        log.info("::: Complete Resetting Expiry Date of Expired Stock :::");
    }
}
