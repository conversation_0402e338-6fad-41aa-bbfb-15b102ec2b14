/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.service.controller.view;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.recipe.model.UnitOfMeasure;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.ProductProjectionsSevice;
import com.stpl.tech.scm.core.service.ProductionBookingService;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.ServiceOrderManagementService;
import com.stpl.tech.scm.core.service.ServiceReceiveManagementService;
import com.stpl.tech.scm.core.service.TransferOrderManagementService;
import com.stpl.tech.scm.core.util.EWayHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.ProductProjectionsDao;
import com.stpl.tech.scm.data.dao.SkuMappingDao;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexRequestDetailData;
import com.stpl.tech.scm.data.model.CapexTemplateData;
import com.stpl.tech.scm.data.model.CompanyBankMapping;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.data.model.ListDetailData;
import com.stpl.tech.scm.data.model.ListTypeData;
import com.stpl.tech.scm.data.model.ProductProjectionsDetailsData;
import com.stpl.tech.scm.data.model.ProductionUnitData;
import com.stpl.tech.scm.data.transport.model.TransportMode;
import com.stpl.tech.scm.domain.model.AddressDetail;
import com.stpl.tech.scm.domain.model.BankIfscCode;
import com.stpl.tech.scm.domain.model.BulkSoDetail;
import com.stpl.tech.scm.domain.model.CapexAuditDetail;
import com.stpl.tech.scm.domain.model.CorrectedSalesInvoiceDetails;
import com.stpl.tech.scm.domain.model.CorrectedSalesInvoiceItemDetails;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.BookingConsumption;
import com.stpl.tech.scm.domain.model.GoodsReceivedItem;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.PaymentType;
import com.stpl.tech.scm.domain.model.PlanOrderItem;
import com.stpl.tech.scm.domain.model.PlanOrderItemPrep;
import com.stpl.tech.scm.domain.model.PlanOrderItemPrepItem;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.ProductProjectionsUnitDetail;
import com.stpl.tech.scm.domain.model.ProductionItemType;
import com.stpl.tech.scm.domain.model.ProductionPlanEvent;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.SalesPerformaCorrectedType;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.scm.domain.model.SCMProductItem;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoiceItem;
import com.stpl.tech.scm.domain.model.SalesPerformaItemTax;
import com.stpl.tech.scm.domain.model.SalesPerformaType;
import com.stpl.tech.scm.domain.model.ServiceOrder;
import com.stpl.tech.scm.domain.model.ServiceOrderItem;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;
import com.stpl.tech.scm.domain.model.ServiceReceive;
import com.stpl.tech.scm.domain.model.ServiceReceiveItem;
import com.stpl.tech.scm.domain.model.ServiceReceivedItemDrilldown;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuStockForUnit;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.SubCategoryDefinition;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.TransferOrderItem;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VendorDebitBalanceVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.WastageAggregatedData;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.notification.email.PaymentRequestEmailNotification;
import com.stpl.tech.scm.notification.email.template.MeasurementBookTemplate;
import com.stpl.tech.scm.notification.email.template.ProductProjectionsEmailNotificationTemplate;
import com.stpl.tech.scm.service.controller.CapexManagementResource;
import com.stpl.tech.scm.domain.model.EstimationSalesDataRequest;
import com.stpl.tech.scm.service.model.UnitReferenceData;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static org.apache.poi.ss.usermodel.CellType.NUMERIC;
import static org.apache.poi.ss.usermodel.CellType.STRING;

@SuppressWarnings("deprecation")
@Component
public class ExcelViewGenerator {

    private static final Logger LOG = LoggerFactory.getLogger(CapexManagementResource.class);

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache dataCache;

    @Autowired
    private SCMProductManagementService productService;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private EnvProperties props;

    @Autowired
    private SkuMappingDao dao;

    @Autowired
    private ProductProjectionsDao productProjectionsDao;

    @Autowired
    private ProductProjectionsSevice productProjectionsSevice;

    @Autowired
    private SCMNotificationService notificationService;


    @Autowired
    private ServiceOrderManagementService serviceOrderManagementService;

    @Autowired
    private ServiceReceiveManagementService serviceReceiveManagementService;

    @Autowired
    private TransferOrderManagementService transferOrderManagementService;

    @Autowired
    private RequestOrderManagementService requestOrderManagementService;

    @Autowired
    private ProductionBookingService productionBookingService;

    public View getStockView(StockTakeType frequency, List<ProductDefinition> productList) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Unit Inventory Sheet -" + frequency.toString() + "-" + SCMUtil.getCurrentDate()
                        + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
                Sheet sheet = getSheetForWorkBook(workbook,
                        "List of products-" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                sheet.setDefaultColumnWidth(20);

                CellStyle style = generateHeaderStyle(workbook);

                Row header = sheet.createRow(0);

                header.createCell(0).setCellValue("Event Type");
                header.getCell(0).setCellStyle(style);

                header.createCell(1).setCellValue("Product ID");
                header.getCell(1).setCellStyle(style);

                header.createCell(2).setCellValue("Product Name");
                header.getCell(2).setCellStyle(style);

                header.createCell(3).setCellValue("Unit of Measure");
                header.getCell(3).setCellStyle(style);

                header.createCell(4).setCellValue("Stock");
                header.getCell(4).setCellStyle(style);

                for (int i = 0; i < productList.size(); i++) {
                    ProductDefinition product = productList.get(i);
                    setText(getCell(sheet, i + 1, 0), frequency.toString());
                    setText(getCell(sheet, i + 1, 1), product.getProductId().toString());
                    setText(getCell(sheet, i + 1, 2), product.getProductName());
                    setText(getCell(sheet, i + 1, 3), product.getUnitOfMeasure());
                    setText(getCell(sheet, i + 1, 4), "");
                }
            }
        };
    }


    private void generateCommentSheet(Workbook workbook , Sheet sheet , List<RequestOrder> requestOrders){
        CellStyle style = generateHeaderStyle(workbook);
        sheet.setDefaultColumnWidth(20);
        Row header = sheet.createRow(0);
        header.createCell(0).setCellValue("UNIT ID");
        header.getCell(0).setCellStyle(style);
        header.createCell(1).setCellValue("UNIT NAME");
        header.getCell(1).setCellStyle(style);
        header.createCell(2).setCellValue("RO ID");
        header.getCell(2).setCellStyle(style);
        header.createCell(3).setCellValue("COMMENT");
        header.getCell(3).setCellStyle(style);

        int i = 1;

        Collections.sort(requestOrders,
                (r1, r2) -> {
                    int ret = r1.getRequestUnit().getName().compareTo(r2.getRequestUnit().getName());
                    if(ret == 0){
                        ret = r1.getId().compareTo(r2.getId());
                    }
                    return ret;
                });
        for(RequestOrder requestOrder : requestOrders){
                Row row = sheet.createRow(i);
                i++;
                row.createCell(0).setCellValue(requestOrder.getRequestUnit().getId());
                row.createCell(1).setCellValue(requestOrder.getRequestUnit().getName());
                row.createCell(2).setCellValue(requestOrder.getId());
                if(Objects.nonNull(requestOrder.getComment())){
                   row.createCell(3).setCellValue(requestOrder.getComment());
                }
        }
    }

    public void getRequestOrderViewBulk(List<RequestOrder> requestOrders, ProductionPlanEvent event , String path , List<String> files ) throws IOException, DataNotFoundException, SumoException {
        Map<Integer, List<ProductPackagingMapping>> productPackagings = productService.viewAllProductPackagingMapping();
        XSSFWorkbook workbook = new XSSFWorkbook();

        String fileName = "Request Order Sheet -" + event.getId() + ".xlsx";

        if (requestOrders.size() > 1) {
            generatePivotSheet(workbook, requestOrders, event);
            generateExcessSheet(workbook, requestOrders, event);
            generateUnitWiseSheet(workbook, requestOrders, productPackagings,false);
            List<RequestOrderItem> itemList = getRequestOrderItems(requestOrders);
            String sheetName = "PRODUCT_LIST" + SCMUtil.getCurrentTimeISTStringWithNoColons();
            Sheet sheet = getSheetForWorkBook(workbook, sheetName);
            generateSheet(workbook, sheet, itemList, productPackagings, null, 2);
            String commentSheetName = "COMMENT_SHEET";
            Sheet commentsSheet = getSheetForWorkBook(workbook, commentSheetName);
            generateCommentSheet(workbook, commentsSheet, requestOrders);
        }

        if (event != null) {
            for (ProductionItemType type : ProductionItemType.values()) {
                generatePlanningConsumptionSheet(workbook, event, type);
            }
        }
        int sheetNum = workbook.getNumberOfSheets();
        if (requestOrders.size() > 1) {
            workbook.setSheetOrder("UNIT_REFERENCE_SHEET", sheetNum - 1);
            workbook.setSheetOrder("COMMENT_SHEET", sheetNum - 2);
        }
        ByteArrayConvertor(workbook, path, files, fileName);
    }

    public void ByteArrayConvertor(XSSFWorkbook workbook, String path , List<String> files,String fileName)throws DataNotFoundException, SumoException, IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            workbook.write(bos);
        } catch (IOException e1) {
            LOG.error("Error while Converting to ByteArray", e1);
        }
        byte[] barray = bos.toByteArray();
        File tempFile = new File(path + fileName);
        if (!tempFile.exists()) {
            tempFile.getParentFile().mkdirs();
        }
        tempFile.createNewFile();
        FileOutputStream fs = new FileOutputStream(tempFile);
        files.add(tempFile.getPath());
        fs.write(barray);
        fs.close();
        bos.close();
    }

    public View previewOrderView(List<RequestOrder> requestOrders, Date fulfilmentDate) {
        Map<Integer, List<ProductPackagingMapping>> productPackagings = productService.viewAllProductPackagingMapping();

        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Preview Request Order Sheet -" + fulfilmentDate + "-"
                        + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xlsx\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
                    generateUnitWiseSheet(workbook, requestOrders, productPackagings,true);
            }
        };
    }

    public View getRequestOrderView(List<RequestOrder> requestOrders, Date fulfilmentDate, ProductionPlanEvent event) {
        Map<Integer, List<ProductPackagingMapping>> productPackagings = productService.viewAllProductPackagingMapping();
        Map<Integer, List<SkuPackagingMapping>> skuPackagings = productService.viewAllSkuPackagingMapping();

        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Request Order Sheet -" + fulfilmentDate + "-"
                        + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xlsx\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);

                if (requestOrders.size() > 1) {
                    generatePivotSheet(workbook, requestOrders, event);
                    generateExcessSheet(workbook,requestOrders,event);
                    generateUnitWiseSheet(workbook, requestOrders, productPackagings,false);
                    try{
                        generateSkuSheet(workbook,requestOrders);
                    }catch (Exception e){
                        LOG.info("Could not Generate Sku Sheet ::::::::", e);
                    }
                    List<RequestOrderItem> itemList = getRequestOrderItems(requestOrders);
                    String sheetName = "PRODUCT_LIST" + SCMUtil.getCurrentTimeISTStringWithNoColons();
                    Sheet sheet = getSheetForWorkBook(workbook, sheetName);
                    generateSheet(workbook, sheet, itemList, productPackagings, null,2);
                    String commentSheetName = "COMMENT_SHEET";
                    Sheet commentsSheet = getSheetForWorkBook(workbook, commentSheetName);
                    generateCommentSheet(workbook,commentsSheet,requestOrders);
                }

                /*
                 * for (RequestOrder requestOrder : requestOrders) { HSSFSheet sheetForUnit =
                 * getSheetForWorkBook(workbook, requestOrder.getRequestUnit().getName() + "-" +
                 * requestOrder.getId()); HSSFRow aRow = sheetForUnit.createRow(0);
                 * aRow.createCell(0).setCellValue("Request ID: " + requestOrder.getId());
                 * aRow.createCell(1).setCellValue("Requesting Unit: " +
                 * requestOrder.getRequestUnit().getName()); sheetForUnit.createRow(1);
                 * generateSheet(workbook, sheetForUnit, requestOrder.getRequestOrderItems(),
                 * productPackagings, requestOrder.getRequestUnit().getName()); }
                 */

                if (event != null) {
                    for (ProductionItemType type : ProductionItemType.values()) {
                        generatePlanningConsumptionSheet(workbook, event, type);
                    }
                }
                int sheetNum = workbook.getNumberOfSheets();
                if(requestOrders.size()>1){
                    workbook.setSheetOrder("UNIT_REFERENCE_SHEET",sheetNum-1);
                    workbook.setSheetOrder("COMMENT_SHEET",sheetNum-2);
                }

            }

        };
    }

    private void generateExcessSheet(Workbook workbook, List<RequestOrder> requestOrders, ProductionPlanEvent event) {
        Sheet sheet = getSheetForWorkBook(workbook, "Excess Sheet");
        sheet.setDefaultColumnWidth(20);
        CellStyle style = generateHeaderStyle(workbook);
        Row unit = sheet.createRow(0);
        unit.createCell(0).setCellValue("Unit");
        unit.createCell(1).setCellValue(scmCache.getUnitDetail(event.getUnitId()).getUnitName());
        unit.createCell(2).setCellValue("Prepared By");
        unit.createCell(3).setCellValue(event.getGeneratedBy().getName());

        Row pDate = sheet.createRow(1);
        pDate.createCell(0).setCellValue("Production Date");
        pDate.createCell(1).setCellValue(new SimpleDateFormat("yyyy-MM-dd").format(event.getGenerationTime()));
        pDate.createCell(2).setCellValue("Generated At");
        pDate.createCell(3).setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(event.getGenerationTime()));

        Row pNo = sheet.createRow(2);
        pNo.createCell(0).setCellValue("Production Plan Number");
        pNo.createCell(1).setCellValue(event.getId());
        pNo.createCell(2).setCellValue("Fulfilment Date");
        pNo.createCell(3).setCellValue(new SimpleDateFormat("yyyy-MM-dd ").format(event.getFulfillmentDate()));
        Row excess = sheet.createRow(3);
        excess.createCell(0).setCellValue("Excess Quantity Sheet");

        Set<IdCodeName> units = new TreeSet<IdCodeName>(new Comparator<IdCodeName>() {
            @Override
            public int compare(IdCodeName o1, IdCodeName o2) {
                return o1.getName().compareTo(o2.getName());
            }
        });
         Set<IdCodeName> semiFinishedproducts = new TreeSet<IdCodeName>(new Comparator<IdCodeName>() {
            @Override
            public int compare(IdCodeName o1, IdCodeName o2) {
                return o1.getName().compareTo(o2.getName());
            }
        });

        Map<Integer, Map<Integer, BigDecimal>> unitProductQty = new HashMap<>();

        for (RequestOrder ro : requestOrders) {
            units.add(ro.getRequestUnit());
            for (RequestOrderItem item : ro.getRequestOrderItems()) {
                if (SCMServiceConstants.SCM_CONSTANT_YES.equals(SCMUtil.setStatus(scmCache.getProductDefinition(item.getProductId()).isRecipeRequired()))) {
                    semiFinishedproducts.add(new IdCodeName(item.getProductId(), item.getUnitOfMeasure(), item.getProductName()));
                }
                mapOfExcess(unitProductQty, item, ro.getRequestUnit());
            }
        }

        List<String> unitShortCode=new ArrayList<>();
            for(IdCodeName d: units){
                UnitDetail detail=scmCache.getUnitDetail(d.getId());
                if(detail.getShortCode()!=null){
                    unitShortCode.add(detail.getShortCode());
                }else {
                    unitShortCode.add(detail.getUnitName());
                }
            }

        Row header = sheet.createRow(5);
        sheet.setDefaultColumnWidth(15);
        header.createCell(0).setCellValue("Product Name");
        header.getCell(0).setCellStyle(style);
        header.createCell(1).setCellValue("UOM");
        header.getCell(1).setCellStyle(style);
        int i = 2;
        for (String s : unitShortCode) {
            header.createCell(i).setCellValue(s);
            header.getCell(i).setCellStyle(style);
            i++;
        }
        header.createCell(i).setCellValue("Total");
        header.getCell(i).setCellStyle(style);

        int rowCount=5;

        rowCount++;
        int j = 2;
        BigDecimal overAllCount = BigDecimal.ZERO;
        for (IdCodeName p : semiFinishedproducts) {
            Row aRow = sheet.createRow(rowCount++);
            aRow.createCell(0).setCellValue(p.getName());
            aRow.createCell(1).setCellValue(p.getCode());
            j = 2;
            BigDecimal total = BigDecimal.ZERO;
            for (IdCodeName u : units) {
                BigDecimal qty = unitProductQty.get(u.getId()).get(p.getId());
                total = total.add(qty != null ? qty : BigDecimal.ZERO);
                    aRow.createCell(j).setCellValue(qty != null ? qty.doubleValue() : BigDecimal.ZERO.doubleValue());
                    j++;
            }
            overAllCount=overAllCount.add(total);
            aRow.createCell(j).setCellValue(total.doubleValue());
        }
        Row aRow = sheet.createRow(rowCount++);
        aRow.createCell(units.size()+2).setCellValue(overAllCount.doubleValue());
    }

    private void mapOfExcess(Map<Integer, Map<Integer, BigDecimal>> unitProductQty, RequestOrderItem item, IdCodeName requestUnit) {

        Map<Integer, BigDecimal> map = unitProductQty.get(requestUnit.getId());
        if (map == null) {
            map = new HashMap<Integer, BigDecimal>();
            unitProductQty.put(requestUnit.getId(), map);
        }
        BigDecimal qty = map.get(item.getProductId());
        if (qty == null) {
            qty = BigDecimal.ZERO;
        }
        qty = qty.add(item.getExcessQuantity());
        map.put(item.getProductId(), qty);
    }

    private void generatePivotSheet(Workbook workbook, List<RequestOrder> requestOrders,
                                    ProductionPlanEvent event) {

        Set<UnitBasicDetail> unitDetails = new TreeSet<UnitBasicDetail>(new Comparator<UnitBasicDetail>(){
            @Override
            public int compare(UnitBasicDetail u1 , UnitBasicDetail u2){
                int ret = u1.getCity().compareTo(u2.getCity());
                if (ret == 0) {
                    ret = u1.getName().compareTo(u2.getName());
                }
                return ret;
            }
        });

        Set<IdCodeName> semiFinishedproducts = new TreeSet<IdCodeName>(new Comparator<IdCodeName>() {
            @Override
            public int compare(IdCodeName o1, IdCodeName o2) {
                return o1.getName().compareTo(o2.getName());
            }
        });

        Set<IdCodeName> products = new TreeSet<IdCodeName>(new Comparator<IdCodeName>() {
            @Override
            public int compare(IdCodeName o1, IdCodeName o2) {
                return o1.getName().compareTo(o2.getName());
            }
        });


        Map<Integer, Map<Integer, BigDecimal>> unitProductQty = new HashMap<>();



        for (RequestOrder ro : requestOrders) {
            unitDetails.add(dataCache.getUnitBasicDetail(ro.getRequestUnit().getId()));
            for (RequestOrderItem item : ro.getRequestOrderItems()) {
                if (SCMServiceConstants.SCM_CONSTANT_YES.equals(SCMUtil.setStatus(scmCache.getProductDefinition(item.getProductId()).isRecipeRequired()))) {
                    semiFinishedproducts.add(new IdCodeName(item.getProductId(), item.getUnitOfMeasure(), item.getProductName()));
                } else {
                    products.add(new IdCodeName(item.getProductId(), item.getUnitOfMeasure(), item.getProductName()));
                }
                addToMap(unitProductQty, item, ro.getRequestUnit());
            }
        }

        Map<Integer,BigDecimal> mapOfProductIdAndQuantity=new HashMap<>();
        creatMapOfProductIdBufferQuantity(mapOfProductIdAndQuantity,event.getRequestItems());
        Map<String, List<Integer>> mapOfProductionLine = productionLine(event);
        mapOfProductionLine.forEach((productionUnit, data) -> {
            Set<UnitBasicDetail> unitFilter = filterUnits(unitDetails, data, unitProductQty);
            if (!unitFilter.isEmpty()) {
            Boolean productionSheet = false;
            Sheet sheet = getSheetForWorkBook(workbook, productionUnit);
            sheet.setDefaultColumnWidth(15);
            CellStyle style = generateHeaderStyle(workbook);
            Map<String,Set<ProductDefinition> > productsFilter = filterProducts(products, data, unitProductQty);
            Map<String,Set<ProductDefinition> > semiFinishedProductsFilter=filterProducts(semiFinishedproducts,data,unitProductQty);
            Map<Integer,String> unitShortCode=new HashMap<>();
            for(UnitBasicDetail d: unitFilter){
                UnitDetail detail=scmCache.getUnitDetail(d.getId());
                if(detail.getShortCode()!=null){
                    unitShortCode.put(d.getId(),detail.getShortCode());
                }else {
                    unitShortCode.put(d.getId(),detail.getUnitName());
                }
            }
            LOG.info("data of short code{}",unitShortCode);
                AtomicInteger sum = new AtomicInteger();
                semiFinishedProductsFilter.forEach((category, product) -> {
                    sum.addAndGet(product.size());
                });
            sheetMetadata(sheet, event, unitFilter, productionUnit);
            // SEMI-FINISHED
            productPivot(sheet, style, unitFilter, semiFinishedProductsFilter, unitProductQty, 5, unitShortCode, productionSheet, data,mapOfProductIdAndQuantity);

            // others
            productPivot(sheet, style, unitFilter, productsFilter, unitProductQty, 13 + sum.intValue(), unitShortCode, productionSheet, data,mapOfProductIdAndQuantity);

            //productionSheet
            String sheetName = productionUnit + " Production";
            Sheet sheet2 = getSheetForWorkBook(workbook, sheetName);
            sheet2.setDefaultColumnWidth(15);
            sheetMetadata(sheet2, event, unitFilter, productionUnit);

            productionSheet = true;
            // SEMI-FINISHED
            productPivot(sheet2, style, unitFilter, semiFinishedProductsFilter, unitProductQty, 5, unitShortCode, productionSheet, data,mapOfProductIdAndQuantity);
            // others
            productPivot(sheet2, style, unitFilter, productsFilter, unitProductQty,  13 + sum.intValue(), unitShortCode, productionSheet, data,mapOfProductIdAndQuantity);
            }
        });
        Sheet referenceSheet = getSheetForWorkBook(workbook, "UNIT_REFERENCE_SHEET");
        addHeadersForReferenceSheet(referenceSheet);
        fillReferenceSheet(unitDetails,referenceSheet);



    }


    void fillReferenceSheet(Set<UnitBasicDetail> units,Sheet sheet){
       List<UnitBasicDetail> unitsSorted = units.stream().sorted(new Comparator<UnitBasicDetail>(){
            @Override
            public int compare(UnitBasicDetail u1 , UnitBasicDetail u2){
                return  u1.getName().compareTo(u2.getName());
            }
        }).collect(Collectors.toList());
        int rowNum = 1;
        for(UnitBasicDetail unit : unitsSorted){
            Row unitRow = sheet.createRow(rowNum);
            UnitDetail ud = scmCache.getUnitDetail(unit.getId());
            unitRow.createCell(0).setCellValue(unit.getId());
            unitRow.createCell(1).setCellValue(unit.getName());
            unitRow.createCell(2).setCellValue(ud.getShortCode());
            rowNum++;
        }
        for(int i = 0;i<3;i++){
            sheet.autoSizeColumn(i);
        }

    }

    void addHeadersForReferenceSheet(Sheet sheet){
        CellStyle style = generateHeaderStyle(sheet.getWorkbook());
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue("UNIT ID");
        row.getCell(0).setCellStyle(style);
        row.createCell(1).setCellValue("UNIT NAME");
        row.getCell(1).setCellStyle(style);
        row.createCell(2).setCellValue("SHORT CODE");
        row.getCell(2).setCellStyle(style);
    }

    private void creatMapOfProductIdBufferQuantity(Map<Integer, BigDecimal> mapOfProductIdAndQuantity, List<PlanOrderItem> requestItems) {
        for (PlanOrderItem item : requestItems) {
            if (!mapOfProductIdAndQuantity.containsKey(item.getProductId())) {
                mapOfProductIdAndQuantity.put(item.getProductId(), item.getTotalQuantity());
            }
        }
    }

    private Map<String, Set<ProductDefinition>> filterProducts(Set<IdCodeName> products, List<Integer> data, Map<Integer, Map<Integer, BigDecimal>> unitProductQty) {
        Set<ProductDefinition> filterList = new TreeSet<ProductDefinition>(new Comparator<ProductDefinition>() {
            @Override
            public int compare(ProductDefinition p1, ProductDefinition p2) {
                return p1.getProductName().compareTo(p2.getProductName());
            }
        });



        Map<String, Set<ProductDefinition>> filterBySUbCategory = new TreeMap<>(new Comparator<String>() {
            @Override
            public int compare(String s1, String s2) {
                return s1.compareTo(s2);
            }
        });
        for (Integer p : data) {
            for (IdCodeName pd : products) {
                if (p.equals(pd.getId())) {
                    ProductDefinition productDefinition = scmCache.getProductDefinition(pd.getId());
                    filterList.add(productDefinition);
                }
            }
        }
        for (ProductDefinition pd : filterList) {
            //ProductDefinition pd = scmCache.getProductDefinition(p.getId());
            /*if (pd.isRecipeRequired() && pd.getClassificationId() != null) {
                ListDetailData ldd = dao.find(ListDetailData.class, pd.getClassificationId());*/

                if (!filterBySUbCategory.containsKey(pd.getSubCategoryDefinition().getName())) {
                    filterBySUbCategory.put(pd.getSubCategoryDefinition().getName(), new TreeSet<ProductDefinition>(new Comparator<ProductDefinition>() {
                        @Override
                        public int compare(ProductDefinition p1, ProductDefinition p2) {
                               return p1.getProductName().compareTo(p2.getProductName());
                        }
                    }));
                }
                filterBySUbCategory.get(pd.getSubCategoryDefinition().getName()).add(pd);
        }
        return filterBySUbCategory;
    }

    private Set<UnitBasicDetail> filterUnits(Set<UnitBasicDetail> units, List<Integer> data,
                                        Map<Integer, Map<Integer, BigDecimal>> unitProductQty) {
        Set<UnitBasicDetail> newUnit = new TreeSet<UnitBasicDetail>(new Comparator<UnitBasicDetail>() {

            @Override
            public int compare(UnitBasicDetail u1 , UnitBasicDetail u2){
                int ret = u1.getCity().compareTo(u2.getCity());
                if (ret == 0) {
                    ret = u1.getName().compareTo(u2.getName());
                }
                return ret;
            }
        });
        for (UnitBasicDetail unit : units) {
            LOG.info("unit id : {} , {}",unit.getId() , unit.get_id());
            for (Integer p : data) {
                LOG.info("unit product : {}",unitProductQty.keySet());
                if (unitProductQty.get(unit.getId()).containsKey(p)) {
                    newUnit.add(unit);
                }
            }
        }
        return newUnit;
    }

    private Map<String, List<Integer>> productionLine(ProductionPlanEvent event) {
        List<PlanOrderItem> requestItemsIds = event.getRequestItems();
        Map<String, List<Integer>> productionWiseProductList = new HashMap<>();
        productionWiseProductList.put(SCMServiceConstants.PRODUCTION_LINE, new ArrayList<>());
        List<ProductionUnitData> productionUnitData=dao.findAll(ProductionUnitData.class);
        Map<Integer, String>  productionUnitMap=getProductionUnitId(productionUnitData);
        Boolean isWarehouse =   SCMUtil.isWareHouse(scmCache.getUnitDetail(event.getUnitId()));
        for (PlanOrderItem data : requestItemsIds) {
            String productionUnit=productionUnitMap.get(data.getProductionUnit());
            if (( Objects.isNull(data.getProductionUnit()) || (isWarehouse && productionUnit.equals("Chaayos"))) || (!productionUnitMap.containsKey(data.getProductionUnit()))) {
                productionWiseProductList.get(SCMServiceConstants.PRODUCTION_LINE).add(data.getProductId());
            } else {
                if (!productionWiseProductList.containsKey(productionUnit)) {
                    productionWiseProductList.put(productionUnit, new ArrayList<>());
                }
                productionWiseProductList.get(productionUnit).add(data.getProductId());
            }
        }
        return productionWiseProductList;

    }
    private  Map<Integer, String> getProductionUnitId(List<ProductionUnitData> productionUnitData){
        Map<Integer, String> productionUnitMap = new HashMap<>();
        for(ProductionUnitData productionData:productionUnitData){
            productionUnitMap.put(productionData.getId(),productionData.getProductionUnitName());
        }
        return productionUnitMap;
    }


    private void sheetMetadata(Sheet sheet,  ProductionPlanEvent event,  Set<UnitBasicDetail> units,String productionLine){
        CellStyle leftAlign  = sheet.getWorkbook().createCellStyle();
        leftAlign.setAlignment(HorizontalAlignment.LEFT);
        // Metadata
        Row unit = sheet.createRow(0);
        unit.createCell(1).setCellValue("Unit");
        unit.createCell(2).setCellValue(scmCache.getUnitDetail(event.getUnitId()).getUnitName());
        unit.createCell(3).setCellValue("Prepared By");
        unit.createCell(4).setCellValue(event.getGeneratedBy().getName());

        Row pDate = sheet.createRow(1);
        pDate.createCell(1).setCellValue("Production Date");
        pDate.createCell(2).setCellValue(new SimpleDateFormat("yyyy-MM-dd").format(event.getGenerationTime()));
        pDate.createCell(3).setCellValue("Generated At");
        pDate.createCell(4).setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(event.getGenerationTime()));


        Row pNo = sheet.createRow(2);
        pNo.createCell(1).setCellValue("Production Plan Number");
        pNo.createCell(2).setCellValue(event.getId());
        pNo.getCell(2).setCellStyle(leftAlign);
        pNo.createCell(3).setCellValue("Total Units Today");
        pNo.createCell(4).setCellValue(units.size());
        pNo.getCell(4).setCellStyle(leftAlign);


//        HSSFRow pBy = sheet.createRow(3);
//        pBy.createCell(0).setCellValue("Prepared By");
//        pBy.createCell(1).setCellValue(event.getGeneratedBy().getName());

//        HSSFRow g = sheet.createRow(4);
//        g.createCell(0).setCellValue("Generated At");
//        g.createCell(1).setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(event.getGenerationTime()));

//        HSSFRow tut = sheet.createRow(5);
//        tut.createCell(0).setCellValue("Total Units Today");
//        tut.createCell(1).setCellValue(units.size());

        Row prodLine = sheet.createRow(3);
        prodLine.createCell(1).setCellValue("Production Line ");
        prodLine.createCell(2).setCellValue(productionLine);
        prodLine.createCell(3).setCellValue("Fulfillment Date");
        prodLine.createCell(4).setCellValue(new SimpleDateFormat("yyyy-MM-dd").format(event.getFulfillmentDate()));

        sheet.createRow(4);
        sheet.createRow(5);
    }

    private  void addBorder(Sheet sheet,Integer firstRow , Integer lastRow , Integer firstCol , Integer lastcol , Workbook workbook){
        CellRangeAddress region = new CellRangeAddress(firstRow, lastRow, firstCol, lastcol);
        RegionUtil.setBorderTop(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet);
    }

    private void productPivot(Sheet sheet, CellStyle style, Set<UnitBasicDetail> units, Map<String,Set<ProductDefinition> > productsDate,
                              Map<Integer, Map<Integer, BigDecimal>> unitProductQty, int rowCount,Map<Integer,String> unitShortCode,
                              Boolean productionSheet, List<Integer> unitIds, Map<Integer,BigDecimal> mapOfProductIdAndQuantity) {
        Set<String> cities = new TreeSet<>();



        //Green Header
        CellStyle greenStyle = sheet.getWorkbook().createCellStyle();
        Font font = sheet.getWorkbook().createFont();
        font.setFontName("Arial");
        font.setColor(HSSFColor.HSSFColorPredefined.WHITE.getIndex());
        greenStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.GREEN.getIndex());
        greenStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        greenStyle.setFont(font);

        addheader(sheet, style, greenStyle,units, rowCount,unitShortCode, productionSheet,cities);

        Workbook workbook = sheet.getWorkbook();
        CellStyle rightAligned = workbook.createCellStyle();
        rightAligned.setAlignment(HorizontalAlignment.RIGHT);

        Map<Integer, String> categoryMap = new HashMap<>();

        rowCount = rowCount+3;
        rowCount++;
        AtomicInteger i = new AtomicInteger(2);
        AtomicInteger rCount = new AtomicInteger(rowCount);
        AtomicReference<Integer> firstRow = new AtomicReference<>(rCount.get() -1);
        AtomicReference<Boolean> oneTime= new AtomicReference<>(false);
        final BigDecimal[] overTotalCount = {BigDecimal.ZERO};
        AtomicInteger sNo = new AtomicInteger(0);
        productsDate.forEach((subCategory,products )-> {
            oneTime.set(true);
            for (ProductDefinition p : products) {
                Map<String,BigDecimal> cityTotals = new HashMap<>();
                sNo.getAndIncrement();
                int x=rCount.intValue();
                rCount.getAndIncrement();
                Row aRow = sheet.createRow(x);
                aRow.createCell(0).setCellValue(sNo.get());
               if(p.isRecipeRequired() && Objects.nonNull(p.getClassificationId())) {
                      if(!categoryMap.containsKey(p.getClassificationId())){
                        ListDetailData ld = dao.find(ListDetailData.class,p.getClassificationId());
                          categoryMap.put(p.getClassificationId(),ld.getName());
                      }
                      aRow.createCell(1).setCellValue(categoryMap.get(p.getClassificationId()));
               }
                if(oneTime.compareAndSet(true,false)) {
                    aRow.createCell(2).setCellValue(subCategory);
                }
                oneTime.set(false);
                aRow.createCell(3).setCellValue(p.getProductName());
                aRow.createCell(4).setCellValue(p.getUnitOfMeasure());
                i.set(5);
                BigDecimal total = BigDecimal.ZERO;
                for (UnitBasicDetail u : units) {
                    BigDecimal qty = unitProductQty.get(u.getId()).get(p.getProductId());
                    if(qty!=null){
                        qty=qty.setScale(2,BigDecimal.ROUND_UP);
                    }
                    total = total.add(qty != null ? qty : BigDecimal.ZERO);
                    if(!cityTotals.containsKey(u.getCity())){
                        cityTotals.put(u.getCity(),BigDecimal.ZERO);
                    }
                    cityTotals.put(u.getCity(),cityTotals.get(u.getCity()).add(qty!=null ? qty : BigDecimal.ZERO));
                    if (!productionSheet) {
                        if(qty!=null){
                            aRow.createCell(i.get()).setCellValue(qty.doubleValue());
                        }
                        else{
                            aRow.createCell(i.get(), STRING).setCellValue("-");
                            aRow.getCell(i.get()).setCellStyle(rightAligned);
                        }
                        i.getAndIncrement();
                    }
                }
                if(productionSheet){
                    total=mapOfProductIdAndQuantity.get(p.getProductId());
                    aRow.createCell(i.get()).setCellValue(total.doubleValue());
                }else {
                    aRow.createCell(i.get()).setCellValue(total.doubleValue());

                    for(String city : cities){
                        i.getAndIncrement();
                        Double cityTotal = cityTotals.get(city).doubleValue();
                        if(cityTotal.equals(Double.valueOf(0))){
                            aRow.createCell(i.get(), STRING).setCellValue("-");
                            aRow.getCell(i.get()).setCellStyle(rightAligned);
                        }
                        else{
                            aRow.createCell(i.get()).setCellValue(cityTotal);
                        }
                    }

                }
                overTotalCount[0] = overTotalCount[0].add(total);
            }
            Integer lastRow = rCount.get()+2;

            addBorder(sheet, firstRow.get(),lastRow,0,i.get(),workbook);

        });
        int y=rCount.intValue();
        y++;
        Row aRow = sheet.createRow(y);
        if(productionSheet){
            if(overTotalCount[0].doubleValue()>0) {
                aRow.createCell(5).setCellValue(overTotalCount[0].doubleValue());
                aRow.getCell(5).setCellStyle(greenStyle);
                CellRangeAddress region = new CellRangeAddress(y, y, 0, 5);
                RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet);
                RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet);
            }
        }else {
            if(overTotalCount[0].doubleValue()>0) {
                aRow.createCell(units.size() + 5).setCellValue(overTotalCount[0].doubleValue());
                aRow.getCell(units.size()+5).setCellStyle(greenStyle);
                CellRangeAddress region = new CellRangeAddress(y, y, 0, units.size() + cities.size() +  5);
                RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet);
                RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet);
            }
        }
        overTotalCount[0]=BigDecimal.ZERO;
        sheet.autoSizeColumn(0);

    }
//    private void addheader(Sheet sheet, CellStyle style, Set<IdCodeName> units, int headerRow, List<String> unitShortCode, Boolean productionSheet) {
//        Row header = sheet.createRow(headerRow);
//        sheet.setDefaultColumnWidth(15);
//        header.createCell(0).setCellValue("CATEGORY");
//        header.getCell(0).setCellStyle(style);
//        header.createCell(1).setCellValue("Product Name");
//        header.getCell(1).setCellStyle(style);
//        header.createCell(2).setCellValue("UOM");
//        header.getCell(2).setCellStyle(style);
//        int i = 3;
//        if(!productionSheet) {
//            for (String s : unitShortCode) {
//                header.createCell(i).setCellValue(s);
//                header.getCell(i).setCellStyle(style);
//                i++;
//            }
//        }
//        header.createCell(i).setCellValue("Total");
//        header.getCell(i).setCellStyle(style);
//    }



    private void addheader(Sheet sheet, CellStyle style,CellStyle greenStyle, Set<UnitBasicDetail> units, int headerRow, Map<Integer,String> unitShortCode, Boolean productionSheet , Set<String> cities) {
        Row row1 = sheet.createRow(headerRow);
        headerRow++;
        Row row2 = sheet.createRow(headerRow);
        headerRow++;
        Row row3 = sheet.createRow(headerRow);
        headerRow++;

        CellStyle greyStyle = sheet.getWorkbook().createCellStyle();
        greyStyle.setAlignment(HorizontalAlignment.CENTER);
        Font font = sheet.getWorkbook().createFont();
        font.setFontName("Arial");
        font.setBold(true);
        greyStyle.setFont(font);
        greyStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.getIndex());
        greyStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        Row header = sheet.createRow(headerRow);
        header.createCell(0).setCellValue("SN");
        header.getCell(0).setCellStyle(style);
        header.createCell(1).setCellValue("CATEGORY");
        header.getCell(1).setCellStyle(style);
        header.createCell(2).setCellValue("SUB CATEGORY");
        header.getCell(2).setCellStyle(style);
        header.createCell(3).setCellValue("Product Name");
        header.getCell(3).setCellStyle(style);
        header.createCell(4).setCellValue("UOM");
        header.getCell(4).setCellStyle(style);
        int i = 5;
        if(!productionSheet) {
            row1.createCell(i-1).setCellValue("State");
            row1.getCell(i-1).setCellStyle(greyStyle);
            row2.createCell(i-1).setCellValue("City");
            row2.getCell(i-1).setCellStyle(greyStyle);
            row3.createCell(i-1).setCellValue("Unit");
            row3.getCell(i-1).setCellStyle(greyStyle);
            for(UnitBasicDetail unit : units){
                row1.createCell(i).setCellValue(unit.getState());
                row2.createCell(i).setCellValue(unit.getCity());
                row3.createCell(i).setCellValue(unit.getName());
                header.createCell(i).setCellValue(unitShortCode.get(unit.getId()));
                header.getCell(i).setCellStyle(style);
                i++;
            }
        }
        header.createCell(i).setCellValue("Total");
        header.getCell(i).setCellStyle(greenStyle);

        if(!productionSheet){
            for(UnitBasicDetail u : units){
                if(!cities.contains(u.getCity())){
                    i++;
                    header.createCell(i).setCellValue(u.getCity());
                    header.getCell(i).setCellStyle(style);
                }
                cities.add(u.getCity());

            }
        }

    }

    private void addToMap(Map<Integer, Map<Integer, BigDecimal>> unitProductQty, RequestOrderItem item,
                          IdCodeName requestUnit) {
        Map<Integer, BigDecimal> map = unitProductQty.get(requestUnit.getId());
        if (map == null) {
            map = new HashMap<Integer, BigDecimal>();
            unitProductQty.put(requestUnit.getId(), map);
        }
        BigDecimal qty = map.get(item.getProductId());
        if (qty == null) {
            qty = BigDecimal.ZERO;
        }
        qty = qty.add(new BigDecimal(item.getRequestedQuantity()));
        map.put(item.getProductId(), qty);
    }

    private void generatePlanningConsumptionSheet(Workbook workbook, ProductionPlanEvent event,
                                                  ProductionItemType type) {
        Sheet sheet = getSheetForWorkBook(workbook, "Planning Event -" + event.getId() + "-" + type.name());
        sheet.setDefaultColumnWidth(20);
        CellStyle style = generateHeaderStyle(workbook);
        Row header = sheet.createRow(0);
        header.createCell(0).setCellValue("Product ID");
        header.getCell(0).setCellStyle(style);
        header.createCell(1).setCellValue("Product Name");
        header.getCell(1).setCellStyle(style);
        header.createCell(2).setCellValue("Unit of Measure");
        header.getCell(2).setCellStyle(style);
        header.createCell(3).setCellValue("Category");
        header.getCell(3).setCellStyle(style);
        header.createCell(4).setCellValue("Item Type");
        header.getCell(4).setCellStyle(style);
        header.createCell(5).setCellValue("Requested Qty");
        header.getCell(5).setCellStyle(style);
        header.createCell(6).setCellValue("Available Qty");
        header.getCell(6).setCellStyle(style);
        header.createCell(7).setCellValue("To-Be Procured Qty");
        header.getCell(7).setCellStyle(style);
        header.createCell(8).setCellValue("Unit Price");
        header.getCell(8).setCellStyle(style);
        header.createCell(9).setCellValue("Amount");
        header.getCell(9).setCellStyle(style);

        int rowCount = 1;
        for (PlanOrderItem item : event.getRequestItems()) {
            if (type.name().equals(item.getItemType())) {
                Row aRow = sheet.createRow(rowCount++);
                aRow.createCell(0).setCellValue(item.getProductId());
                aRow.createCell(1).setCellValue(item.getProductName());
                aRow.createCell(2).setCellValue(item.getUnitOfMeasure());
                aRow.createCell(3).setCellValue(item.getCategory());
                aRow.createCell(4).setCellValue(item.getItemType());
                aRow.createCell(5).setCellValue(SCMUtil.convertToBigDecimal(item.getTotalQuantity().floatValue()).setScale(2,BigDecimal.ROUND_UP).doubleValue());
                aRow.createCell(6).setCellValue(item.getAvailableQuantity().floatValue());
                BigDecimal toBeProcured = BigDecimal.ZERO;
                if (item.getAvailableQuantity().compareTo(item.getTotalQuantity()) < 0) {
                    toBeProcured = item.getAvailableQuantity().subtract(item.getTotalQuantity());
                }
                aRow.createCell(7).setCellValue(SCMUtil.convertToBigDecimal(toBeProcured.floatValue()).setScale(2,BigDecimal.ROUND_UP).doubleValue());
                aRow.createCell(8).setCellValue(item.getUnitPrice().floatValue());
                aRow.createCell(9).setCellValue(item.getAmount().floatValue());
            }
        }
    }

    private HSSFSheet getSheetForWorkBook(HSSFWorkbook workbook, String sheetName) {
        int index = workbook.getSheetIndex(sheetName);
        if (index != -1) {
            workbook.removeSheetAt(index);
        }
        return workbook.createSheet(sheetName);
    }

    private Sheet getSheetForWorkBook(Workbook workbook, String sheetName) {
        int index = workbook.getSheetIndex(sheetName);
        if (index != -1) {
            workbook.removeSheetAt(index);
        }
        return workbook.createSheet(sheetName);
    }

    private Map<Integer,GoodsReceivedItem> createSkuQuantityMap(PlanOrderItemPrepItem planItem , Map<Integer,GoodsReceivedItem> skuQuantityMap
    ,Map<Integer, Integer> productToSkuMap){
        Integer key = null;
        Integer skuId = null;
        if (productToSkuMap.containsKey(planItem.getProductId())) {
            key = productToSkuMap.get(planItem.getProductId());
            skuId = key;
        } else {
            key = planItem.getProductId();
        }
        if (!skuQuantityMap.containsKey(key)) {
            GoodsReceivedItem grItem = new GoodsReceivedItem();
            grItem.setProductId(planItem.getProductId());
            if (Objects.nonNull(skuId)) {
                SkuDefinition skuDefinition = scmCache.getSkuDefinition(skuId);
                grItem.setSkuId(skuId);
                grItem.setSkuName(skuDefinition.getSkuName());
            } else {
                grItem.setSkuId(-1);
                grItem.setSkuName("No Mapping Found !!");
            }
            grItem.setTransferredQuantity(planItem.getQuantity().floatValue());
            grItem.setUnitOfMeasure(planItem.getUnitOfMeasure());
            skuQuantityMap.put(key, grItem);
        } else {
            GoodsReceivedItem grItem = skuQuantityMap.get(key);
            grItem.setTransferredQuantity(grItem.getTransferredQuantity() + planItem.getQuantity().floatValue());
            skuQuantityMap.put(key, grItem);
        }
        if(Objects.nonNull(planItem.getPlanOrderItemPrepItems()) && !planItem.getPlanOrderItemPrepItems().isEmpty()){
            for(PlanOrderItemPrepItem item : planItem.getPlanOrderItemPrepItems()){
                skuQuantityMap = createSkuQuantityMap(item,skuQuantityMap,productToSkuMap);
            }
        }

        return skuQuantityMap;
    }

    private void generateSkuSheet(Workbook workbook , List<RequestOrder> requestOrders ) throws DataNotFoundException, SumoException {
        Sheet sheet =  getSheetForWorkBook(workbook, "skuList");
        sheet.setDefaultColumnWidth(20);
        CellStyle style = generateHeaderStyle(workbook);
        AtomicInteger rowCount = new AtomicInteger(1);
        Map<Integer,RequestOrderItem> roItemMap = new HashMap<>();
        for (RequestOrder rq : requestOrders) {
            for (RequestOrderItem item : rq.getRequestOrderItems()) {
                ProductDefinition product = scmCache.getProductDefinition(item.getProductId());
                if (product.getCategoryDefinition().getId().equals(SCMServiceConstants.CATEGORY_SEMI_FINISHED) && product.isRecipeRequired()) {
                          if(!roItemMap.containsKey(product.getProductId())){
                              item.setUnitId(rq.getFulfillmentUnit().getId());
                              roItemMap.put(product.getProductId(),item);
                          }else{
                              RequestOrderItem roItem = roItemMap.get(product.getProductId());
                              roItem.setRequestedQuantity(roItem.getRequestedQuantity() + item.getRequestedQuantity());
                              roItemMap.put(product.getProductId(),roItem);
                          }
                }
            }
        }

        Map<Integer,GoodsReceivedItem> skuQuantityMap = new HashMap<>();
        for (Integer productId : roItemMap.keySet()) {
            PlanOrderItemPrep planOrder = null;
            Map<Integer, Integer> productToSkuMap = new HashMap<>();
            try {
                planOrder = requestOrderManagementService.getPlanItemsForSemiFinishedProduct(roItemMap.get(productId), false);
                productToSkuMap = requestOrderManagementService.getProductionBookingProductSkuMap(productId, roItemMap.get(productId).getUnitId());
            } catch (Exception e) {
                LOG.info("error : ", e);
                continue;
            }
            for (PlanOrderItemPrepItem planItem : planOrder.getPlanOrderItemPrepItems()) {
                   skuQuantityMap = createSkuQuantityMap(planItem,skuQuantityMap,productToSkuMap);
            }
        }

        Row header = sheet.createRow(rowCount.getAndIncrement());
        header.createCell(0).setCellValue("Product ID");
        header.getCell(0).setCellStyle(style);
        header.createCell(1).setCellValue("Product Name");
        header.getCell(1).setCellStyle(style);
        header.createCell(2).setCellValue("SKU Id");
        header.getCell(2).setCellStyle(style);
        header.createCell(3).setCellValue("SKU Name");
        header.getCell(3).setCellStyle(style);
        header.createCell(4).setCellValue("Quantity");
        header.getCell(4).setCellStyle(style);
        header.createCell(5).setCellValue("Unit of Measure");
        header.getCell(5).setCellStyle(style);

        for(Integer key : skuQuantityMap.keySet()){
                    GoodsReceivedItem item = skuQuantityMap.get(key);
                    ProductDefinition productDefinition = scmCache.getProductDefinition(item.getProductId());
                    Row skuRow = sheet.createRow(rowCount.getAndIncrement());
                    skuRow.createCell(0).setCellValue(item.getProductId());
                    skuRow.createCell(1).setCellValue(productDefinition.getProductName());
                    skuRow.createCell(2).setCellValue(item.getSkuId());
                    skuRow.createCell(3).setCellValue(item.getSkuName());
                    skuRow.createCell(4).setCellValue(item.getTransferredQuantity());
                    skuRow.createCell(5).setCellValue(item.getUnitOfMeasure());
        }

    }

    private void generateUnitWiseSheet(Workbook workbook, List<RequestOrder> requestOrders,
                                       Map<Integer, List<ProductPackagingMapping>> productPackagings,boolean preview) {
        Sheet sheet = getSheetForWorkBook(workbook, "UnitWiseProducts");
        sheet.setDefaultColumnWidth(20);
        CellStyle style1 = generateCellStyle(workbook,"Orange");
        if(Objects.nonNull(preview) && preview) {
            Row headerPreview = sheet.createRow(0);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
            headerPreview.createCell(0).setCellValue("Preview Of Plan Orders");
            headerPreview.getCell(0).setCellStyle(style1);
        }
        CellStyle style = generateHeaderStyle(workbook);
        Row header = sheet.createRow(2);
        header.createCell(0).setCellValue("Product ID");
        header.getCell(0).setCellStyle(style);
        header.createCell(1).setCellValue("Product Name");
        header.getCell(1).setCellStyle(style);
        header.createCell(2).setCellValue("Unit of Measure");
        header.getCell(2).setCellStyle(style);
        header.createCell(3).setCellValue("Quantity");
        header.getCell(3).setCellStyle(style);
        header.createCell(4).setCellValue("Packaging Qty");
        header.getCell(4).setCellStyle(style);
        header.createCell(5).setCellValue("Packaging UOM");
        header.getCell(5).setCellStyle(style);
        header.createCell(6).setCellValue("Unit");
        header.getCell(6).setCellStyle(style);
        if(Objects.nonNull(preview) && preview){
            header.createCell(7).setCellValue("Request Order Id");
            header.getCell(7).setCellStyle(style);
        }

        int rowCount = 3;
        for (RequestOrder rq : requestOrders) {
            for (RequestOrderItem item : rq.getRequestOrderItems()) {
                Row aRow = sheet.createRow(rowCount++);
                aRow.createCell(0).setCellValue(item.getProductId());
                aRow.createCell(1).setCellValue(item.getProductName());
                aRow.createCell(2).setCellValue(item.getUnitOfMeasure());
                aRow.createCell(3).setCellValue(SCMUtil.convertToBigDecimal(item.getRequestedQuantity()).
                    setScale(2,BigDecimal.ROUND_UP).doubleValue());
                PackagingDefinition packaging = getDefaultPackaging(productPackagings.get(item.getProductId()));
                float conversionRatio = 1f;
                if (packaging != null) {
                    conversionRatio = packaging.getConversionRatio();
                }
                aRow.createCell(4).setCellValue(SCMUtil.convertToBigDecimal(item.getRequestedQuantity() / conversionRatio).setScale(2,BigDecimal.ROUND_UP).doubleValue());
                aRow.createCell(5).setCellValue(packaging != null ? packaging.getPackagingName() : "N/A");
                aRow.createCell(6).setCellValue(rq.getRequestUnit().getName());
                if(Objects.nonNull(preview) && preview){
                    aRow.createCell(7).setCellValue(rq.getId());
                }
            }
        }
    }

    private void generateRawMaterialWorkbook(Workbook workbook, Map<Integer, SCMProductItem> productMap) throws SumoException {

        Sheet sheet = getSheetForWorkBook(workbook, "ProductionPlanning");
        sheet.setDefaultColumnWidth(20);
        CellStyle style = generateHeaderStyle(workbook);
        Row header = sheet.createRow(0);
        header.createCell(0).setCellValue("Product ID");
        header.getCell(0).setCellStyle(style);
        header.createCell(1).setCellValue("Product Name");
        header.getCell(1).setCellStyle(style);
        header.createCell(2).setCellValue("Unit Of Measure");
        header.getCell(2).setCellStyle(style);
        header.createCell(3).setCellValue("Quantity");
        header.getCell(3).setCellStyle(style);
        header.createCell(4).setCellValue("Paths");
        header.getCell(4).setCellStyle(style);

        int rowCount = 2;

        for (Map.Entry<Integer, SCMProductItem> entry : productMap.entrySet()) {

            Row aRow = sheet.createRow(rowCount++);
            aRow.createCell(0).setCellValue(entry.getValue().getProductId());
            aRow.createCell(1).setCellValue(entry.getValue().getProductName());
            aRow.createCell(2).setCellValue(entry.getValue().getUom());
            aRow.createCell(3).setCellValue(SCMUtil.convertToBigDecimal(entry.getValue().getQuantity()).toString());
            for (int r = 0; r < entry.getValue().getPaths().size(); r++) {
                Row bRow = sheet.createRow(r + rowCount);
                bRow.createCell(4).setCellValue(entry.getValue().getPaths().get(r));
            }
            rowCount = rowCount + entry.getValue().getPaths().size()+1;
        }

    }


    private Sheet generateSheet(Workbook workbook, Sheet sheet, List<RequestOrderItem> itemList,
                                    Map<Integer, List<ProductPackagingMapping>> productPackagings, String unitName , Integer rowNum) {
        sheet.setDefaultColumnWidth(20);

        CellStyle style = generateHeaderStyle(workbook);
        Row header = sheet.createRow(rowNum);
        header.createCell(0).setCellValue("Product ID");
        header.getCell(0).setCellStyle(style);
        header.createCell(1).setCellValue("Product Name");
        header.getCell(1).setCellStyle(style);
        header.createCell(2).setCellValue("Unit of Measure");
        header.getCell(2).setCellStyle(style);
        header.createCell(3).setCellValue("Quantity");
        header.getCell(3).setCellStyle(style);
        header.createCell(4).setCellValue("Packaging Qty");
        header.getCell(4).setCellStyle(style);
        header.createCell(5).setCellValue("Packaging UOM");
        header.getCell(5).setCellStyle(style);
        if (unitName != null) {
            header.createCell(6).setCellValue("Unit");
            header.getCell(6).setCellStyle(style);
        }

        int rowCount = rowNum+1;
        for (RequestOrderItem item : itemList) {
            Row aRow = sheet.createRow(rowCount++);
            aRow.createCell(0).setCellValue(item.getProductId());
            aRow.createCell(1).setCellValue(item.getProductName());
            aRow.createCell(2).setCellValue(item.getUnitOfMeasure());
            aRow.createCell(3).setCellValue(SCMUtil.convertToBigDecimal(item.getRequestedAbsoluteQuantity()).setScale(2,BigDecimal.ROUND_UP).doubleValue());
            PackagingDefinition packaging = getDefaultPackaging(productPackagings.get(item.getProductId()));
            float conversionRatio = 1f;
            if (packaging != null) {
                conversionRatio = packaging.getConversionRatio();
            }
            aRow.createCell(4).setCellValue(SCMUtil.convertToBigDecimal(item.getRequestedAbsoluteQuantity() / conversionRatio).setScale(2,BigDecimal.ROUND_UP).doubleValue());
            aRow.createCell(5).setCellValue(packaging != null ? packaging.getPackagingName() : "N/A");
            if (unitName != null) {
                aRow.createCell(6).setCellValue(unitName);
            }
        }

        return sheet;
    }

    private PackagingDefinition getDefaultPackaging(List<ProductPackagingMapping> productPackagings) {
        Optional<ProductPackagingMapping> defaultPackaging = productPackagings.stream()
                .filter(productPackagingMapping -> productPackagingMapping.isIsDefault()).findFirst();
        if (defaultPackaging.isPresent()) {
            return scmCache.getPackagingDefinitions().get(defaultPackaging.get().getPackagingId());
        }
        return null;
    }

    public View getRequestOrderView(RequestOrder requestOrder) {
        Map<Integer, List<ProductPackagingMapping>> productPackagings = productService.viewAllProductPackagingMapping();
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Request Order Sheet -" + requestOrder.getRequestUnit().getName() + "-"
                        + requestOrder.getFulfillmentDate() + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);

                Sheet sheet = getSheetForWorkBook(workbook,
                        requestOrder.getRequestUnit().getName() + "_" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                Row aRow = sheet.createRow(0);
                aRow.createCell(0).setCellValue("Request ID: " + requestOrder.getId());
                aRow.createCell(1).setCellValue("Requesting Unit: " + requestOrder.getRequestUnit().getName());
                sheet.createRow(1);
                generateSheet(workbook, sheet, requestOrder.getRequestOrderItems(), productPackagings,
                        requestOrder.getRequestUnit().getName(),2);
            }
        };
    }

    private Map<String,List<RequestOrder>> getUnitToRoMap(List<RequestOrder> requestOrderList){
        Map<String,List<RequestOrder>> map = new TreeMap<>();
        for(RequestOrder requestOrder : requestOrderList){
            if(!map.containsKey(requestOrder.getRequestUnit().getName())){
                map.put(requestOrder.getRequestUnit().getName(),new ArrayList<>());
            }
            map.get(requestOrder.getRequestUnit().getName()).add(requestOrder);
        }
        return map;
    }

    public View getRequestOrdersView(List<RequestOrder> requestOrderList) {
        Map<Integer, List<ProductPackagingMapping>> productPackagings = productService.viewAllProductPackagingMapping();
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Request Orders Sheet -"  + "-"
                        + requestOrderList.get(0).getFulfillmentDate() + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);

                Map<String,List<RequestOrder>> unitToRO  = getUnitToRoMap(requestOrderList);

                for(String unit : unitToRO.keySet()){

                    Sheet sheet = getSheetForWorkBook(workbook,
                            unit + "_" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                    Integer rowNum = 0;
                    List<RequestOrder> requestOrders = unitToRO.get(unit);
                    for(RequestOrder requestOrder : requestOrders){
                        Row aRow = sheet.createRow(rowNum);
                        rowNum++;
                        aRow.createCell(0).setCellValue("Request ID: " + requestOrder.getId());
                        aRow.createCell(1).setCellValue("Requesting Unit: " + requestOrder.getRequestUnit().getName());
                        sheet.createRow(rowNum);
                        rowNum++;
                        generateSheet(workbook, sheet, requestOrder.getRequestOrderItems(), productPackagings,
                                requestOrder.getRequestUnit().getName(),rowNum);
                        rowNum = sheet.getLastRowNum() + 2;
                    }

                }

            }
        };
    }

    public View getTosView(List<TransferOrder> transferOrders,Map<Integer, SkuDefinition> skuDefinitionList,Map<Integer,ProductDefinition> productList,
            Map<Integer, SubCategoryDefinition> subCategoryDefinitionMap ,Boolean isBulkEvent){

        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {
                Integer bulkEventId = transferOrders.get(0).getBulkTransferEventId();
                String fileName = "Transfer_Order_Sheet" + SCMUtil.getCurrentTimeISTStringWithNoColons();
                if(isBulkEvent) {
                    fileName = "BULK_TO_EVENT_" + bulkEventId;
                }
                Map<Integer,TransferOrderItem> itemAggregateMap = getAggregatedItemMap(transferOrders);
                XSSFSheet transferSheet = (XSSFSheet) workbook.createSheet();
                List<String> columns = Arrays.asList("TO ID","GENERATED FOR UNIT","RO ID","CREATED BY","GENERATION TIME",
                        "SKU NAME","CATEGORY","UOM","REQ QTY","TRANSFERRED QTY","RECEIVED QTY" ,"TO STATUS","CAFE REMARK");
                workbook.setSheetName(0,fileName);

                if(isBulkEvent){
                    XSSFCellStyle headerStyle = (XSSFCellStyle) generateHeaderStyle(workbook);
                    XSSFRow bulkEventRow = transferSheet.createRow(0);
                    bulkEventRow.createCell(0).setCellValue("Bulk Event Id ");
                    bulkEventRow.getCell(0).setCellStyle(headerStyle);
                    bulkEventRow.createCell(1).setCellValue(bulkEventId);
                }

                createHeaderRow(workbook,transferSheet,2,columns);

                transferSheet.createFreezePane(0,3);
                fillTOsSheet(transferSheet,transferOrders,skuDefinitionList,productList,subCategoryDefinitionMap);
                addBorder(transferSheet,2,transferSheet.getLastRowNum(),0,12,workbook);


                //aggregate sheet
                List<String> aggregateColumns = Arrays.asList("SKU NAME","CATEGORY","UOM","REQ QTY","TRANSFERRED QTY","RECEIVED QTY" );
                XSSFSheet aggregateSheet = (XSSFSheet) workbook.createSheet("Aggregate Sheet");
                createHeaderRow(workbook,aggregateSheet,0,aggregateColumns);
                aggregateSheet.createFreezePane(0,1,0,1);
                fillTOSheet(aggregateSheet, itemAggregateMap.values(),skuDefinitionList,productList
                ,subCategoryDefinitionMap,1);
                Integer lastRow = aggregateSheet.getLastRowNum();
                addBorder(aggregateSheet,0,lastRow,0,5,workbook);

                response.addHeader("Content-Disposition", "attachment; filename=" + fileName + MimeType.XLSX.name().toLowerCase());

            }
        };
    }

    TransferOrderItem cloneTOItem(TransferOrderItem transferOrderItem){
        TransferOrderItem transferOrderItem1 = new TransferOrderItem();
        transferOrderItem1.setReceivedQuantity(transferOrderItem.getReceivedQuantity());
        transferOrderItem1.setRequestedQuantity(transferOrderItem.getRequestedQuantity());
        transferOrderItem1.setTransferredQuantity(transferOrderItem.getTransferredQuantity());
        transferOrderItem1.setSkuName(transferOrderItem.getSkuName());
        transferOrderItem1.setSkuId(transferOrderItem.getSkuId());
        transferOrderItem1.setUnitOfMeasure(transferOrderItem.getUnitOfMeasure());
        return transferOrderItem1;

    }

    GoodsReceivedItem cloneGRItem(GoodsReceivedItem source){
        GoodsReceivedItem goodsReceivedItem = new GoodsReceivedItem();
        goodsReceivedItem.setReceivedQuantity(Objects.nonNull(source.getReceivedQuantity()) ? source.getReceivedQuantity() : 0);
        goodsReceivedItem.setTransferredQuantity(source.getTransferredQuantity());
        goodsReceivedItem.setExcessQuantity(Objects.nonNull(source.getExcessQuantity()) ? source.getExcessQuantity() : 0);
        goodsReceivedItem.setUnitOfMeasure(source.getUnitOfMeasure());
        goodsReceivedItem.setExpiryDate(source.getExpiryDate());
        goodsReceivedItem.setNegotiatedUnitPrice(source.getNegotiatedUnitPrice());
        goodsReceivedItem.setPrice(source.getPrice());
        goodsReceivedItem.setSkuId(source.getSkuId());
        goodsReceivedItem.setAssociatedAssetId(source.getAssociatedAssetId());
        goodsReceivedItem.setAssociatedAssetTagValue(source.getAssociatedAssetTagValue());
        goodsReceivedItem.setCategory(source.getCategory());
        goodsReceivedItem.setSubCategory(source.getSubCategory());
        goodsReceivedItem.setSkuName(source.getSkuName());
        return goodsReceivedItem;

    }


    Map<Integer,TransferOrderItem> getAggregatedItemMap(List<TransferOrder> transferOrders) {
        Map<Integer, TransferOrderItem> aggregatedMap = new HashMap<>();
        for(TransferOrder transferOrder : transferOrders){
            for (TransferOrderItem transferOrderItem : transferOrder.getTransferOrderItems()) {
                TransferOrderItem aggregatedItem = aggregatedMap.get(transferOrderItem.getSkuId());
                if (aggregatedItem == null) {
                    aggregatedMap.put(transferOrderItem.getSkuId(), cloneTOItem(transferOrderItem));
                } else {
                    aggregatedItem.setTransferredQuantity(
                            aggregatedItem.getTransferredQuantity() + transferOrderItem.getTransferredQuantity());
                    aggregatedItem.setRequestedQuantity(aggregatedItem.getRequestedQuantity()+ transferOrderItem.getRequestedQuantity());
                    aggregatedItem.setReceivedQuantity(aggregatedItem.getReceivedQuantity() + transferOrderItem.getReceivedQuantity());
                }
            }
        }
        return aggregatedMap;

    }

    Map<Integer,GoodsReceivedItem> getAggregatedGrItemMap(List<GoodsReceived> goodsReceivedList) {
        Map<Integer, GoodsReceivedItem> aggregatedMap = new HashMap<>();
        for(GoodsReceived goodsReceived : goodsReceivedList){
            for (GoodsReceivedItem goodsReceivedItem : goodsReceived.getGoodsReceivedItems()) {
                GoodsReceivedItem aggregatedItem = aggregatedMap.get(goodsReceivedItem.getSkuId());
                if (Objects.isNull(aggregatedItem)) {
                    aggregatedMap.put(goodsReceivedItem.getSkuId(), cloneGRItem(goodsReceivedItem));
                } else {
                    aggregatedItem.setTransferredQuantity(
                            aggregatedItem.getTransferredQuantity() + goodsReceivedItem.getTransferredQuantity());
                    if(Objects.nonNull(goodsReceivedItem.getReceivedQuantity())){
                        aggregatedItem.setReceivedQuantity(aggregatedItem.getReceivedQuantity()+ goodsReceivedItem.getReceivedQuantity());
                    }
                    if(Objects.nonNull(goodsReceivedItem.getExcessQuantity())){
                        aggregatedItem.setExcessQuantity(aggregatedItem.getExcessQuantity() + goodsReceivedItem.getExcessQuantity());
                    }
                }
            }
        }
        return aggregatedMap;

    }




    public View  getConsolidatedTorqusView(List<TransferOrderItem> transferOrderItems,
                                          Map<Integer, SkuDefinition> skuDefinitionList, Map<Integer, ProductDefinition> productList,
                                          Map<Integer, SubCategoryDefinition> subCategoryDefinitionMap) {
        return new AbstractXlsxView() {

            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest httpServletRequest, HttpServletResponse response) throws Exception {

                String fileName = "\"Torqus_Transfer_Order_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);

                Sheet sheet = getSheetForWorkBook(workbook,
                        "Transfer_Order_Format-" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                sheet.createRow(0);
                Row aRow = sheet.createRow(1);
                aRow.createCell(0).setCellValue("Supply_TO-Site-ID");
                aRow.createCell(1).setCellValue("supplier-from-siteId");
                aRow.createCell(2).setCellValue("Category");
                aRow.createCell(3).setCellValue("SKU *");
                aRow.createCell(4).setCellValue("Brand");
                aRow.createCell(5).setCellValue("Item Description");
                aRow.createCell(6).setCellValue("Unit");
                aRow.createCell(7).setCellValue("Order-Quantity*");

                Map<Integer, TransferOrderItem> aggregatedMap = new HashMap<>();
                for (TransferOrderItem transferOrderItem : transferOrderItems) {
                    TransferOrderItem aggregatedItem = aggregatedMap.get(transferOrderItem.getSkuId());
                    if (aggregatedItem == null) {
                        aggregatedMap.put(transferOrderItem.getSkuId(), transferOrderItem);
                    } else {
                        aggregatedItem.setTransferredQuantity(
                                aggregatedItem.getTransferredQuantity() + transferOrderItem.getTransferredQuantity());
                    }
                }
                generateTORows(sheet, aggregatedMap.values(), skuDefinitionList, productList, subCategoryDefinitionMap);
            }
        };
    }

    void addTOMetadata(XSSFSheet sheet , TransferOrder transferOrder){
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        Font font = sheet.getWorkbook().createFont();
        font.setFontName("Arial");
        font.setBold(true);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setFont(font);

        CellStyle leftAligned = sheet.getWorkbook().createCellStyle();
        leftAligned.setAlignment(HorizontalAlignment.LEFT);

        // Metadata
        XSSFRow row1 = sheet.createRow(0);
        row1.createCell(0).setCellValue("TO ID");
        row1.getCell(0).setCellStyle(headerStyle);
        row1.createCell(1).setCellValue(transferOrder.getId());
        row1.getCell(1).setCellStyle(leftAligned);
        row1.createCell(2).setCellValue("RO ID");
        row1.getCell(2).setCellStyle(headerStyle);
        if(Objects.nonNull(transferOrder.getRequestOrderId())){
            row1.createCell(3).setCellValue(transferOrder.getRequestOrderId());
            row1.getCell(3).setCellStyle(leftAligned);
        }
        XSSFRow row2 = sheet.createRow(1);
        row2.createCell(0).setCellValue("Generated For Unit");
        row2.getCell(0).setCellStyle(headerStyle);
        row2.createCell(1).setCellValue(transferOrder.getGeneratedForUnitId().getName());
        row2.createCell(2).setCellValue("Generation Time");
        row2.getCell(2).setCellStyle(headerStyle);
        row2.createCell(3).setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(transferOrder.getGenerationTime()));


        XSSFRow row3 = sheet.createRow(2);
        row3.createCell(0).setCellValue("Created By");
        row3.getCell(0).setCellStyle(headerStyle);
        row3.createCell(1).setCellValue(transferOrder.getGeneratedBy().getName());
        row3.createCell(2).setCellValue("TO Status");
        row3.getCell(2).setCellStyle(headerStyle);
        row3.createCell(3).setCellValue(transferOrder.getStatus().value());

        XSSFRow row4 = sheet.createRow(3);
        row4.createCell(0).setCellValue("Cafe Remark");
        row4.getCell(0).setCellStyle(headerStyle);
        if(Objects.nonNull(transferOrder.getComment())){
            row4.createCell(1).setCellValue(transferOrder.getComment());
        }

    }

    public View getTorqusTOView(TransferOrder transferOrder, Map<Integer, SkuDefinition> skuDefinitionList,
                                Map<Integer, ProductDefinition> productList, Map<Integer, SubCategoryDefinition> subCategoryDefinitionMap) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {
                XSSFSheet transferSheet = (XSSFSheet) workbook.createSheet();
                List<String> columns = Arrays.asList("SKU NAME","CATEGORY","UOM","REQ QTY","TRANSFERRED QTY","RECEIVED QTY" );
                workbook.setSheetName(0,"Transfer Order Format-" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "." + MimeType.XLSX.name().toLowerCase());

                addTOMetadata(transferSheet,transferOrder);
                createHeaderRow(workbook,transferSheet,5,columns);
                transferSheet.createFreezePane(0,6);

                fillTOSheet(transferSheet,transferOrder.getTransferOrderItems(),skuDefinitionList,productList,subCategoryDefinitionMap,6);
                addBorder(transferSheet,5,transferSheet.getLastRowNum(),0,5,workbook);
                response.addHeader("Content-Disposition", "attachment; filename=" + "Transfer_Order_" + transferOrder.getId() +  SCMUtil.getCurrentTimeISTStringWithNoColons() + "." + MimeType.XLSX.name().toLowerCase());

            }
        };
    }

//    Sheet sheet = getSheetForWorkBook(workbook,
//            "Transfer Order Format-" + SCMUtil.getCurrentTimeISTStringWithNoColons());
//                sheet.createRow(0);
//    Row aRow = sheet.createRow(1);
//                aRow.createCell(0).setCellValue("Supply_TO-Site-ID");
//                aRow.createCell(1).setCellValue("supplier-from-siteId");
//                aRow.createCell(2).setCellValue("Category");
//                aRow.createCell(3).setCellValue("SKU *");
//                aRow.createCell(4).setCellValue("Brand");
//                aRow.createCell(5).setCellValue("Item Description");
//                aRow.createCell(6).setCellValue("Unit");
//                aRow.createCell(7).setCellValue("Order-Quantity*");

    private void fillTOSheet(XSSFSheet sheet , Collection<TransferOrderItem> transferOrderItemList,
                             Map<Integer, SkuDefinition> skuDefinitionList, Map<Integer, ProductDefinition> productList,
                             Map<Integer, SubCategoryDefinition> subCategoryDefinitionMap , Integer rowCount){


        for(TransferOrderItem transferOrderItem : transferOrderItemList){
             XSSFRow row = sheet.createRow(rowCount);
             SkuDefinition skuDefinition = skuDefinitionList.get(transferOrderItem.getSkuId());
             ProductDefinition product = productList.get(skuDefinition.getLinkedProduct().getId());
             SubCategoryDefinition subCategoryDefinition = subCategoryDefinitionMap
                    .get(product.getSubCategoryDefinition().getId());
             row.createCell(0).setCellValue(skuDefinition.getSkuName());
             row.createCell(1).setCellValue(Objects.nonNull(subCategoryDefinition) ? subCategoryDefinition.getSubCategoryName() : "");
             row.createCell(2).setCellValue(skuDefinition.getUnitOfMeasure());
             row.createCell(3).setCellValue(transferOrderItem.getRequestedQuantity());
             row.createCell(4).setCellValue(transferOrderItem.getTransferredQuantity());
             row.createCell(5).setCellValue(transferOrderItem.getReceivedQuantity());

             rowCount++;


        }
        for(int i = 0;i<14;i++){
            sheet.autoSizeColumn(i);
        }
    }

    private void fillGrAggregateSheet(XSSFSheet sheet , Collection<GoodsReceivedItem> goodsReceivedItems , Integer rowCount){
        for(GoodsReceivedItem goodsReceivedItem : goodsReceivedItems){
            XSSFRow row = sheet.createRow(rowCount);
            row.createCell(0).setCellValue(goodsReceivedItem.getSkuName());
            row.createCell(1).setCellValue(goodsReceivedItem.getCategory());
            row.createCell(2).setCellValue(goodsReceivedItem.getUnitOfMeasure());
            row.createCell(3).setCellValue(goodsReceivedItem.getTransferredQuantity());
            row.createCell(4).setCellValue(goodsReceivedItem.getReceivedQuantity());
            row.createCell(5).setCellValue(goodsReceivedItem.getExcessQuantity());

            rowCount++;


        }
        for(int i = 0;i<6;i++){
            sheet.autoSizeColumn(i);
        }
    }

    private void fillTOsSheet(XSSFSheet sheet , List<TransferOrder> transferOrders,
                             Map<Integer, SkuDefinition> skuDefinitionList, Map<Integer, ProductDefinition> productList,
                             Map<Integer, SubCategoryDefinition> subCategoryDefinitionMap){
        int rowCount = 3;
        for(TransferOrder transferOrder : transferOrders){
            for(TransferOrderItem transferOrderItem : transferOrder.getTransferOrderItems()){
                XSSFRow row = sheet.createRow(rowCount);
                SkuDefinition skuDefinition = skuDefinitionList.get(transferOrderItem.getSkuId());
                ProductDefinition product = productList.get(skuDefinition.getLinkedProduct().getId());
                SubCategoryDefinition subCategoryDefinition = subCategoryDefinitionMap
                        .get(product.getSubCategoryDefinition().getId());
                row.createCell(0).setCellValue(transferOrder.getId());
                row.createCell(1).setCellValue(transferOrder.getGeneratedForUnitId().getName());
                if(Objects.nonNull(transferOrder.getRequestOrderId())){
                    row.createCell(2).setCellValue(transferOrder.getRequestOrderId());
                }
                row.createCell(3).setCellValue(transferOrder.getGeneratedBy().getName());
                row.createCell(4).setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(transferOrder.getGenerationTime()));
                row.createCell(5).setCellValue(skuDefinition.getSkuName());
                row.createCell(6).setCellValue(Objects.nonNull(subCategoryDefinition) ? subCategoryDefinition.getSubCategoryName() : "");
                row.createCell(7).setCellValue(skuDefinition.getUnitOfMeasure());
                row.createCell(8).setCellValue(transferOrderItem.getRequestedQuantity());
                row.createCell(9).setCellValue(transferOrderItem.getTransferredQuantity());
                row.createCell(10).setCellValue(transferOrderItem.getReceivedQuantity());
                row.createCell(11).setCellValue(transferOrder.getStatus().value());
                if(Objects.nonNull(transferOrder.getComment())){
                    row.createCell(12).setCellValue(transferOrder.getComment());
                }


                rowCount++;
            }
        }
        for(int i = 0;i<14;i++){
            sheet.autoSizeColumn(i);
        }
    }

    private void fillGRsSheet(XSSFSheet sheet , List<GoodsReceived> goodsReceivedList){
        int rowCount = 1;
        for(GoodsReceived goodsReceived : goodsReceivedList){
            for(GoodsReceivedItem goodsReceivedItem : goodsReceived.getGoodsReceivedItems()){
                XSSFRow row = sheet.createRow(rowCount);
                row.createCell(0).setCellValue(goodsReceived.getId());
                row.createCell(1).setCellValue(goodsReceived.getGenerationUnitId().getName());
                if(Objects.nonNull(goodsReceived.getTransferOrderId())){
                    row.createCell(2).setCellValue(goodsReceived.getTransferOrderId());
                }
                if(Objects.nonNull(goodsReceivedItem.getAssociatedAssetId())){
                    row.createCell(3).setCellValue(goodsReceivedItem.getAssociatedAssetId());
                }
                row.createCell(4).setCellValue(goodsReceived.getGeneratedBy().getName());
                row.createCell(5).setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(goodsReceived.getGenerationTime()));
                row.createCell(6).setCellValue(goodsReceivedItem.getSkuName());
                row.createCell(7).setCellValue(goodsReceivedItem.getCategory());
                row.createCell(8).setCellValue(goodsReceivedItem.getUnitOfMeasure());
                if(Objects.nonNull(goodsReceivedItem.getReceivedQuantity())){
                    row.createCell(9).setCellValue(goodsReceivedItem.getReceivedQuantity());
                }
                row.createCell(10).setCellValue(goodsReceivedItem.getTransferredQuantity());
                if(Objects.nonNull(goodsReceivedItem.getExcessQuantity())){
                    row.createCell(11).setCellValue(goodsReceivedItem.getExcessQuantity());
                }
                row.createCell(12).setCellValue(goodsReceived.getStatus().value());

                rowCount++;
            }
        }
        for(int i = 0;i<13;i++){
            sheet.autoSizeColumn(i);
        }
    }



    private void generateTORows(Sheet sheet, Collection<TransferOrderItem> transferOrderItems,
                                Map<Integer, SkuDefinition> skuDefinitionList, Map<Integer, ProductDefinition> productList,
                                Map<Integer, SubCategoryDefinition> subCategoryDefinitionMap) {

        int rowCount = 2;
        for (TransferOrderItem item : transferOrderItems) {
            Row row = sheet.createRow(rowCount++);
            SkuDefinition skuDefinition = skuDefinitionList.get(item.getSkuId());
            ProductDefinition product = productList.get(skuDefinition.getLinkedProduct().getId());
            SubCategoryDefinition subCategoryDefinition = subCategoryDefinitionMap
                    .get(product.getSubCategoryDefinition().getId());
            row.createCell(0).setCellValue("");
            row.createCell(1).setCellValue("");
            row.createCell(2)
                    .setCellValue(subCategoryDefinition != null ? subCategoryDefinition.getSubCategoryName() : "");
            row.createCell(3).setCellValue(skuDefinition.getTorqusSkuName());
            row.createCell(4).setCellValue("");
            row.createCell(5).setCellValue(skuDefinition.getSkuName());
            row.createCell(6).setCellValue(skuDefinition.getUnitOfMeasure());
            PackagingDefinition packagingDefinition = null;
            for (SkuPackagingMapping skuPackagingMapping : skuDefinition.getSkuPackagings()) {
                if (skuPackagingMapping.isIsDefault()) {
                    packagingDefinition = scmCache.getPackagingDefinitions().get(skuPackagingMapping.getPackagingId());
                }
            }
            if (packagingDefinition != null) {
                row.createCell(7)
                        .setCellValue(item.getTransferredQuantity() / packagingDefinition.getConversionRatio());
            } else {
                row.createCell(7).setCellValue(item.getTransferredQuantity());
            }

        }
    }

    private List<RequestOrderItem> getRequestOrderItems(List<RequestOrder> requestOrders) {
        Map<Integer, RequestOrderItem> itemList = new HashMap<>();
        List<RequestOrderItem> orderItems = new ArrayList<>();
        for (RequestOrder order : requestOrders) {
            orderItems.addAll(order.getRequestOrderItems());
        }
        for (RequestOrderItem orderItem : orderItems) {
            int productId = orderItem.getProductId();
            RequestOrderItem item = itemList.get(productId);
            if (item == null) {
                item = new RequestOrderItem();
                item.setProductId(productId);
                item.setProductName(orderItem.getProductName());
                item.setUnitOfMeasure(orderItem.getUnitOfMeasure());
                item.setRequestedAbsoluteQuantity(0f);
                itemList.put(productId, item);
            }
            float requestedQuantity = item.getRequestedAbsoluteQuantity();
            requestedQuantity += orderItem.getRequestedAbsoluteQuantity();
            item.setRequestedAbsoluteQuantity(requestedQuantity);
        }
        return new ArrayList<>(itemList.values());
    }

    private CellStyle generateHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Arial");
        style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        font.setBold(true);
        font.setColor(HSSFColor.HSSFColorPredefined.WHITE.getIndex());
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);
        return style;
    }

    private CellStyle generateCellStyle(Workbook workbook, String color) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Arial");
        style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        if(color.equals("Orange")){
            style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.ORANGE.getIndex());
        }
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        font.setBold(true);
        font.setColor(HSSFColor.HSSFColorPredefined.WHITE.getIndex());
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);
        return style;
    }

    private XSSFCellStyle generateCellStyle(XSSFWorkbook workbook , String color , Integer r , Integer g , Integer b , String fontColor){
        //out of color and r,g,b pass one as parameter other as null.
        XSSFCellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Arial");
        if(Objects.nonNull(color)){
            if(color.equals("Green")){
                style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_GREEN.getIndex());
            }
            else if(color.equals("Blue")){
                style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.SKY_BLUE.getIndex());
            }
            else if(color.equals("Yellow")){
                style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_YELLOW.getIndex());
            }
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }else if(Objects.nonNull(r)){
            style.setFillForegroundColor(new XSSFColor(new java.awt.Color(r, g, b)));
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }

        if(Objects.nonNull(fontColor)){
            if(fontColor.equals("Orange")){
                font.setColor(HSSFColor.HSSFColorPredefined.ORANGE.getIndex());
            }
            else if(fontColor.equals("White")){
                font.setColor(HSSFColor.HSSFColorPredefined.WHITE.getIndex());
            }
        }
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);
        return style;
    }

    public void  getPlanningViewBulk(ProductionPlanEvent event, List<RequestOrder> requestOrders,String path , List<String> srcFiles) throws DataNotFoundException, SumoException, IOException {
        getRequestOrderViewBulk(requestOrders, event , path,srcFiles);

    }

    public View  getPlanningView(ProductionPlanEvent event, List<RequestOrder> requestOrders) {
        Date fulfilmentDate = event.getFulfillmentDate();
        return getRequestOrderView(requestOrders, fulfilmentDate, event);
    }

    public View getChaayosAndGntSales(List<UnitReferenceData> referenceOrderUnits) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {
                String timeString = SCMUtil.getCurrentTimeISTStringWithNoColons();
                String fileName = "\"Chaayos_GnT_Sales" + "_" + timeString + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
                Sheet sheet = getSheetForWorkBook(workbook, "Chaayos_GnT_Sales" + "_" + timeString);
                sheet.setDefaultColumnWidth(20);

                CellStyle style = generateHeaderStyle(workbook);

                Row header = sheet.createRow(0);
                List<EstimationSalesDataRequest> estimationSalesDataRequests = referenceOrderUnits.get(0).getSalesData();
                header.createCell(0).setCellValue("HEADERS");
                header.getCell(0).setCellStyle(style);
                for(int i=0; i<estimationSalesDataRequests.size(); i++) {
                    header.createCell(i+1).setCellValue(SCMUtil.getFormattedDate(estimationSalesDataRequests.get(i).getDate()));
                    header.getCell(i+1).setCellStyle(style);
                }
                int count = 0;
                // Unit wise Loop
                for (int i = 0; i < referenceOrderUnits.size(); i++) {
                    Unit unit = dataCache.getUnit(referenceOrderUnits.get(i).getUnitId());
                    int innerCount = count;
                    setText(getCell(sheet,count + 1, 0), unit.getName());
                    setText(getCell(sheet,count + 2, 0), "Chaayos Sales");
                    setText(getCell(sheet,count + 3, 0), "Chaayos Delivery %");
                    setText(getCell(sheet,count + 4, 0), "GnT Sales");
                    setText(getCell(sheet,count + 5, 0), "GnT Delivery %");
                    setText(getCell(sheet,count + 6, 0), " ");
                    count = count + 6;
                    // Date wise Loop
                    for(int j = 0; j<referenceOrderUnits.get(i).getSalesData().size(); j++){
                        // Brand wise Loop
                        for(int k=0 ; k<referenceOrderUnits.get(i).getSalesData().get(j).getBrands().size(); k++){
                            if(referenceOrderUnits.get(i).getSalesData().get(j).getBrands().get(k).getId().equals(1)){
                                setNumber(getCell(sheet, innerCount + 2, j+1), AppUtils.roundToInteger(referenceOrderUnits.get(i).getSalesData().get(j).getBrands().get(k).getSaleAmount()));
                                setNumber(getCell(sheet, innerCount + 3, j+1), AppUtils.roundToInteger(referenceOrderUnits.get(i).getSalesData().get(j).getBrands().get(k).getDeliverySalePercentage()));
                            }
                            if(referenceOrderUnits.get(i).getSalesData().get(j).getBrands().get(k).getId().equals(3)){
                                setNumber(getCell(sheet, innerCount + 4, j+1), AppUtils.roundToInteger(referenceOrderUnits.get(i).getSalesData().get(j).getBrands().get(k).getSaleAmount()));
                                setNumber(getCell(sheet, innerCount + 5, j+1), AppUtils.roundToInteger(referenceOrderUnits.get(i).getSalesData().get(j).getBrands().get(k).getDeliverySalePercentage()));
                            }
                        }
                    }
                }
            }
        };
    }

    public View getWarehouseInventoryListView(List<SkuStockForUnit> inventoryList, String unitName) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {
                String timeString = SCMUtil.getCurrentTimeISTStringWithNoColons();
                String fileName = "\"Inventory_List_" + unitName.replaceAll(" ", "_") + "_" + timeString + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
                Sheet sheet = getSheetForWorkBook(workbook, "Inventory_List_" + unitName.replaceAll(" ", "_") + "_" + timeString);
                sheet.setDefaultColumnWidth(20);

                CellStyle style = generateHeaderStyle(workbook);

                Row header = sheet.createRow(0);

                header.createCell(0).setCellValue("S.No.");
                header.getCell(0).setCellStyle(style);

                header.createCell(1).setCellValue("SKU Name");
                header.getCell(1).setCellStyle(style);

                header.createCell(2).setCellValue("SKU Id");
                header.getCell(2).setCellStyle(style);

                header.createCell(3).setCellValue("Unit of Measure");
                header.getCell(3).setCellStyle(style);

                header.createCell(4).setCellValue("Stock");
                header.getCell(4).setCellStyle(style);

                header.createCell(5).setCellValue("Category Definition");
                header.getCell(5).setCellStyle(style);

                header.createCell(6).setCellValue("Sub Category Definition");
                header.getCell(6).setCellStyle(style);



                for (int i = 0; i < inventoryList.size(); i++) {
                    SkuStockForUnit sku = inventoryList.get(i);
                    setText(getCell(sheet, i + 1, 0), String.valueOf(i + 1));
                    setText(getCell(sheet, i + 1, 1), sku.getName());
                    setText(getCell(sheet, i + 1, 2), String.valueOf(sku.getSkuId()));
                    setText(getCell(sheet, i + 1, 3), sku.getUom());
                    setText(getCell(sheet, i + 1, 4), "");
                    setText(getCell(sheet, i + 1, 5), sku.getCategoryDef());
                    setText(getCell(sheet, i + 1, 6), sku.getSubCategoryDef());
                }
            }
        };

    }


    public View getYesBankPRView(List<PaymentRequest> paymentRequests, Map<Integer, VendorDetail> vendors, CompanyBankMapping mapping , Boolean toBeSaved) {
        View view = new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {
                String fileName = "\"Payment_Request_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
                Sheet sheet = getSheetForWorkBook(workbook,
                        "Payment_Request_Sheet-" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                sheet.setDefaultColumnWidth(20);
                CellStyle style = generateHeaderStyle(workbook);
                Row header = sheet.createRow(0);
                header.createCell(0).setCellValue("Beneficiary Name");
                header.getCell(0).setCellStyle(style);
                header.createCell(1).setCellValue("Beneficiary A/c No.");
                header.getCell(1).setCellStyle(style);
                header.createCell(2).setCellValue("IFS Code");
                header.getCell(2).setCellStyle(style);
                header.createCell(3).setCellValue("Amount");
                header.getCell(3).setCellStyle(style);
                header.createCell(4).setCellValue("Remarks");
                header.getCell(4).setCellStyle(style);
                header.createCell(5).setCellValue("PR Id");
                header.getCell(5).setCellStyle(style);
                header.createCell(6).setCellValue("UTR Number");
                header.getCell(6).setCellStyle(style);
                header.createCell(7).setCellValue("Actual Date (Mention in dd-mm-yyyy) format" );
                header.getCell(7).setCellStyle(style);
                for (int i = 0; i < paymentRequests.size(); i++) {
                    PaymentRequest paymentRequest = paymentRequests.get(i);
                    VendorDetail vendorDetail = vendors.get(paymentRequest.getVendorId().getId());
                    setText(getCell(sheet, i + 1, 0), vendorDetail.getEntityName());
                    setText(getCell(sheet, i + 1, 1), vendorDetail.getAccountDetails().getAccountNumber());
                    setText(getCell(sheet, i + 1, 2), vendorDetail.getAccountDetails().getIfscCode());
                    setNumber(getCell(sheet, i + 1, 3), AppUtils.roundToInteger(paymentRequest.getPaidAmount()));
                    setText(getCell(sheet, i + 1, 4), paymentRequest.getInvoiceNumber());
                    setNumber(getCell(sheet, i + 1, 5), paymentRequest.getPaymentRequestId());
                    setText(getCell(sheet, i + 1, 6), "");
                    setText(getCell(sheet, i + 1, 7), "");
                }
                if(toBeSaved.equals(Boolean.TRUE) && !paymentRequests.isEmpty()){
                    String newFileName = "Payment_Request_" + SCMUtil.getCurrentTimeISTStringWithNoColons();
                    sendPaymentSheetEmail(newFileName,workbook,paymentRequests,mapping);
                }
            }
        };
        return view;
    }





    public View getKotakBankPRView(List<PaymentRequest> paymentRequests, Map<Integer, VendorDetail> vendors, CompanyBankMapping mapping , Boolean toBeSaved ) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Payment_Request_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);

                Sheet sheet = getSheetForWorkBook(workbook,
                        "Payment_Request_Sheet-" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                sheet.setDefaultColumnWidth(20);
                CellStyle style = generateHeaderStyle(workbook);
                Row header = sheet.createRow(0);
                header.createCell(0).setCellValue("Client_Code");
                header.getCell(0).setCellStyle(style);
                header.createCell(1).setCellValue("Product_Code");
                header.getCell(1).setCellStyle(style);
                header.createCell(2).setCellValue("Payment_Type");
                header.getCell(2).setCellStyle(style);
                header.createCell(3).setCellValue("Payment_Ref_No");
                header.getCell(3).setCellStyle(style);
                header.createCell(4).setCellValue("Payment_Date (Mention in dd/mm/yyyy) format");
                header.getCell(4).setCellStyle(style);
                header.createCell(5).setCellValue("Dr_Ac_No");
                header.getCell(5).setCellStyle(style);
                header.createCell(6).setCellValue("Amount");
                header.getCell(6).setCellStyle(style);
                header.createCell(7).setCellValue("Beneficiary_Code");
                header.getCell(7).setCellStyle(style);
                header.createCell(8).setCellValue("Beneficiary_Name");
                header.getCell(8).setCellStyle(style);
                header.createCell(9).setCellValue("IFSC Code");
                header.getCell(9).setCellStyle(style);
                header.createCell(10).setCellValue("Beneficiary_Acc_No");
                header.getCell(10).setCellStyle(style);
                header.createCell(11).setCellValue("Beneficiary_Email");
                header.getCell(11).setCellStyle(style);
                header.createCell(12).setCellValue("Beneficiary_Mobile");
                header.getCell(12).setCellStyle(style);
                header.createCell(13).setCellValue("Debit_Narration");
                header.getCell(13).setCellStyle(style);
                header.createCell(14).setCellValue("Approver");
                header.getCell(14).setCellStyle(style);
                header.createCell(15).setCellValue("Enrichment_1");
                header.getCell(15).setCellStyle(style);
                header.createCell(16).setCellValue("Enrichment_1");
                header.getCell(16).setCellStyle(style);
                header.createCell(17).setCellValue("PR Id");
                header.getCell(17).setCellStyle(style);
                header.createCell(18).setCellValue("UTR Number");
                header.getCell(18).setCellStyle(style);
                header.createCell(19).setCellValue("Actual Date (Mention in dd-mm-yyyy) format");
                header.getCell(19).setCellStyle(style);


                for (int i = 0; i < paymentRequests.size(); i++) {
                    PaymentRequest paymentRequest = paymentRequests.get(i);
                    VendorDetail vendorDetail = vendors.get(paymentRequest.getVendorId().getId());
                    setText(getCell(sheet, i + 1, 0), mapping.getClientCode());
                    setText(getCell(sheet, i + 1, 1), "RPAY");
                    setText(getCell(sheet, i + 1, 2), vendorDetail.getAccountDetails().getIfscCode().startsWith("KKBK") ?
                            PaymentType.IFT.value() : PaymentType.NEFT.value());
                    setText(getCell(sheet, i + 1, 3), "");
                    setDate(workbook, getCell(sheet, i + 1, 4), paymentRequest.getPaymentDate(), "dd/MM/yyyy");
                    setText(getCell(sheet, i + 1, 5), mapping.getBankAccountNo());
                    setNumber(getCell(sheet, i + 1, 6), AppUtils.roundToInteger(paymentRequest.getPaidAmount()));
                    setText(getCell(sheet, i + 1, 7), "");
                    setText(getCell(sheet, i + 1, 8), SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()));
                    setText(getCell(sheet, i + 1, 9), vendorDetail.getAccountDetails().getIfscCode());
                    setText(getCell(sheet, i + 1, 10), vendorDetail.getAccountDetails().getAccountNumber());
                    setText(getCell(sheet, i + 1, 11), vendorDetail.getPrimaryEmail());
                    setText(getCell(sheet, i + 1, 12), vendorDetail.getPrimaryContact());
                    setText(getCell(sheet, i + 1, 13), SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()));
                    setText(getCell(sheet, i + 1, 14), "");
                    setText(getCell(sheet, i + 1, 15), "");
                    setText(getCell(sheet, i + 1, 16), "");
                    setNumber(getCell(sheet, i + 1, 17), paymentRequest.getPaymentRequestId());
                    setText(getCell(sheet, i + 1, 18), "");
                    setText(getCell(sheet, i + 1, 19), "");
                }
                if(toBeSaved.equals(Boolean.TRUE) && !paymentRequests.isEmpty()){
                    String newFileName = "Payment_Request_" + SCMUtil.getCurrentTimeISTStringWithNoColons();
                     sendPaymentSheetEmail(newFileName,workbook,paymentRequests,mapping);
                }
            }
        };

    }

    private Boolean sendPaymentSheetEmail(String fileName , Workbook workbook ,List<PaymentRequest> paymentRequests, CompanyBankMapping mapping) throws IOException {
        File fileToUpload = new File(props.getBasePath() + fileName );
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        byte[] barray = null;
        try {
            workbook.write(bos);
        } catch (IOException e1) {
            LOG.error("Error in Generating Payment Sheet", e1);
        }
        try {
            barray = bos.toByteArray();
        }catch (Exception e){
            LOG.error("Error While Creating File");
        }
        //email generation
        try{
            LOG.info("Trying to sent  email For Payment Sheet");
            List<String> toEmails = new ArrayList<>();
            toEmails.add("<EMAIL>");
            List<AttachmentData> attachments = new ArrayList<>();
            AttachmentData paymentSheet = null;
            paymentSheet = new AttachmentData(barray,fileName,
                    AppConstants.EXCEL_MIME_TYPE);
            attachments.add(paymentSheet);
            List<Integer> prIds = paymentRequests.stream().map(pr -> pr.getPaymentRequestId()).collect(Collectors.toList());
            PaymentRequestEmailNotification paymentRequestEmailNotification = new PaymentRequestEmailNotification(props.getEnvType(),prIds, mapping.getBankName(),
                    mapping.getCompanyId(),toEmails);
            LOG.info("Sending email..");
            paymentRequestEmailNotification.sendRawMail(attachments);
            fileToUpload.delete();
            LOG.info("Sent Email");
        }catch (Exception e){
            LOG.info("error sending email");
            e.printStackTrace();
            LOG.info(e.getMessage());
        }
        return  true;

    }


    public View getKotakV2BankPRView(List<PaymentRequest> paymentRequests, Map<Integer, VendorDetail> vendors, CompanyBankMapping mapping, Boolean toBeSaved) {
        AbstractXlsxView view = new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Payment_Request_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);

                Sheet sheet = getSheetForWorkBook(workbook,
                        "Payment_Request_Sheet-" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                sheet.setDefaultColumnWidth(20);
                CellStyle style = generateHeaderStyle(workbook);
                Row header = sheet.createRow(0);
                header.createCell(0).setCellValue("Client_Code");
                header.getCell(0).setCellStyle(style);
                header.createCell(1).setCellValue("Product_Code");
                header.getCell(1).setCellStyle(style);
                header.createCell(2).setCellValue("Payment_Type");
                header.getCell(2).setCellStyle(style);
                header.createCell(3).setCellValue("Payment_Ref_No.");
                header.getCell(3).setCellStyle(style);
                header.createCell(4).setCellValue("Payment_Date");
                header.getCell(4).setCellStyle(style);
                header.createCell(5).setCellValue("Instrument Date");
                header.getCell(5).setCellStyle(style);
                header.createCell(6).setCellValue("Dr_Ac_No");
                header.getCell(6).setCellStyle(style);
                header.createCell(7).setCellValue("Amount");
                header.getCell(7).setCellStyle(style);
                header.createCell(8).setCellValue("Bank_Code_Indicator");
                header.getCell(8).setCellStyle(style);
                header.createCell(9).setCellValue("Beneficiary_Code");
                header.getCell(9).setCellStyle(style);
                header.createCell(10).setCellValue("Beneficiary_Name");
                header.getCell(10).setCellStyle(style);
                header.createCell(11).setCellValue("Beneficiary_Bank");
                header.getCell(11).setCellStyle(style);
                header.createCell(12).setCellValue("Beneficiary_Branch / IFSC Code");
                header.getCell(12).setCellStyle(style);
                header.createCell(13).setCellValue("Beneficiary_Acc_No");
                header.getCell(13).setCellStyle(style);

                header.createCell(14).setCellValue("Location");
                header.getCell(14).setCellStyle(style);
                header.createCell(15).setCellValue("Print_Location");
                header.getCell(15).setCellStyle(style);
                header.createCell(16).setCellValue("Instrument_Number");
                header.getCell(16).setCellStyle(style);
                header.createCell(17).setCellValue("Ben_Add1");
                header.getCell(17).setCellStyle(style);
                header.createCell(18).setCellValue("Ben_Add2");
                header.getCell(18).setCellStyle(style);
                header.createCell(19).setCellValue("Ben_Add3");
                header.getCell(19).setCellStyle(style);
                header.createCell(20).setCellValue("Ben_Add4");
                header.getCell(20).setCellStyle(style);

                header.createCell(21).setCellValue("Beneficiary_Email");
                header.getCell(21).setCellStyle(style);
                header.createCell(22).setCellValue("Beneficiary_Mobile");
                header.getCell(22).setCellStyle(style);
                header.createCell(23).setCellValue("Debit_Narration");
                header.getCell(23).setCellStyle(style);
                header.createCell(24).setCellValue("Credit_Narration");
                header.getCell(24).setCellStyle(style);
                header.createCell(25).setCellValue("PR Id");
                header.getCell(25).setCellStyle(style);
                header.createCell(26).setCellValue("UTR Number");
                header.getCell(26).setCellStyle(style);
                header.createCell(27).setCellValue("Actual Date (Mention in dd-mm-yyyy) format");
                header.getCell(27).setCellStyle(style);


                for (int i = 0; i < paymentRequests.size(); i++) {
                    PaymentRequest paymentRequest = paymentRequests.get(i);
                    VendorDetail vendorDetail = vendors.get(paymentRequest.getVendorId().getId());
                    setText(getCell(sheet, i + 1, 0), mapping.getClientCode());
                    setText(getCell(sheet, i + 1, 1), "VENPAY");
                    setText(getCell(sheet, i + 1, 2), vendorDetail.getAccountDetails().getIfscCode().startsWith("KKBK") ?
                            PaymentType.IFT.value() : PaymentType.NEFT.value());
                    setText(getCell(sheet, i + 1, 3), "");
                    setDate(workbook, getCell(sheet, i + 1, 4), paymentRequest.getPaymentDate(), "dd/MM/yyyy");
                    setText(getCell(sheet, i + 1, 5), "");
                    setText(getCell(sheet, i + 1, 6), mapping.getBankAccountNo());
                    setNumber(getCell(sheet, i + 1, 7), AppUtils.roundToInteger(paymentRequest.getPaidAmount()));
                    setText(getCell(sheet, i + 1, 8), "M");
                    setText(getCell(sheet, i + 1, 9), "");
                    setText(getCell(sheet, i + 1, 10), SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()));
                    setText(getCell(sheet, i + 1, 11), "");
                    setText(getCell(sheet, i + 1, 12), vendorDetail.getAccountDetails().getIfscCode());
                    setText(getCell(sheet, i + 1, 13), vendorDetail.getAccountDetails().getAccountNumber());
                    setText(getCell(sheet, i + 1, 14), "");
                    setText(getCell(sheet, i + 1, 15), "");
                    setText(getCell(sheet, i + 1, 16), "");
                    setText(getCell(sheet, i + 1, 17), "");
                    setText(getCell(sheet, i + 1, 18), "");
                    setText(getCell(sheet, i + 1, 19), "");
                    setText(getCell(sheet, i + 1, 20), "");
                    setText(getCell(sheet, i + 1, 21), vendorDetail.getPrimaryEmail());
                    setText(getCell(sheet, i + 1, 22), vendorDetail.getPrimaryContact());
                    setText(getCell(sheet, i + 1, 23), SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()));
                    setText(getCell(sheet, i + 1, 24), SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()));
                    setNumber(getCell(sheet, i + 1, 25), paymentRequest.getPaymentRequestId());
                    setText(getCell(sheet, i + 1, 26), "");
                    setText(getCell(sheet, i + 1, 27), "");
                }
                if(toBeSaved.equals(Boolean.TRUE) && !paymentRequests.isEmpty()){
                    String newFileName = "Payment_Request_" + SCMUtil.getCurrentTimeISTStringWithNoColons();
                    sendPaymentSheetEmail(newFileName,workbook,paymentRequests,mapping);
                }
            }
        };
        return view;
    }

    public View getHdfcBankPRView(List<PaymentRequest> paymentRequests, Map<Integer, VendorDetail> vendors, CompanyBankMapping mapping, Boolean toBeSaved) {
        View view = new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Payment_Request_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);

                Sheet sheet = getSheetForWorkBook(workbook,
                        "Payment_Request_Sheet-" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                sheet.setDefaultColumnWidth(20);
                CellStyle style = generateHeaderStyle(workbook);
                Row header = sheet.createRow(0);

                int columnCount = 0;
                List<String> fieldNames = new ArrayList<>(Arrays.asList("Transaction_Type","Beneficiary_Code","Beneficiary_Account_Number","Amount_to_be_transferred","Beneficiary_Name",
                        "Drawee_Location","Print_Location","Beneficiary_Address_1","Beneficiary_Address_2","Beneficiary_Address_3","Beneficiary_Address_4","Beneficiary_Address_5","Instruction_Reference_Number",
                        "Customer Reference Number(PR Id)","Payment_Details_1","Payment_Details_2","Payment_Details_3","Payment_Details_4","Payment_Details_5","Payment_Details_6",
                        "Payment_Details_7","Cheque_Number","Transaction_Date(DD/MM/YYYY)","MICR_Number","IFSC_Code","Beneficiary_Bank_Name","Beneficiary_Bank_Branch_Name",
                        "Beneficiary_Email_Id","Debit_Account_Number","UTR Number","Actual_Date(Mention in dd-mm-yyyy) format"));
                for (String fieldName : fieldNames) {
                    Cell cell = header.createCell(columnCount++);
                    cell.setCellValue(fieldName);
                    cell.setCellStyle(style);
                }

                for (int i = 0; i < paymentRequests.size(); i++) {
                    PaymentRequest paymentRequest = paymentRequests.get(i);
                    VendorDetail vendorDetail = vendors.get(paymentRequest.getVendorId().getId());
                    setText(getCell(sheet, i + 1, 0), vendorDetail.getAccountDetails().getIfscCode().startsWith("HDFC") ?
                            "I" : "N");
                    setText(getCell(sheet, i + 1, 1), vendorDetail.getAccountDetails().getIfscCode().startsWith("HDFC") ?
                            vendorDetail.getAccountDetails().getAccountNumber() : "");
                    setText(getCell(sheet, i + 1, 2), vendorDetail.getAccountDetails().getAccountNumber());
                    setNumber(getCell(sheet, i + 1, 3), AppUtils.roundToInteger(paymentRequest.getPaidAmount()));
                    setText(getCell(sheet, i + 1, 4), SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()).length() > 40 ?
                            SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()).substring(0,40) : SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()));
                    setNumber(getCell(sheet, i + 1, 13), paymentRequest.getPaymentRequestId());
                    setText(getCell(sheet, i + 1, 14), mapping.getBankIFSCCode().substring(0,4)+"_TO_"+ (getBankName(vendorDetail).equalsIgnoreCase("")
                            ? vendorDetail.getAccountDetails().getIfscCode().substring(0,4) : getBankName(vendorDetail)));
                    setDate(workbook, getCell(sheet, i + 1, 22), paymentRequest.getPaymentDate(), "dd/MM/yyyy");
                    setText(getCell(sheet, i + 1, 24), vendorDetail.getAccountDetails().getIfscCode());
                    setText(getCell(sheet, i + 1, 25), getBankName(vendorDetail));
                    setText(getCell(sheet, i + 1, 27), vendorDetail.getPrimaryEmail());
                    setText(getCell(sheet, i + 1, 28), mapping.getBankAccountNo());
                    setText(getCell(sheet, i + 1, 29), "");
                    setText(getCell(sheet, i + 1, 30), "");
                }
                if(toBeSaved.equals(Boolean.TRUE) && !paymentRequests.isEmpty()){
                    String newFileName = "Payment_Request_" + SCMUtil.getCurrentTimeISTStringWithNoColons();
                    sendPaymentSheetEmail(newFileName,workbook,paymentRequests,mapping);
                }
            }
        };
        return view;
    }
    public View getIciciBankPRView(List<PaymentRequest> paymentRequests, Map<Integer, VendorDetail> vendors, CompanyBankMapping mapping, Boolean toBeSaved){
        View view = new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Payment_Request_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);

                Sheet sheet = getSheetForWorkBook(workbook,
                        "Payment_Request_Sheet-" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                sheet.setDefaultColumnWidth(20);
                CellStyle style = generateHeaderStyle(workbook);
                Row header = sheet.createRow(0);

                int columnCount = 0;
                List<String>fieldNames = new ArrayList<>(Arrays.asList("Debit Ac No" , "Beneficiary Ac No","Beneficiary Name","Amt","Pay Mod","Payment_Instruction_Date","IFSC","Payable Location name","Print Location","Bene Mobile no","Bene email id","Ben add1","Ben add2","Ben add3","Ben add4","PR NUMBER","FILE NAME","Add details 3","Add details 4","Add details 5","NARRATION","Utr Number","Payment Date")) ;
               
                for (String fieldName : fieldNames) {
                    Cell cell = header.createCell(columnCount++);
                    cell.setCellValue(fieldName);
                    cell.setCellStyle(style);
                }

                for (int i = 0; i < paymentRequests.size(); i++) {
                    PaymentRequest paymentRequest = paymentRequests.get(i);
                    VendorDetail vendorDetail = vendors.get(paymentRequest.getVendorId().getId());
                    setText(getCell(sheet, i + 1, 0), mapping.getBankAccountNo());
                            setText(getCell(sheet, i + 1, 1), vendorDetail.getAccountDetails().getAccountNumber());
                            
                            setText(getCell(sheet, i + 1, 2), SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()).length() > 40 ?
                            SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()).substring(0,40) : SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()));
                            setNumber(getCell(sheet, i + 1, 3), AppUtils.roundToInteger(paymentRequest.getPaidAmount()));
                            setText(getCell(sheet, i + 1, 4), vendorDetail.getAccountDetails().getIfscCode().startsWith("ICIC") ?
                            "I" : AppUtils.roundToInteger(paymentRequest.getPaidAmount()) <= 200000 ? "N" : "R");
                            setDate(workbook, getCell(sheet, i + 1, 5), paymentRequest.getPaymentDate(), "dd-MMM-yyyy");
                    setText(getCell(sheet, i + 1, 6), vendorDetail.getAccountDetails().getIfscCode()
                            );

                             setText(getCell(sheet, i + 1, 9), vendorDetail.getPrimaryContact());
                             setText(getCell(sheet, i + 1, 10), vendorDetail.getPrimaryEmail());
                             setNumber(getCell(sheet, i + 1, 15), paymentRequest.getPaymentRequestId());
                }
                if(toBeSaved.equals(Boolean.TRUE) && !paymentRequests.isEmpty()){
                    String newFileName = "Payment_Request_" + SCMUtil.getCurrentTimeISTStringWithNoColons();
                    sendPaymentSheetEmail(newFileName,workbook,paymentRequests,mapping);
                }
            }
        };
        return view;
       
    }

    private static String getBankName(VendorDetail vendorDetail) {
        String bankName = BankIfscCode.getBankNameFromIfsc(vendorDetail.getAccountDetails().getIfscCode().substring(0,4));
        if (Objects.nonNull(bankName)) {
            return bankName;
        }
        return "";
    }


    public View getVendorDebitBalanceView(List<VendorDetail> vendorDetails, int companyId) {
        View view = new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {
                String fileName = "\"Vendor_Debit_Balance" + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
                Sheet sheet = getSheetForWorkBook(workbook,
                        "Vendor_Debit_Balance_Sheet-" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                sheet.setDefaultColumnWidth(20);
                CellStyle style = generateHeaderStyle(workbook);
                Row header = sheet.createRow(0);
                header.createCell(0).setCellValue("Vendor Id");
                header.getCell(0).setCellStyle(style);
                header.createCell(1).setCellValue("Vendor Name");
                header.getCell(1).setCellStyle(style);
                header.createCell(2).setCellValue("Debit Balance");
                header.getCell(2).setCellStyle(style);
                header.createCell(3).setCellValue("Company Id");
                header.getCell(3).setCellStyle(style);
                header.createCell(4).setCellValue("Company Name");
                header.getCell(4).setCellStyle(style);
                for (int i = 0; i < vendorDetails.size(); i++) {
                    VendorDetail vendorDetail = vendorDetails.get(i);
                    setNumber(getCell(sheet, i + 1, 0), vendorDetail.getVendorId());
                    setText(getCell(sheet, i + 1, 1), vendorDetail.getEntityName());
                    BigDecimal debitBalance = new BigDecimal(0);
                    for (VendorDebitBalanceVO vo : vendorDetail.getVos()) {
                        if (vo.getCompanyId() == companyId) {
                            debitBalance = vo.getDebitBalance();
                        }
                    }
                    setNumber(getCell(sheet, i + 1, 2), debitBalance.doubleValue());
                    setNumber(getCell(sheet, i + 1, 3), companyId);
                    setText(getCell(sheet, i + 1, 4), dataCache.getCompany(companyId).getName());
                }
            }
        };
        return view;
    }

    private void setNumber(Cell cell, double number) {
        cell.setCellType(CellType.NUMERIC);
        cell.setCellValue(number);
    }

    private CellStyle getDateStyle(HSSFWorkbook wb, String format) {
        CellStyle cellStyle = wb.createCellStyle();
        CreationHelper createHelper = wb.getCreationHelper();
        cellStyle.setDataFormat(createHelper.createDataFormat().getFormat("dd/MM/yyyy"));
        return cellStyle;
    }

    private void setDate(Workbook wb, Cell cell, Date date, String format) {
        CellStyle cellStyle = wb.createCellStyle();
        CreationHelper createHelper = wb.getCreationHelper();
        cellStyle.setDataFormat(createHelper.createDataFormat().getFormat(format));
        cell.setCellStyle(cellStyle);
        cell.setCellType(STRING);
        cell.setCellValue(AppUtils.getFormattedTime(date, format));
    }

    private void setDate(XSSFWorkbook wb, XSSFCell cell, Date date, String format) {
        CellStyle cellStyle = wb.createCellStyle();
        CreationHelper createHelper = wb.getCreationHelper();
        cellStyle.setDataFormat(createHelper.createDataFormat().getFormat(format));
        cell.setCellStyle(cellStyle);
        cell.setCellType(CellType.STRING);
        //String data = AppUtils.getFormattedTime(date, format);
        cell.setCellValue(AppUtils.getFormattedTime(date, format));
    }

    protected Cell getCell(Sheet sheet, int row, int col) {
        Row sheetRow = sheet.getRow(row);
        if (sheetRow == null) {
            sheetRow = sheet.createRow(row);
        }

        Cell cell = sheetRow.getCell(col);
        if (cell == null) {
            cell = sheetRow.createCell(col);
        }

        return cell;
    }

    void setText(Cell cell, String text) {
        cell.setCellType(STRING);
        cell.setCellValue(text);
    }

    public View generateInvoiceExcel(SalesPerformaInvoice invoice) {
        View view = new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Performa_Invoice_" + invoice.getId() + "_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xlsx\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
                Sheet sheet = getSheetForWorkBook(workbook,
                        "Performa_Invoice_" + invoice.getId() + "_" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                sheet.setDefaultColumnWidth(20);
                CellStyle style = generateHeaderStyle(workbook);
                Row header = sheet.createRow(0);
                header.createCell(0).setCellValue("Transfer Date");
                header.getCell(0).setCellStyle(style);
                header.createCell(1).setCellValue("Sending Unit");
                header.getCell(1).setCellStyle(style);
                header.createCell(2).setCellValue("Sending Unit GSTIN");
                header.getCell(2).setCellStyle(style);
                header.createCell(3).setCellValue("Sending Unit Address");
                header.getCell(3).setCellStyle(style);
                header.createCell(4).setCellValue("Sending Unit Pin code");
                header.getCell(4).setCellStyle(style);
                header.createCell(5).setCellValue("Sending Unit State");
                header.getCell(5).setCellStyle(style);
                header.createCell(6).setCellValue("Receiving Unit");
                header.getCell(6).setCellStyle(style);
                header.createCell(7).setCellValue("Receiving Unit GSTIN");
                header.getCell(7).setCellStyle(style);
                header.createCell(8).setCellValue("Receiving Unit Address");
                header.getCell(8).setCellStyle(style);
                header.createCell(9).setCellValue("Receiving Unit Pin code");
                header.getCell(9).setCellStyle(style);
                header.createCell(10).setCellValue("Receiving Unit State");
                header.getCell(10).setCellStyle(style);
                if (invoice.getVehicle().getTransportMode().equals(TransportMode.ROAD.name())) {
                    header.createCell(11).setCellValue("Vehicle Name");
                    header.getCell(11).setCellStyle(style);
                    header.createCell(12).setCellValue("Vehicle Number");
                    header.getCell(12).setCellStyle(style);
                } else {
                    header.createCell(11).setCellValue("Transporter GSTIN");
                    header.getCell(11).setCellStyle(style);
                    header.createCell(12).setCellValue("Docket Number");
                    header.getCell(12).setCellStyle(style);
                }
                header.createCell(13).setCellValue("SKU_NAME");
                header.getCell(13).setCellStyle(style);
                header.createCell(14).setCellValue("HSN");
                header.getCell(14).setCellStyle(style);
                header.createCell(15).setCellValue("Quantity");
                header.getCell(15).setCellStyle(style);
                header.createCell(16).setCellValue("Base Price");
                header.getCell(16).setCellStyle(style);
                header.createCell(17).setCellValue("Cost");
                header.getCell(17).setCellStyle(style);
                header.createCell(18).setCellValue("Tax");
                header.getCell(18).setCellStyle(style);
                header.createCell(19).setCellValue("CGST Amount");
                header.getCell(19).setCellStyle(style);
                header.createCell(20).setCellValue("SGST Amount");
                header.getCell(20).setCellStyle(style);
                header.createCell(21).setCellValue("IGST Amount");
                header.getCell(21).setCellStyle(style);
                header.createCell(22).setCellValue("CESS Amount");
                header.getCell(22).setCellStyle(style);
                header.createCell(23).setCellValue("Total Invoice Value");
                header.getCell(23).setCellStyle(style);
                header.createCell(24).setCellValue("Billing Address");
                header.getCell(24).setCellStyle(style);
                header.createCell(25).setCellValue("Purchase Order No.");
                header.getCell(25).setCellStyle(style);
                header.createCell(26).setCellValue("Cost Price");
                header.getCell(26).setCellStyle(style);
                header.createCell(27).setCellValue("SKU_UOM");
                header.getCell(27).setCellStyle(style);
                header.createCell(28).setCellValue("INVOICE_NO");
                header.getCell(28).setCellStyle(style);

                double amount = SCMUtil.add(invoice.getTotalAmount(), invoice.getAdditionalCharges()).doubleValue();
                for (int i = 0; i < invoice.getItems().size(); i++) {
                    Row itemRow = sheet.createRow(i + 1);
                    SalesPerformaInvoiceItem invoiceItem = invoice.getItems().get(i);
                    if (i == 0) {
                        setMetaData(itemRow, invoice);
                    }
                    itemRow.createCell(13, STRING).setCellValue(invoiceItem.getSku().getName());
                    itemRow.createCell(14, STRING).setCellValue(invoiceItem.getSku().getCode());
                    itemRow.createCell(15, STRING).setCellValue(invoiceItem.getQuantity().doubleValue());
                    itemRow.createCell(16, CellType.NUMERIC).setCellValue(invoiceItem.getSellPrice().doubleValue());
                    itemRow.createCell(17, CellType.NUMERIC).setCellValue(invoiceItem.getSellAmount().doubleValue());
                    itemRow.createCell(18, CellType.NUMERIC).setCellValue(invoiceItem.getTotalTax().doubleValue());

                    List<SalesPerformaItemTax> taxes = invoiceItem.getTaxes();
                    double cgst = 0d;
                    double igst = 0d;
                    double sgst = 0d;
                    double cess = 0d;

                    for (SalesPerformaItemTax tax : taxes) {
                        switch (tax.getType()) {
                            case CGST:
                                cgst = tax.getValue().doubleValue();
                                break;
                            case IGST:
                                igst = tax.getValue().doubleValue();
                                break;
                            case SGST:
                                sgst = tax.getValue().doubleValue();
                                break;
                            case CESS1:
                                cess = tax.getValue().doubleValue();
                                break;
                        }
                    }
                    itemRow.createCell(19, CellType.NUMERIC).setCellValue(cgst);
                    itemRow.createCell(20, CellType.NUMERIC).setCellValue(sgst);
                    itemRow.createCell(21, CellType.NUMERIC).setCellValue(igst);
                    itemRow.createCell(22, CellType.NUMERIC).setCellValue(cess);
                    itemRow.createCell(23, CellType.NUMERIC).setCellValue(amount);
                    if(invoiceItem.getCurrPrice() != null) {
                        itemRow.createCell(26, CellType.NUMERIC).setCellValue(invoiceItem.getCurrPrice().doubleValue());
                    }
                    itemRow.createCell(27, CellType.NUMERIC).setCellValue(invoiceItem.getUom());
                }
            }
        };
        return view;
    }

    public View downloadCnOrDnInvoice(SalesPerformaInvoice invoice, String type) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = type + "_" + invoice.getId() + "_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xlsx\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
                Sheet sheet = getSheetForWorkBook(workbook,
                        type + "_" + invoice.getId() + "_" + SCMUtil.getCurrentTimeISTStringWithNoColons());
                sheet.setDefaultColumnWidth(20);
                CellStyle style = generateHeaderStyle(workbook);
                Row header = sheet.createRow(0);
                header.createCell(0).setCellValue("Dispatch Date");
                header.getCell(0).setCellStyle(style);
                header.createCell(1).setCellValue("Sending Unit");
                header.getCell(1).setCellStyle(style);
                header.createCell(2).setCellValue("Sending Unit GSTIN");
                header.getCell(2).setCellStyle(style);
                header.createCell(3).setCellValue("Sending Unit Address");
                header.getCell(3).setCellStyle(style);
                header.createCell(4).setCellValue("Sending Unit Pin code");
                header.getCell(4).setCellStyle(style);
                header.createCell(5).setCellValue("Sending Unit State");
                header.getCell(5).setCellStyle(style);
                header.createCell(6).setCellValue("Receiving Unit");
                header.getCell(6).setCellStyle(style);
                header.createCell(7).setCellValue("Receiving Unit GSTIN");
                header.getCell(7).setCellStyle(style);
                header.createCell(8).setCellValue("Receiving Unit Address");
                header.getCell(8).setCellStyle(style);
                header.createCell(9).setCellValue("Receiving Unit Pin code");
                header.getCell(9).setCellStyle(style);
                header.createCell(10).setCellValue("Receiving Unit State");
                header.getCell(10).setCellStyle(style);
                if (invoice.getVehicle().getTransportMode().equals(TransportMode.ROAD.name())) {
                    header.createCell(11).setCellValue("Vehicle Name");
                    header.getCell(11).setCellStyle(style);
                    header.createCell(12).setCellValue("Vehicle Number");
                    header.getCell(12).setCellStyle(style);
                } else {
                    header.createCell(11).setCellValue("Transporter GSTIN");
                    header.getCell(11).setCellStyle(style);
                    header.createCell(12).setCellValue("Docket Number");
                    header.getCell(12).setCellStyle(style);
                }
                header.createCell(13).setCellValue("TYPE");
                header.getCell(13).setCellStyle(style);
                if(SalesPerformaCorrectedType.CREDIT_NOTE.name().equals(type)) {
                    header.createCell(14).setCellValue("Generated Credit Note Id");
                    header.getCell(14).setCellStyle(style);
                } else {
                    header.createCell(14).setCellValue("Generated Debit Note Id");
                    header.getCell(14).setCellStyle(style);
                }
                header.createCell(15).setCellValue("Invoice Status");
                header.getCell(15).setCellStyle(style);
                header.createCell(16).setCellValue("Sku Name");
                header.getCell(16).setCellStyle(style);
                header.createCell(17).setCellValue("Original Quantity");
                header.getCell(17).setCellStyle(style);
                header.createCell(18).setCellValue("Revised Quantity");
                header.getCell(18).setCellStyle(style);
                header.createCell(19).setCellValue("Price");
                header.getCell(19).setCellStyle(style);
                header.createCell(20).setCellValue("Revised Price");
                header.getCell(20).setCellStyle(style);
                header.createCell(21).setCellValue("Tax");
                header.getCell(21).setCellStyle(style);
                header.createCell(22).setCellValue("HSN");
                header.getCell(22).setCellStyle(style);
                header.createCell(23).setCellValue("Billing Address");
                header.getCell(23).setCellStyle(style);
                if(SalesPerformaType.B2B_RETURN.equals(invoice.getType())) {
                    header.createCell(24).setCellValue("Reference Invoice No.");
                    header.getCell(24).setCellStyle(style);
                } else {
                    header.createCell(24).setCellValue("Purchase Order No.");
                    header.getCell(24).setCellStyle(style);
                }
                header.createCell(25).setCellValue("Invoice No");
                header.getCell(25).setCellStyle(style);

                if(SalesPerformaType.B2B_RETURN.equals(invoice.getType()) || SalesPerformaType.RETURN_TO_VENDOR.equals(invoice.getType())) {
                    List<SalesPerformaInvoiceItem> items = invoice.getItems();
                    for(int i = 0; i<items.size(); i++) {
                        SalesPerformaInvoiceItem item = items.get(i);
                        Row itemRow = sheet.createRow(i + 1);
                        if (i == 0) {
                            setMetaDataForCorrected(itemRow, invoice, type);
                        }
                        itemRow.createCell(16, STRING).setCellValue(item.getSku().getName());
                        itemRow.createCell(17, STRING).setCellValue(item.getPkgQty().doubleValue());
                        itemRow.createCell(18, STRING).setCellValue(item.getQty().doubleValue());
                        itemRow.createCell(19, NUMERIC).setCellValue(item.getPkgPrice().doubleValue());
                        itemRow.createCell(20, NUMERIC).setCellValue(item.getSellPrice().doubleValue());
                        itemRow.createCell(21, NUMERIC).setCellValue(item.getTotalTax().doubleValue());
                        itemRow.createCell(22, STRING).setCellValue(item.getSku().getCode());
                    }
                } else if(SalesPerformaCorrectedType.CREDIT_NOTE.name().equals(type)) {
                    CorrectedSalesInvoiceDetails correctionCreditNoteDetail = invoice.getCorrectionCreditNoteDetail();
                    for (int i = 0; i < correctionCreditNoteDetail.getSalesPerformaCorrectedItems().size(); i++) {
                        Row itemRow = sheet.createRow(i + 1);
                        CorrectedSalesInvoiceItemDetails invoiceItem = correctionCreditNoteDetail.getSalesPerformaCorrectedItems().get(i);
                        if (i == 0) {
                            setMetaDataForCorrected(itemRow, invoice, type);
                        }
                        itemRow.createCell(16, STRING).setCellValue(invoiceItem.getSkuName());
                        itemRow.createCell(17, STRING).setCellValue(invoiceItem.getPkgQty().doubleValue());
                        itemRow.createCell(18, STRING).setCellValue(invoiceItem.getRevisedPkgQty().doubleValue());
                        itemRow.createCell(19, NUMERIC).setCellValue(invoiceItem.getPrice().doubleValue());
                        itemRow.createCell(20, NUMERIC).setCellValue(invoiceItem.getRevisedPrice().doubleValue());
                        itemRow.createCell(21, NUMERIC).setCellValue(invoiceItem.getTax().doubleValue());
                        itemRow.createCell(22, NUMERIC).setCellValue(invoiceItem.getTaxCode());
                    }
                } else {
                    CorrectedSalesInvoiceDetails correctionDebitNoteDetail = invoice.getCorrectionDebitNoteDetail();
                    for (int i = 0; i < correctionDebitNoteDetail.getSalesPerformaCorrectedItems().size(); i++) {
                        Row itemRow = sheet.createRow(i + 1);
                        CorrectedSalesInvoiceItemDetails invoiceItem = correctionDebitNoteDetail.getSalesPerformaCorrectedItems().get(i);
                        if (i == 0) {
                            setMetaDataForCorrected(itemRow, invoice, type);
                        }
                        itemRow.createCell(16, STRING).setCellValue(invoiceItem.getSkuName());
                        itemRow.createCell(17, STRING).setCellValue(invoiceItem.getPkgQty().doubleValue());
                        itemRow.createCell(18, STRING).setCellValue(invoiceItem.getRevisedPkgQty().doubleValue());
                        itemRow.createCell(19, NUMERIC).setCellValue(invoiceItem.getPrice().doubleValue());
                        itemRow.createCell(20, NUMERIC).setCellValue(invoiceItem.getRevisedPrice().doubleValue());
                        itemRow.createCell(21, NUMERIC).setCellValue(invoiceItem.getTax().doubleValue());
                        itemRow.createCell(22, NUMERIC).setCellValue(invoiceItem.getTaxCode());
                    }
                }
            }
        };
    }

    private void setMetaData(Row itemRow, SalesPerformaInvoice invoice) {
        Unit sendingUnit = dataCache.getUnit(invoice.getSendingUnit().getId());
        Address sourceUnitAddress = sendingUnit.getAddress();
        VendorDetail vendorDetail = scmCache.getVendorDetail(invoice.getVendor().getId());
        if (vendorDetail != null) {
            Optional<VendorDispatchLocation> location = vendorDetail.getDispatchLocations().stream()
                    .filter(loc -> loc.getDispatchId().equals(invoice.getDispatchLocation().getId()))
                    .findFirst();
            location.ifPresent(dispatchLocation -> {
                AddressDetail destLocationAddress = dispatchLocation.getAddress();
                itemRow.createCell(0, STRING).setCellValue(SCMUtil.getFormattedDate(invoice.getDispatchDate()));
                itemRow.createCell(1, STRING).setCellValue(sendingUnit.getName());
                itemRow.createCell(2, STRING).setCellValue(sendingUnit.getTin());
                itemRow.createCell(3, STRING).setCellValue(EWayHelper.getAddress1(sourceUnitAddress));
                itemRow.createCell(4, STRING).setCellValue(sourceUnitAddress.getZipCode());
                itemRow.createCell(5, STRING).setCellValue(sendingUnit.getLocation().getState().getCode());
                itemRow.createCell(6, STRING).setCellValue(vendorDetail.getEntityName() + " [" + dispatchLocation.getLocationName() + "]");
                itemRow.createCell(7, STRING).setCellValue(dispatchLocation.getGstin());
                itemRow.createCell(8, STRING).setCellValue(EWayHelper.getAddress(destLocationAddress));
                itemRow.createCell(9, STRING).setCellValue(destLocationAddress.getZipcode());
                itemRow.createCell(10, STRING).setCellValue(destLocationAddress.getStateCode());
                if (invoice.getVehicle().getTransportMode().equals(TransportMode.ROAD.name())) {
                    itemRow.createCell(11, STRING).setCellValue(invoice.getVehicle().getName());
                    itemRow.createCell(12, STRING).setCellValue(invoice.getVehicle().getRegistrationNumber());
                } else {
                    itemRow.createCell(11, STRING).setCellValue(invoice.getVehicle().getRegistrationNumber());
                    itemRow.createCell(12, STRING).setCellValue(invoice.getDocketNumber());
                }
                if (invoice.getBillingAddress()!=null) {
                    itemRow.createCell(24, STRING)
                            .setCellValue(invoice.getBillingAddress());
                }
                if(invoice.getPurchasedOrderNumber()!=null){
                    itemRow.createCell(25, STRING).setCellValue(invoice.getPurchasedOrderNumber());
                }
                if(invoice.getId()!=null){
                    itemRow.createCell(28, STRING).setCellValue(invoice.getId());
                }
            });
        }
    }

    private void setMetaDataForCorrected(Row itemRow, SalesPerformaInvoice invoice, String type) {
        Unit sendingUnit = dataCache.getUnit(invoice.getSendingUnit().getId());
        Address sourceUnitAddress = sendingUnit.getAddress();
        VendorDetail vendorDetail = scmCache.getVendorDetail(invoice.getVendor().getId());
        if (vendorDetail != null) {
            Optional<VendorDispatchLocation> location = vendorDetail.getDispatchLocations().stream()
                    .filter(loc -> loc.getDispatchId().equals(invoice.getDispatchLocation().getId()))
                    .findFirst();
            location.ifPresent(dispatchLocation -> {
                AddressDetail destLocationAddress = dispatchLocation.getAddress();
                itemRow.createCell(0, STRING).setCellValue(SCMUtil.formatDate(invoice.getDispatchDate(), "dd/MM/yyyy"));
                itemRow.createCell(1, STRING).setCellValue(sendingUnit.getName());
                itemRow.createCell(2, STRING).setCellValue(sendingUnit.getTin());
                itemRow.createCell(3, STRING).setCellValue(EWayHelper.getAddress1(sourceUnitAddress));
                itemRow.createCell(4, STRING).setCellValue(sourceUnitAddress.getZipCode());
                itemRow.createCell(5, STRING).setCellValue(sendingUnit.getLocation().getState().getCode());
                itemRow.createCell(6, STRING).setCellValue(vendorDetail.getEntityName() + " [" + dispatchLocation.getLocationName() + "]");
                itemRow.createCell(7, STRING).setCellValue(dispatchLocation.getGstin());
                itemRow.createCell(8, STRING).setCellValue(EWayHelper.getAddress(destLocationAddress));
                itemRow.createCell(9, STRING).setCellValue(destLocationAddress.getZipcode());
                itemRow.createCell(10, STRING).setCellValue(destLocationAddress.getStateCode());
                if (invoice.getVehicle().getTransportMode().equals(TransportMode.ROAD.name())) {
                    itemRow.createCell(11, STRING).setCellValue(invoice.getVehicle().getName());
                    itemRow.createCell(12, STRING).setCellValue(invoice.getVehicle().getRegistrationNumber());
                } else {
                    itemRow.createCell(11, STRING).setCellValue(invoice.getVehicle().getRegistrationNumber());
                    itemRow.createCell(12, STRING).setCellValue(invoice.getDocketNumber());
                }

                if(SalesPerformaType.B2B_RETURN.equals(invoice.getType())) {
                    itemRow.createCell(13, STRING).setCellValue(SalesPerformaCorrectedType.CREDIT_NOTE.value());
                    itemRow.createCell(14, STRING).setCellValue(invoice.getGeneratedCreditNoteId());
                    itemRow.createCell(15, STRING).setCellValue(invoice.getStatus().name());
                } else if(SalesPerformaType.RETURN_TO_VENDOR.equals(invoice.getType())) {
                    itemRow.createCell(13, STRING).setCellValue(SalesPerformaCorrectedType.DEBIT_NOTE.value());
                    itemRow.createCell(14, STRING).setCellValue(invoice.getGeneratedDebitNoteId());
                    itemRow.createCell(15, STRING).setCellValue(invoice.getStatus().name());
                } else if(SalesPerformaCorrectedType.CREDIT_NOTE.name().equals(type)) {
                    itemRow.createCell(13, STRING).setCellValue(invoice.getCorrectionCreditNoteDetail().getType());
                    itemRow.createCell(14, STRING).setCellValue(invoice.getCorrectionCreditNoteDetail().getGeneratedCreditNoteId());
                    itemRow.createCell(15, STRING).setCellValue(invoice.getCorrectionCreditNoteDetail().getInvoiceStatus());
                } else {
                    itemRow.createCell(13, STRING).setCellValue(invoice.getCorrectionDebitNoteDetail().getType());
                    itemRow.createCell(14, STRING).setCellValue(invoice.getCorrectionDebitNoteDetail().getGeneratedDebitNoteId());
                    itemRow.createCell(15, STRING).setCellValue(invoice.getCorrectionDebitNoteDetail().getInvoiceStatus());
                }

                if (invoice.getBillingAddress()!=null) {
                    itemRow.createCell(23, STRING).setCellValue(invoice.getBillingAddress());
                }
                if(SalesPerformaType.B2B_RETURN.equals(invoice.getType())) {
                    itemRow.createCell(24, STRING).setCellValue(invoice.getReferenceInvoiceNumber());
                } else {
                    itemRow.createCell(24, NUMERIC).setCellValue(invoice.getPurchasedOrderNumber());
                }

                if(invoice.getId()!=null){
                    itemRow.createCell(25, STRING).setCellValue(invoice.getId());
                }
            });
        }
    }

    public View generateCapexFIle(CapexRequestDetailData capexRequestDetailData) {

        View view = new AbstractXlsxView() {
            @Override
            protected Workbook createWorkbook(Map<String, Object> model,
                                              HttpServletRequest request) {
                CapexTemplateData capexTemplateData = scmCache.getCapexTemplate(capexRequestDetailData.getType());
                FileDetail fileDetail = new FileDetail(capexTemplateData.getBucket(), capexTemplateData.getKey(),
                        capexTemplateData.getUrl() + "/" + capexTemplateData.getBucket() + "/" + capexTemplateData.getKey());
                File file = fileArchiveService.getFileFromS3(props.getBasePath() + File.separator + "s3", fileDetail);
                FileInputStream fis = null;
                try {
                    fis = new FileInputStream(file);
                } catch (FileNotFoundException e) {
                    LOG.error("Error in Downloading File from S3", e);
                } //Read the spreadsheet that needs to be updated
                XSSFWorkbook workbook = null;
                try {
                    workbook = new XSSFWorkbook(fis);
                } catch (IOException e) {
                    LOG.error("Error in Downloading File from S3", e);
                }
                XSSFSheet sheet = workbook.getSheetAt(0);
                sheet.enableLocking();
                XSSFRow row1 = sheet.getRow(0);
                XSSFCell cell1 = row1.getCell(1);
                cell1.setCellValue(capexRequestDetailData.getId());
                XSSFRow row2 = sheet.getRow(1);
                XSSFCell cell2 = row2.getCell(1);
                cell2.setCellValue(capexRequestDetailData.getUnitId());
                XSSFRow row3 = sheet.getRow(2);
                XSSFCell cell3 = row3.getCell(1);
                cell3.setCellValue(capexRequestDetailData.getUnitName());
                XSSFRow row4 = sheet.getRow(3);
                XSSFCell cell4 = row4.getCell(1);
                cell4.setCellValue(capexRequestDetailData.getType());
                XSSFRow row5 = sheet.getRow(4);
                XSSFCell cell5 = row5.getCell(1);
                cell5.setCellValue(AppUtils.getSMSTemplateDate(SCMUtil.getCurrentTimestamp()));
                XSSFRow row6 = sheet.getRow(5);
                XSSFCell cell6 = row6.getCell(1);
                cell6.setCellValue(capexRequestDetailData.getGeneratedBy());
                XSSFRow row7 = sheet.getRow(6);
                XSSFCell cell7 = row7.getCell(1);
                cell7.setCellValue(capexRequestDetailData.getAccessKey());
                XSSFRow row8 = sheet.getRow(7);
                XSSFCell cell8 = row8.getCell(1);
                cell8.setCellValue(capexRequestDetailData.getTemplateVersion());
                XSSFRow row9 = sheet.getRow(8);
                if(row9!=null){
                    XSSFCell cell9 = row9.getCell(1);
                    cell9.setCellValue("Yes");
                }


                XSSFSheet sheet1 = workbook.getSheetAt(1);
                sheet1.enableLocking();
                workbook.setSheetName(workbook.getSheetIndex(sheet1),
                        String.valueOf(capexRequestDetailData.getUnitId()) + "-" + capexRequestDetailData.getUnitName());
                workbook.getSheetAt(2).enableLocking();
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                try {
                    workbook.write(bos);
                } catch (IOException e1) {
                    LOG.error("Error in Saving File in S3", e1);
                }
                byte[] barray = bos.toByteArray();
                InputStream is = new ByteArrayInputStream(barray);
                MultipartFile multipartFile = null;
                try {
                    multipartFile = new MockMultipartFile(capexTemplateData.getKey(), is);
                } catch (Exception e) {
                    LOG.error("Error in Saving File in S3", e);
                }
                String fileName = getCapexUploadFileName(capexRequestDetailData.getUnitName(), capexRequestDetailData.getId(), MimeType.XLSX, capexRequestDetailData.getTemplateVersion());
                saveCapexFile(fileName, multipartFile, capexTemplateData.getBucket());
                return workbook;

            }

            private void saveCapexFile(String fileName, MultipartFile multipartFile, String capexTemplateBucket) {
                FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), capexTemplateBucket, fileName, multipartFile, true);
            }

            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.setHeader("Content-Disposition", getCapexUploadFileName(capexRequestDetailData.getUnitName(), capexRequestDetailData.getId(), MimeType.XLSX, capexRequestDetailData.getTemplateVersion()));
            }
        };
        return view;
    }

    public static String getCapexUploadFileName(String unitName, Integer requestId, MimeType fileExtension, String version) {
        return "Capex_Request" + "-" + unitName + "-" + version + "-" + requestId + "." + fileExtension.name().toLowerCase();
    }

    public static String getUploadProjectionsFileName( MimeType fileExtension) {
        return "PRODUCT_PROJECTIONS" + "-" + AppUtils.getCurrentTimestamp() + "." + fileExtension.name().toLowerCase();
    }

    public View getCapexFile(CapexAuditDetail capexAuditDetail) {

        View view = new AbstractXlsxView() {
            @Override
            protected Workbook createWorkbook(Map<String, Object> model,
                                              HttpServletRequest request) {
                String fileName = getCapexUploadFileName(capexAuditDetail.getUnitName(), capexAuditDetail.getCapexRequestId(), MimeType.XLSX, capexAuditDetail.getVersion());
                CapexTemplateData capexTemplateData = scmCache.getCapexTemplate("Renovation");
                FileDetail fileDetail = new FileDetail(props.getS3Bucket(), capexTemplateData.getBucket() + "/" + fileName,
                        capexTemplateData.getUrl() + "/" + "chaayosdevtest/" + capexTemplateData.getBucket() + "/" + fileName);
                File file = fileArchiveService.getFileFromS3(props.getBasePath() + File.separator + "s3", fileDetail);
                FileInputStream fis = null;
                try {
                    fis = new FileInputStream(file);
                } catch (FileNotFoundException e) {
                    LOG.error("Error in Downloading File from S3", e);
                } //Read the spreadsheet that needs to be updated
                XSSFWorkbook workbook = null;
                try {
                    workbook = new XSSFWorkbook(fis);
                } catch (IOException e) {
                    LOG.error("Error in Downloading File from S3", e);
                }
                return workbook;

            }

            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.setHeader("Content-Disposition", getCapexUploadFileName(capexAuditDetail.getUnitName(), capexAuditDetail.getCapexRequestId(), MimeType.XLSX, capexAuditDetail.getVersion()));
            }
        };
        return view;

    }

    public View getNewCapexFile(CapexAuditDetailData capexAuditDetailData, CapexAuditDetail capexAuditDetail) {

        View view = new AbstractXlsxView() {
            @Override
            protected Workbook createWorkbook(Map<String, Object> model,
                                              HttpServletRequest request) {
                String fileName = getCapexUploadFileName(capexAuditDetail.getUnitName(), capexAuditDetailData.getCapexRequestId(), MimeType.XLSX, capexAuditDetail.getVersion());
                CapexTemplateData capexTemplateData = scmCache.getCapexTemplate("Renovation");
                FileDetail fileDetail = new FileDetail(props.getS3Bucket(), capexTemplateData.getBucket() + "/" + fileName,
                        capexTemplateData.getUrl() + "/" + "chaayosdevtest/" + capexTemplateData.getBucket() + "/" + fileName);
                File file = fileArchiveService.getFileFromS3(props.getBasePath() + File.separator + "s3", fileDetail);
                FileInputStream fis = null;
                try {
                    fis = new FileInputStream(file);
                } catch (FileNotFoundException e) {
                    LOG.error("Error in Downloading File from S3", e);
                } //Read the spreadsheet that needs to be updated
                XSSFWorkbook workbook = null;
                try {
                    workbook = new XSSFWorkbook(fis);
                } catch (IOException e) {
                    LOG.error("Error in Downloading File from S3", e);
                }
                XSSFSheet sheet = workbook.getSheetAt(0);
                sheet.enableLocking();
                XSSFRow row5 = sheet.getRow(4);
                XSSFCell cell5 = row5.getCell(1);
                cell5.setCellValue(AppUtils.getSMSTemplateDate(SCMUtil.getCurrentTimestamp()));
                XSSFRow row6 = sheet.getRow(5);
                XSSFCell cell6 = row6.getCell(1);
                cell6.setCellValue(Integer.parseInt(capexAuditDetailData.getDownloadedBy()));
                XSSFRow row7 = sheet.getRow(6);
                XSSFCell cell7 = row7.getCell(1);
                cell7.setCellValue(capexAuditDetailData.getAccessKey());
                XSSFRow row8 = sheet.getRow(7);
                XSSFCell cell8 = row8.getCell(1);
                cell8.setCellValue(capexAuditDetailData.getVersion());

                XSSFSheet sheet1 = workbook.getSheetAt(1);
                sheet.enableLocking();
                //code to get the actual template in the s3 bucket
                XSSFWorkbook workbookActual = null;
                FileDetail fileDetailActual = new FileDetail(capexTemplateData.getBucket(), capexTemplateData.getKey(),
                        capexTemplateData.getUrl() + "/" + capexTemplateData.getBucket() + "/" + capexTemplateData.getKey());
                File fileActual = fileArchiveService.getFileFromS3(props.getBasePath() + File.separator + "s3", fileDetailActual);
                FileInputStream fisActual = null;
                try {
                    fisActual = new FileInputStream(fileActual);
                } catch (FileNotFoundException e) {
                    LOG.error("Error in Downloading Actual template File from S3", e);
                } //Read the spreadsheet that needs to be updated
                try {
                    workbookActual = new XSSFWorkbook(fisActual);
                } catch (IOException e) {
                    LOG.error("Error in Downloading Actual template File from S3", e);
                }

                XSSFSheet sheet1Actual = workbookActual.getSheetAt(1);
                Map<String, Double> currentFileDepartmentData = new HashMap<>();
                Boolean check = false;
                if(sheet1.getLastRowNum() != sheet1Actual.getLastRowNum()){
                    check = true;
                    for(int i = 4; i < sheet1.getLastRowNum(); i++){
                        XSSFRow currentRow = sheet1.getRow(i);
                        String departmentName = currentRow.getCell(2).getStringCellValue();
                        Double amount = currentRow.getCell(3).getNumericCellValue();
                        currentFileDepartmentData.put(departmentName,amount);
                    }
                    XSSFSheet actualSheet = workbookActual.getSheetAt(0);
                    actualSheet.enableLocking();
                    XSSFRow row1 = actualSheet.getRow(0);
                    XSSFCell cell1 = row1.getCell(1);
                    cell1.setCellValue((int)sheet.getRow(0).getCell(1).getNumericCellValue());
                    XSSFRow row2 = actualSheet.getRow(1);
                    XSSFCell cell2 = row2.getCell(1);
                    cell2.setCellValue((int)sheet.getRow(1).getCell(1).getNumericCellValue());
                    XSSFRow row3 = actualSheet.getRow(2);
                    XSSFCell cell3 = row3.getCell(1);
                    cell3.setCellValue(sheet.getRow(2).getCell(1).getStringCellValue());
                    XSSFRow row4 = actualSheet.getRow(3);
                    XSSFCell cell4 = row4.getCell(1);
                    cell4.setCellValue(sheet.getRow(3).getCell(1).getStringCellValue());
                    XSSFRow row5Actual = actualSheet.getRow(4);
                    XSSFCell cell5Actual = row5Actual.getCell(1);
                    cell5Actual.setCellValue(AppUtils.getSMSTemplateDate(SCMUtil.getCurrentTimestamp()));
                    XSSFRow row6Actual = actualSheet.getRow(5);
                    XSSFCell cell6Actual= row6Actual.getCell(1);
                    cell6Actual.setCellValue(Integer.parseInt(capexAuditDetailData.getDownloadedBy()));
                    XSSFRow row7Actual = actualSheet.getRow(6);
                    XSSFCell cell7Actual = row7Actual.getCell(1);
                    cell7Actual.setCellValue(capexAuditDetailData.getAccessKey());
                    XSSFRow row8Actual = actualSheet.getRow(7);
                    XSSFCell cell8Actual = row8Actual.getCell(1);
                    cell8Actual.setCellValue(capexAuditDetailData.getVersion());

                    String sheetName = sheet1.getSheetName();
                    workbookActual.setSheetName(workbookActual.getSheetIndex(sheet1Actual),sheetName);

                    for(int i = 4; i < sheet1Actual.getLastRowNum(); i++){
                        String name = sheet1Actual.getRow(i).getCell(2).getStringCellValue();
                        if(currentFileDepartmentData.containsKey(name)){
                            sheet1Actual.getRow(i).getCell(3).setCellValue(currentFileDepartmentData.get(name).intValue());
                        }
                        else{
                            LOG.info("missing department name is : {}",name);
                        }
                    }
                }

                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                try {
                    if(check){
                        sheet1Actual.setForceFormulaRecalculation(true);
                        workbookActual.write(bos);
                    }
                    else{
                        sheet1.setForceFormulaRecalculation(true);
                        workbook.write(bos);
                    }
                } catch (IOException e1) {
                    LOG.error("Error in Saving File in S3", e1);
                }
                byte[] barray = bos.toByteArray();
                InputStream is = new ByteArrayInputStream(barray);
                MultipartFile multipartFile = null;
                try {
                    multipartFile = new MockMultipartFile(capexTemplateData.getKey(), is);
                } catch (Exception e) {
                    LOG.error("Error in Saving File in S3", e);
                }
                String fileNewName = getCapexUploadFileName(capexAuditDetail.getUnitName(), capexAuditDetail.getCapexRequestId(), MimeType.XLSX, capexAuditDetailData.getVersion());
                saveCapexFile(fileNewName, multipartFile, capexTemplateData.getBucket());
                return check ? workbookActual : workbook;
            }

            private void saveCapexFile(String fileName, MultipartFile multipartFile, String capexTemplateBucket) {
                FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), capexTemplateBucket, fileName, multipartFile, true);
            }

            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.setHeader("Content-Disposition", getCapexUploadFileName(capexAuditDetail.getUnitName(), capexAuditDetailData.getCapexRequestId(), MimeType.XLSX, capexAuditDetailData.getVersion()));
            }
        };
        return view;

    }

    private XSSFSheet createSheetForSummary(XSSFWorkbook workbook ,XSSFSheet sheet,Integer rowCount ){
        List<String> columns = Arrays.asList("Cost Element","Business Cost Center" , "Department", "Description" , "UOM" , "Tax Rate" , "Amount" , "Amount With Tax" );
        return createHeaderRow(workbook,sheet,rowCount,columns);
    }



    private XSSFSheet createHeaderRow(Workbook workbook , XSSFSheet sheet , Integer rowCount , List<String> columns){
        CellStyle style = generateHeaderStyle(workbook);
        XSSFRow row = sheet.createRow(rowCount);
        Integer col = 0;
        for(String colName : columns){
            row.createCell(col).setCellValue(colName);
            row.getCell(col).setCellStyle(style);
            col++;
        }
        return  sheet;
    }

    private XSSFSheet createSheetForMB(XSSFWorkbook workbook , XSSFSheet sheet , Integer rowCount){
        List<String> columns = Arrays.asList("DEPARTMENT","Category","SubCategory_name","Measurement_description",
                "Source_UOM","Nos","Length" , "Width" , "Height" , "Length (Captured)" , "Width (Captured)",
                "Height(Captured)" , "Qty" , "Unit Price" ,"Amount" , "Amount with Tax"  );
        return createHeaderRow(workbook,sheet,rowCount,columns);
    }

    private XSSFSheet createSheet(XSSFWorkbook workbook ,String type,Boolean isRecipeRequired) {
        //LOG.info("creating sheet for type : {}",type);
        CellStyle style = generateHeaderStyle(workbook);
        XSSFSheet sheet = workbook.createSheet();
        sheet.setDefaultColumnWidth(20);
        XSSFRow row = sheet.createRow(0);
        row.createCell(0).setCellValue("Start Date");
        row.getCell(0).setCellStyle(style);
        row.createCell(1).setCellValue("End Date");
        row.getCell(1).setCellStyle(style);
        if (type.equalsIgnoreCase("SCMUnitInput")) {
            row.createCell(2).setCellValue("Brand Code");
            row.getCell(2).setCellStyle(style);
            row.createCell(3).setCellValue("Unit Id");
            row.getCell(3).setCellStyle(style);
            row.createCell(4).setCellValue("Unit Name");
            row.getCell(4).setCellStyle(style);
            row.createCell(5).setCellValue("Unit Cost Center");
            row.getCell(5).setCellStyle(style);
            row.createCell(6).setCellValue("Unit Status");
            row.getCell(6).setCellStyle(style);
            row.createCell(7).setCellValue("Cafe Opening Date");
            row.getCell(7).setCellStyle(style);
            row.createCell(8).setCellValue("Clone Unit Id");
            row.getCell(8).setCellStyle(style);
        }
        else if (type.equalsIgnoreCase("MenuProductOutput")) {
            row.createCell(2).setCellValue("Brand Code");
            row.getCell(2).setCellStyle(style);
            row.createCell(3).setCellValue("Order Source");
            row.getCell(3).setCellStyle(style);
            row.createCell(4).setCellValue("Unit Id");
            row.getCell(4).setCellStyle(style);
            row.createCell(5).setCellValue("Unit Name");
            row.getCell(5).setCellStyle(style);
            row.createCell(6).setCellValue("Unit Cost Center");
            row.getCell(6).setCellStyle(style);
            row.createCell(7).setCellValue("Unit Status");
            row.getCell(7).setCellStyle(style);
            row.createCell(8).setCellValue("Cafe Opening Date");
            row.getCell(8).setCellStyle(style);
            row.createCell(9).setCellValue("Business Date");
            row.getCell(9).setCellStyle(style);
            row.createCell(10).setCellValue("Menu Product Id");
            row.getCell(10).setCellStyle(style);
            row.createCell(11).setCellValue("Menu Product Name");
            row.getCell(11).setCellStyle(style);
            row.createCell(12).setCellValue("Menu Product Category");
            row.getCell(12).setCellStyle(style);
            row.createCell(13).setCellValue("Menu Product Subcategory");
            row.getCell(13).setCellStyle(style);
            row.createCell(14).setCellValue("Dimension");
            row.getCell(14).setCellStyle(style);
            row.createCell(15).setCellValue("Quantity");
            row.getCell(15).setCellStyle(style);
            row.createCell(16).setCellValue("Price");
            row.getCell(16).setCellStyle(style);
            row.createCell(17).setCellValue("Sales");
            row.getCell(17).setCellStyle(style);
            row.createCell(18).setCellValue("Recipe Id");
            row.getCell(18).setCellStyle(style);
        }
        else {
            row.createCell(2).setCellValue("Unit Id");
            row.getCell(2).setCellStyle(style);
            row.createCell(3).setCellValue("Unit Name");
            row.getCell(3).setCellStyle(style);
            row.createCell(4).setCellValue("Unit Cost Center");
            row.getCell(4).setCellStyle(style);
            row.createCell(5).setCellValue("Unit Status");
            row.getCell(5).setCellStyle(style);
            row.createCell(6).setCellValue("Cafe Opening Date");
            row.getCell(6).setCellStyle(style);
//            row.createCell(7).setCellValue("Business Date");
//            row.getCell(7).setCellStyle(style);
            row.createCell(7).setCellValue("Fulfillment unit");
            row.getCell(7).setCellStyle(style);
            row.createCell(8).setCellValue("SCM Product Id");
            row.getCell(8).setCellStyle(style);
            row.createCell(9).setCellValue("SCM Product Name");
            row.getCell(9).setCellStyle(style);
            row.createCell(10).setCellValue("SCM Product Category");
            row.getCell(10).setCellStyle(style);
            row.createCell(11).setCellValue("SCM Product Subcategory");
            row.getCell(11).setCellStyle(style);
            row.createCell(12).setCellValue("UOM");
            row.getCell(12).setCellStyle(style);
            row.createCell(13).setCellValue("Quantity");
            row.getCell(13).setCellStyle(style);
            if (isRecipeRequired) {
                row.createCell(14).setCellValue("Recipe Id");
                row.getCell(14).setCellStyle(style);
            }
        }
        return sheet;
    }

    public View generateProductProjections(List<ProductProjectionsUnitDetail> unitsInputData, List<ProductProjectionsUnitDetail> menuOutput,
                                           List<ProductProjectionsUnitDetail> scmOutput, List<ProductProjectionsUnitDetail> detailedScmOutput,
                                           ProductProjectionsDetailsData projectionsDetailsData, Set<ProductProjectionsUnitDetail> duplicateUnitsDataList) {
        View view = new AbstractXlsxView() {
            @Override
            protected Workbook createWorkbook(Map<String, Object> model,
                                              HttpServletRequest request) {
                long startOfExcelGeneration = System.currentTimeMillis();
                XSSFWorkbook workbook = new XSSFWorkbook();
                XSSFSheet sheet = createSheet(workbook,"SCMUnitInput",false);
                workbook.setSheetName(0,"Scm Unit Input");
                //LOG.info("size is {}",unitsInputData.size());
                for (int i=0;i<unitsInputData.size();i++){
                    XSSFRow rows = sheet.createRow(i+1);
                    setDate(workbook,rows.createCell(0) , unitsInputData.get(i).getStartDate(), "dd-MM-yyyy");
                    setDate(workbook,rows.createCell(1) , unitsInputData.get(i).getEndDate(), "dd-MM-yyyy");
                    rows.createCell(2).setCellValue(unitsInputData.get(i).getBrandCode());
                    rows.createCell(3).setCellValue(unitsInputData.get(i).getUnitId());
                    rows.createCell(4).setCellValue(unitsInputData.get(i).getUnitName());
                    rows.createCell(5).setCellValue(unitsInputData.get(i).getUnitCostCenter());
                    rows.createCell(6).setCellValue(unitsInputData.get(i).getStatus());
                    if(unitsInputData.get(i).getCafeOpeningDate() != null) {
                        setDate(workbook,rows.createCell(7) , unitsInputData.get(i).getCafeOpeningDate(), "dd/MM/yyyy");
                    }
                    if(unitsInputData.get(i).getSalesClonedFrom() != null) {
                        rows.createCell(8).setCellValue(unitsInputData.get(i).getSalesClonedFrom());
                    }
                }

                XSSFSheet sheet1 = createSheet(workbook,"MenuProductOutput",false);
                workbook.setSheetName(1,"Menu Product Projections");
                for (int i=0;i<menuOutput.size();i++) {
                    XSSFRow rows = sheet1.createRow(i+1);
                    setDate(workbook, rows.createCell(0), menuOutput.get(i).getStartDate(), "dd-MM-yyyy");
                    setDate(workbook, rows.createCell(1), menuOutput.get(i).getEndDate(), "dd-MM-yyyy");
                    rows.createCell(2).setCellValue(menuOutput.get(i).getBrandCode());
                    rows.createCell(3).setCellValue(menuOutput.get(i).getOrderSource());
                    rows.createCell(4).setCellValue(menuOutput.get(i).getUnitId());
                    rows.createCell(5).setCellValue(menuOutput.get(i).getUnitName());
                    rows.createCell(6).setCellValue(menuOutput.get(i).getUnitCostCenter());
                    rows.createCell(7).setCellValue(menuOutput.get(i).getStatus());
                    if (menuOutput.get(i).getCafeOpeningDate() != null) {
                        setDate(workbook, rows.createCell(8), menuOutput.get(i).getCafeOpeningDate(), "dd/MM/yyyy");
                    }
                    if (menuOutput.get(i).getBusinessDate() != null ) {
                        setDate(workbook, rows.createCell(9), menuOutput.get(i).getBusinessDate(), "dd/MM/yyyy");
                    }
                    if (menuOutput.get(i).getMenuProductId() != null) {
                        rows.createCell(10).setCellValue(menuOutput.get(i).getMenuProductId());
                    }
                    if (menuOutput.get(i).getMenuProductName() != null ) {
                        rows.createCell(11).setCellValue(menuOutput.get(i).getMenuProductName());
                    }
                    if (menuOutput.get(i).getMenuProductCategory() != null) {
                        rows.createCell(12).setCellValue(menuOutput.get(i).getMenuProductCategory());
                    }
                    if (menuOutput.get(i).getMenuProductSubCategory() != null ) {
                        rows.createCell(13).setCellValue(menuOutput.get(i).getMenuProductSubCategory());
                    }
                    if (menuOutput.get(i).getDimension() != null ) {
                        rows.createCell(14).setCellValue(menuOutput.get(i).getDimension());
                    }
                    if (menuOutput.get(i).getQuantity() != null ) {
                        rows.createCell(15).setCellValue(menuOutput.get(i).getQuantity().doubleValue());
                    }
                    if (menuOutput.get(i).getPrice() != null ) {
                        rows.createCell(16).setCellValue(menuOutput.get(i).getPrice().doubleValue());
                    }
                    if (menuOutput.get(i).getSales() != null) {
                        rows.createCell(17).setCellValue(menuOutput.get(i).getSales().doubleValue());
                    }
                    if (menuOutput.get(i).getRecipeId() != null) {
                        rows.createCell(18).setCellValue(menuOutput.get(i).getRecipeId());
                    }
                }

                XSSFSheet sheet2 = createSheet(workbook,"ScmProductOutput",true);
                workbook.setSheetName(2,"Scm Product Projections");
                for (int i=0;i<scmOutput.size();i++) {
                    XSSFRow rows = sheet2.createRow(i+1);
                    setDate(workbook, rows.createCell(0), scmOutput.get(i).getStartDate(), "dd-MM-yyyy");
                    setDate(workbook, rows.createCell(1), scmOutput.get(i).getEndDate(), "dd-MM-yyyy");
                    rows.createCell(2).setCellValue(scmOutput.get(i).getUnitId());
                    rows.createCell(3).setCellValue(scmOutput.get(i).getUnitName());
                    rows.createCell(4).setCellValue(scmOutput.get(i).getUnitCostCenter());
                    rows.createCell(5).setCellValue(scmOutput.get(i).getStatus());
                    if (scmOutput.get(i).getCafeOpeningDate() != null) {
                        setDate(workbook, rows.createCell(6), scmOutput.get(i).getCafeOpeningDate(), "dd/MM/yyyy");
                    }
//                    if (menuOutput.get(i).getBusinessDate() != null ) {
//                        setDate(workbook, rows.createCell(9), menuOutput.get(i).getBusinessDate(), "dd/MM/yyyy");
//                    }
                    if (scmOutput.get(i).getFulfillmentUnit() != null) {
                        rows.createCell(7).setCellValue(scmOutput.get(i).getFulfillmentUnit());
                    }
                    if (scmOutput.get(i).getScmProductId() != null) {
                        rows.createCell(8).setCellValue(scmOutput.get(i).getScmProductId());
                    }
                    if (scmOutput.get(i).getScmProductName() != null ) {
                        rows.createCell(9).setCellValue(scmOutput.get(i).getScmProductName());
                    }
                    if (scmOutput.get(i).getScmProductCategory() != null) {
                        rows.createCell(10).setCellValue(scmOutput.get(i).getScmProductCategory());
                    }
                    if (scmOutput.get(i).getScmProductSubCategory() != null) {
                        rows.createCell(11).setCellValue(scmOutput.get(i).getScmProductSubCategory());
                    }
                    if (scmOutput.get(i).getUom() != null ) {
                        rows.createCell(12).setCellValue(scmOutput.get(i).getUom());
                    }
                    if (scmOutput.get(i).getQuantity() != null ) {
                        rows.createCell(13).setCellValue(scmOutput.get(i).getQuantity().doubleValue());
                    }
                    if (scmOutput.get(i).getScmRecipeId() != null) {
                        rows.createCell(14).setCellValue(scmOutput.get(i).getScmRecipeId());
                    }
                }

                XSSFSheet sheet3 = createSheet(workbook,"DetailedScmProductOutput",false);
                workbook.setSheetName(3,"Detailed Scm Product Projections");
                for (int i=0;i<detailedScmOutput.size();i++) {
                    XSSFRow rows = sheet3.createRow(i+1);
                    setDate(workbook, rows.createCell(0), detailedScmOutput.get(i).getStartDate(), "dd-MM-yyyy");
                    setDate(workbook, rows.createCell(1), detailedScmOutput.get(i).getEndDate(), "dd-MM-yyyy");
//                    rows.createCell(2).setCellValue(detailedScmOutput.get(i).getUnitId());
//                    rows.createCell(3).setCellValue(detailedScmOutput.get(i).getUnitName());
//                    rows.createCell(4).setCellValue(detailedScmOutput.get(i).getUnitCostCenter());
//                    rows.createCell(5).setCellValue(detailedScmOutput.get(i).getStatus());
//                    if (detailedScmOutput.get(i).getCafeOpeningDate() != null) {
//                        setDate(workbook, rows.createCell(6), detailedScmOutput.get(i).getCafeOpeningDate(), "dd/MM/yyyy");
//                    }
//                    if (menuOutput.get(i).getBusinessDate() != null ) {
//                        setDate(workbook, rows.createCell(9), menuOutput.get(i).getBusinessDate(), "dd/MM/yyyy");
//                    }
//                    if (detailedScmOutput.get(i).getFulfillmentUnit() != null) {
//                        rows.createCell(7).setCellValue(detailedScmOutput.get(i).getFulfillmentUnit());
//                    }
                    if (detailedScmOutput.get(i).getScmProductId() != null) {
                        rows.createCell(8).setCellValue(detailedScmOutput.get(i).getScmProductId());
                    }
                    if (detailedScmOutput.get(i).getScmProductName() != null ) {
                        rows.createCell(9).setCellValue(detailedScmOutput.get(i).getScmProductName());
                    }
                    if (detailedScmOutput.get(i).getScmProductCategory() != null) {
                        rows.createCell(10).setCellValue(detailedScmOutput.get(i).getScmProductCategory());
                    }
                    if (detailedScmOutput.get(i).getScmProductSubCategory() != null) {
                        rows.createCell(11).setCellValue(detailedScmOutput.get(i).getScmProductSubCategory());
                    }
                    if (detailedScmOutput.get(i).getUom() != null ) {
                        rows.createCell(12).setCellValue(detailedScmOutput.get(i).getUom());
                    }
                    if (detailedScmOutput.get(i).getQuantity() != null ) {
                        rows.createCell(13).setCellValue(detailedScmOutput.get(i).getQuantity().doubleValue());
                    }
                }
                LOG.info("TIMING | L1 - generateProductProjections()->createWorkbook - Generation of Excel Sheet is : {}", System.currentTimeMillis() - startOfExcelGeneration);
                long publishGeneratedProjections = System.currentTimeMillis();
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                try {
                        workbook.write(bos);
                }
                catch (IOException e1) {
                    LOG.error("Error in Saving File in S3", e1);
                }
                byte[] barray = bos.toByteArray();
                InputStream is = new ByteArrayInputStream(barray);
                MultipartFile multipartFile = null;
                try {
                    multipartFile = new MockMultipartFile("PRODUCT_PROJECTIONS" + "." + MimeType.XLSX.name().toLowerCase(), is);
                } catch (Exception e) {
                    LOG.error("Error in Saving File in S3", e);
                }
                String fileNewName = "PRODUCT_PROJECTIONS_"+ projectionsDetailsData.getId() + "." + MimeType.XLSX.name().toLowerCase();
                saveCapexFile(fileNewName, multipartFile, "scm-projections");
                String downloadedPath = props.getS3Bucket() + "/" + "scm-projections" + "/" + fileNewName;
                productProjectionsSevice.updateProjectionsData(downloadedPath,projectionsDetailsData);
                //sending eamil

                try{
                    ProductProjectionsEmailNotificationTemplate emailTemplate = new ProductProjectionsEmailNotificationTemplate(
                            props.getBasePath(), props.getEnvType(), projectionsDetailsData,duplicateUnitsDataList);
                    FileDetail fileDetail = new FileDetail(props.getS3Bucket(),"scm-projections/"+fileNewName,
                            downloadedPath);
                    File uploadedFile = fileArchiveService.getFileFromS3(props.getBasePath()+File.separator+"s3", fileDetail);
                    String emailIds[] = {"<EMAIL>"} ;
                    notificationService.sendProjectionsNotification(emailTemplate,uploadedFile,emailIds);
                }
                catch (Exception e) {
                    LOG.info("Unable to send Email for product Projections ::: ",e);
                }
                LOG.info("TIMING | L1 - generateProductProjections()->createWorkbook - Upload to S3, Send Email is : {}", System.currentTimeMillis() - publishGeneratedProjections);
                LOG.info("TIMING | L1 - generateProductProjections()->TOTAL  - Total Excel generation & notify is : {}", System.currentTimeMillis() - startOfExcelGeneration);

                return workbook;
            }

            private void saveCapexFile(String fileName, MultipartFile multipartFile, String projectionsBucket) {
                FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), projectionsBucket, fileName, multipartFile, true);
            }

            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.setHeader("Content-Disposition", "PRODUCT_PROJECTIONS" + "." + MimeType.XLSX.name().toLowerCase());
            }
        };
        return view;
    }

    private String getUomValue(String uom){
        if(Objects.isNull(uom)){
            return "";
        }
        switch (uom){
            case "SQ_FT":
            case "FT":
                return UnitOfMeasure.FT.value();
            case "SQ_MTR":
            case "CUBIC_M":
            case "M":
                return UnitOfMeasure.M.value();
            case "CUBIC_CM":
                return "CM";
            default:
                return "";


        }
    }

    private Double getConvertionUomValue(String uom , String sourceUom){
        //For converstion from uom value to SourceUom value
        if(sourceUom.equals(UnitOfMeasure.FT.value())){
            if(uom.equals(UnitOfMeasure.M.value())){
                return 3.28084;
            }else if(uom.equals("CM")){
                return 0.0328084;
            }else{
                return Double.valueOf(0);
            }
        }else if(sourceUom.equals(UnitOfMeasure.M.value())){
            if(uom.equals(UnitOfMeasure.FT.value())){
                return 0.3048;
            }else if(uom.equals("CM")){
                return 0.01;
            }else{
                return Double.valueOf(0);
            }
        }else if(sourceUom.equals("mm")){
            if(uom.equals(UnitOfMeasure.FT.value())){
               return 304.8;
            }else if(uom.equals(UnitOfMeasure.M.value())){
               return Double.valueOf(1000);
            }else if(uom.equals("CM")){
               return Double.valueOf(10);
            }else{
                return Double.valueOf(0);
            }

        }else{
            return Double.valueOf(0);
        }



    }

    public void addComment(Workbook workbook, XSSFSheet sheet, int rowIdx, int colIdx, String author, String commentText) {
        CreationHelper factory = workbook.getCreationHelper();
        XSSFCell cell  = sheet.getRow(rowIdx).getCell(colIdx);
        ClientAnchor anchor = factory.createClientAnchor();
        anchor.setCol1(cell.getColumnIndex() + 1); //the box of the comment starts at this given column...
        anchor.setCol2(cell.getColumnIndex() + 2); //...and ends at that given column
        anchor.setRow1(rowIdx); //one row below the cell...
        anchor.setRow2(rowIdx+3); //...and 4 rows high

        Drawing drawing = sheet.createDrawingPatriarch();
        Comment comment = drawing.createCellComment(anchor);
        //set the comment text and author
        comment.setString(factory.createRichTextString(commentText));
        comment.setAuthor(author);

        cell.setCellComment(comment);
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public View generateMeasurementBook(HttpServletResponse response , Integer srId , List<String> toEmails , List<String> ccEmails , IdCodeName initiatedBy) {

        return new AbstractXlsxView() {
            @Override
            protected Workbook createWorkbook(Map<String, Object> model,
                                              HttpServletRequest request) {
                long startOfExcelGeneration = System.currentTimeMillis();
                XSSFWorkbook workbook = new XSSFWorkbook();
                LOG.info("creating sheet for Measurement Book");
                ServiceReceive serviceReceive = serviceReceiveManagementService.findServiceReceive(null,null,null,null,null,srId).get(0);

                CellStyle style = generateHeaderStyle(workbook);
                XSSFCellStyle greenHeader = generateCellStyle(workbook,"Green",null,null,null,null);
                XSSFCellStyle yellowHeader = generateCellStyle(workbook,null,168,121,50,"White");
                XSSFCellStyle blueHeader = generateCellStyle(workbook,null,50,139,168,null);
                XSSFCellStyle greyHeader = generateCellStyle(workbook,null,230,230,230,"Orange");
                CellStyle blackHeader = generateHeaderStyle(workbook);
                XSSFCellStyle colStyle  = generateCellStyle(workbook,null,null,null,null,null);


                //Summary Sheet
                XSSFSheet summarySheet = workbook.createSheet();
                summarySheet.setDefaultColumnWidth(20);
                workbook.setSheetName(0,"Email Summary");
                XSSFRow  vendorRow = summarySheet.createRow(0);
                vendorRow.createCell(0).setCellValue("Vendor");
                vendorRow.getCell(0).setCellStyle(blackHeader);
                vendorRow.createCell(1).setCellValue(serviceReceive.getVendor().getName());
                vendorRow.getCell(1).setCellStyle(colStyle);

                XSSFRow companyRow = summarySheet.createRow(2);
                companyRow.createCell(0).setCellValue("Receiving Company");
                companyRow.getCell(0).setCellStyle(blackHeader);
                companyRow.createCell(1).setCellValue(serviceReceive.getCompany().getName());
                companyRow.getCell(1).setCellStyle(colStyle);
                companyRow.getCell(1).getCellStyle().setWrapText(true);
                Set<Integer> soIds = new HashSet<>();
                Set<Integer> soItemIds = new HashSet<>();
                for(ServiceReceiveItem serviceReceiveItem : serviceReceive.getServiceReceiveItems()){
                    soIds.add(serviceReceiveItem.getServiceOrderId());
                    soItemIds.add(serviceReceiveItem.getServiceOrderItemId());
                }
                Integer summaryRows = 5;
                for(Integer soId : soIds ){
                    List<ServiceOrderStatus> statusList = Arrays.asList(ServiceOrderStatus.APPROVED,
                            ServiceOrderStatus.IN_PROGRESS , ServiceOrderStatus.CLOSED , ServiceOrderStatus.CANCELLED);
                    ServiceOrder serviceOrder = serviceOrderManagementService.findServiceOrders(null,null,soId,null,statusList,null,null,true,null).get(0);
                    XSSFRow soRow = summarySheet.createRow(summaryRows);
                    soRow.createCell(0).setCellValue("SO ID");
                    soRow.getCell(0).setCellStyle(yellowHeader);
                    soRow.createCell(1).setCellValue(soId);
                    soRow.getCell(1).setCellStyle(colStyle);
                    soRow.createCell(3).setCellValue("Created Date");
                    soRow.getCell(3).setCellStyle(yellowHeader);
                    setDate(workbook, soRow.createCell(4), serviceOrder.getGenerationTime(), "dd-MM-yyyy");
                    soRow.getCell(4).setCellStyle(colStyle);
                    summaryRows++;
                    createSheetForSummary(workbook,summarySheet,summaryRows);
                    summaryRows++;
                    Integer intialRow = summaryRows + 1;
                    for(ServiceOrderItem serviceOrderItem : serviceOrder.getOrderItems()){
                            boolean isIncluded = soItemIds.contains(serviceOrderItem.getId());
                            CostElementData costElementData = dao.find(CostElementData.class,serviceOrderItem.getCostElementId());
                            ListDetailData department = dao.find(ListDetailData.class,costElementData.getDepartment().getId());
                            XSSFRow soItemRow = summarySheet.createRow(summaryRows);
                            soItemRow.createCell(0).setCellValue(serviceOrderItem.getCostElementName());
                            soItemRow.getCell(0).setCellStyle(colStyle);
                            soItemRow.createCell(1).setCellValue(serviceOrderItem.getBusinessCostCenterName());
                            soItemRow.getCell(1).setCellStyle(colStyle);
                            soItemRow.createCell(2).setCellValue(department.getName());
                            soItemRow.getCell(2).setCellStyle(colStyle);
                            soItemRow.getCell(2).getCellStyle().setWrapText(true);
                            soItemRow.createCell(3).setCellValue(serviceOrderItem.getServiceDescription());
                            soItemRow.getCell(3).setCellStyle(colStyle);
                            soItemRow.getCell(3).getCellStyle().setWrapText(true);
                            soItemRow.createCell(4).setCellValue(serviceOrderItem.getUnitOfMeasure());
                            soItemRow.getCell(4).setCellStyle(colStyle);
                            soItemRow.createCell(5).setCellValue(serviceOrderItem.getTaxRate().doubleValue());
                            soItemRow.getCell(5).setCellStyle(colStyle);
                            soItemRow.createCell(6).setCellValue(isIncluded ? serviceOrderItem.getTotalCost().doubleValue() : Double.valueOf(0));
                            soItemRow.getCell(6).setCellStyle(isIncluded ?colStyle : blueHeader);
                            soItemRow.createCell(7).setCellValue(isIncluded ? serviceOrderItem.getAmountPaid().doubleValue() : Double.valueOf(0));
                            soItemRow.getCell(7).setCellStyle(isIncluded ? colStyle : blueHeader);
                            if(!isIncluded){
                                addComment(workbook,summarySheet,summaryRows,6,"admin","Not Included In current SR");
                                addComment(workbook,summarySheet,summaryRows,7,"admin","Not Included In current SR");
                            }
                            summaryRows++;
                    }
                    Integer endRow = summaryRows;
                    XSSFRow totalRow = summarySheet.createRow(summaryRows);
                    totalRow.setRowStyle(greenHeader);
                    totalRow.createCell(3).setCellValue("Total Values");
                    totalRow.getCell(3).setCellStyle(greenHeader);
                    totalRow.createCell(6);
                    XSSFCell totalCell = totalRow.getCell(6);
                    totalCell.setCellFormula("SUM(" + "G" + intialRow.toString() + ":" + "G" + endRow.toString()  + ")");
                    totalCell.setCellType(CellType.FORMULA);
                    totalCell.setCellStyle(greenHeader);
                    totalRow.createCell(7);
                    XSSFCell totalWithTaxCell = totalRow.getCell(7);
                    totalWithTaxCell.setCellFormula("SUM(" + "H" + intialRow.toString() + ":" + "H" + endRow.toString()  + ")");
                    totalWithTaxCell.setCellType(CellType.FORMULA);
                    totalWithTaxCell.setCellStyle(greenHeader);
                    summaryRows  = summaryRows + 2;

                }



                //Measurement Book Sheet
                XSSFSheet sheet = workbook.createSheet();
                sheet.setDefaultColumnWidth(30);
                workbook.setSheetName(1, "Measurement Book Values");
                Integer row = -1;

                for (ServiceReceiveItem serviceReceiveItem : serviceReceive.getServiceReceiveItems()) {
                    CostElementData costElementData = dao.find(CostElementData.class, serviceReceiveItem.getCostElementId());
                    row++;
                    XSSFRow commonRow = sheet.createRow(row);
                    commonRow.createCell(0).setCellValue("SO ID");
                    commonRow.getCell(0).setCellStyle(greyHeader);
                    commonRow.createCell(1).setCellValue(serviceReceiveItem.getServiceOrderId());
                    commonRow.getCell(1).setCellStyle(colStyle);
                    commonRow.createCell(3).setCellValue("Cost Element");
                    commonRow.getCell(3).setCellStyle(greyHeader);
                    commonRow.createCell(4).setCellValue(costElementData.getCostElementName());
                    commonRow.getCell(4).setCellStyle(colStyle);
                    commonRow.createCell(6).setCellValue("Description");
                    commonRow.getCell(6).setCellStyle(greyHeader);
                    commonRow.createCell(7).setCellValue(costElementData.getDescription());
                    commonRow.getCell(7).setCellStyle(colStyle);
                    commonRow.getCell(7).getCellStyle().setWrapText(true);
                    commonRow.createCell(9).setCellValue("UOM");
                    commonRow.getCell(9).setCellStyle(greyHeader);
                    commonRow.createCell(10).setCellValue(costElementData.getUom());
                    commonRow.getCell(10).setCellStyle(colStyle);
                    List<ServiceReceivedItemDrilldown> serviceReceivedItemDrilldownList = new ArrayList<>();
                    if(Objects.nonNull(serviceReceiveItem.getServiceReceivedItemDrillDown())){
                        serviceReceivedItemDrilldownList = serviceReceiveItem.getServiceReceivedItemDrillDown();
                    }
                    row++;
                    createSheetForMB(workbook,sheet,row);
                    //row++;
                    ListTypeData subCategory = null;
                    if(costElementData.getSubCategory()!=null){
                        subCategory = dao.find(ListTypeData.class,costElementData.getSubCategory().getId());
                    }
                    ListDetailData category = null;
                    if(costElementData.getCategory()!=null){
                        category = dao.find(ListDetailData.class,costElementData.getCategory().getId());
                    }
                    ListDetailData department = dao.find(ListDetailData.class,costElementData.getDepartment().getId());
                    for (ServiceReceivedItemDrilldown serviceReceivedItemDrilldown : serviceReceivedItemDrilldownList) {
                        XSSFCellStyle drilldownStyle = null;
                        if(serviceReceivedItemDrilldown.getIsExclusionEntry().equals(SCMServiceConstants.SCM_CONSTANT_YES)){
                            drilldownStyle = generateCellStyle(workbook,null,255,127,127,null);
                        }else{
                            drilldownStyle = colStyle;
                        }

                        row++;
                        String sourceUom = serviceReceivedItemDrilldown.getSourceUom();
                        BigDecimal taxValue = serviceReceiveItem.getTaxRate();
                        Integer nos = serviceReceivedItemDrilldown.getNos();
                        BigDecimal quantity = serviceReceivedItemDrilldown.getReceivedQuantity();
                        BigDecimal length = serviceReceivedItemDrilldown.getLength();
                        BigDecimal height = serviceReceivedItemDrilldown.getHeight();
                        BigDecimal width = serviceReceivedItemDrilldown.getWidth();
                        XSSFRow drilldownRow = sheet.createRow(row);
                        Integer currentRow = row + 1;
                        drilldownRow.createCell(0).setCellValue(department.getName());
                        drilldownRow.getCell(0).setCellStyle(drilldownStyle);
                        if(costElementData.getCategory()!=null){
                            drilldownRow.createCell(1).setCellValue(category.getName());
                            drilldownRow.getCell(1).setCellStyle(drilldownStyle);
                        }
                        if(costElementData.getSubCategory()!=null){
                            drilldownRow.createCell(2).setCellValue(subCategory.getName());
                            drilldownRow.getCell(2).setCellStyle(drilldownStyle);
                        }
                        drilldownRow.createCell(3).setCellValue(serviceReceivedItemDrilldown.getDescription());
                        drilldownRow.getCell(3).setCellStyle(drilldownStyle);
                        drilldownRow.getCell(3).getCellStyle().setWrapText(true);
                        drilldownRow.createCell(4).setCellValue(serviceReceivedItemDrilldown.getSourceUom());
                        drilldownRow.getCell(4).setCellStyle(drilldownStyle);
                        drilldownRow.createCell(5).setCellValue(nos);
                        drilldownRow.getCell(5).setCellStyle(drilldownStyle);
                        if(!sourceUom.equals(serviceReceiveItem.getUnitOfMeasure()))
                        {
                            if(Objects.nonNull(length)){
                                drilldownRow.createCell(6).setCellValue(length.doubleValue());
                                drilldownRow.getCell(6).setCellStyle(drilldownStyle);

                                drilldownRow.createCell(9);
                                XSSFCell lengthCell = drilldownRow.getCell(9);
                                lengthCell.setCellFormula( "G" + currentRow.toString() + "*" + getConvertionUomValue(getUomValue(costElementData.getUom()),serviceReceivedItemDrilldown.getSourceUom()));
                                lengthCell.setCellType(CellType.FORMULA);
                                drilldownRow.getCell(9).setCellStyle(drilldownStyle);
                            }
                            if(Objects.nonNull(width)){
                                drilldownRow.createCell(7).setCellValue(width.doubleValue());
                                drilldownRow.getCell(7).setCellStyle(drilldownStyle);

                                drilldownRow.createCell(10);
                                XSSFCell widthCell = drilldownRow.getCell(10);
                                widthCell.setCellFormula( "H" + currentRow.toString() + "*" + getConvertionUomValue(getUomValue(costElementData.getUom()),serviceReceivedItemDrilldown.getSourceUom()));
                                widthCell.setCellType(CellType.FORMULA);
                                drilldownRow.getCell(10).setCellStyle(drilldownStyle);
                            }
                            if(Objects.nonNull(height)){
                                drilldownRow.createCell(8).setCellValue(height.doubleValue());
                                drilldownRow.getCell(8).setCellStyle(drilldownStyle);

                                drilldownRow.createCell(11);
                                XSSFCell heightCell = drilldownRow.getCell(11);
                                heightCell.setCellFormula( "I" + currentRow.toString() + "*" + getConvertionUomValue(getUomValue(costElementData.getUom()),serviceReceivedItemDrilldown.getSourceUom()));
                                heightCell.setCellType(CellType.FORMULA);
                                drilldownRow.getCell(11).setCellStyle(drilldownStyle);
                            }
                        }else{
                            if(Objects.nonNull(length)){
                                drilldownRow.createCell(6).setCellValue(length.doubleValue());
                                drilldownRow.getCell(6).setCellStyle(drilldownStyle);
                            }
                            if(Objects.nonNull(width)){
                                drilldownRow.createCell(7).setCellValue(width.doubleValue());
                                drilldownRow.getCell(7).setCellStyle(drilldownStyle);
                            }
                            if(Objects.nonNull(height)){
                                drilldownRow.createCell(8).setCellValue(height.doubleValue());
                                drilldownRow.getCell(8).setCellStyle(drilldownStyle);
                            }
                        }

                        drilldownRow.createCell(12).setCellValue(quantity.doubleValue());
                        drilldownRow.getCell(12).setCellStyle(drilldownStyle);
                        drilldownRow.createCell(13).setCellValue(serviceReceiveItem.getUnitPrice().doubleValue());
                        drilldownRow.getCell(13).setCellStyle(drilldownStyle);
                        drilldownRow.createCell(14);
                        XSSFCell amountCell = drilldownRow.getCell(14);
                        amountCell.setCellFormula( "M" + currentRow.toString() + "*" + "N" + currentRow.toString());
                        amountCell.setCellType(CellType.FORMULA);
                        drilldownRow.getCell(14).setCellStyle(drilldownStyle);
                        drilldownRow.createCell(15);
                        XSSFCell amountWithTaxCell = drilldownRow.getCell(15);
                        amountWithTaxCell.setCellFormula( "O" + currentRow.toString() + "+" +  "(" + "O" + currentRow.toString() + "*" + taxValue.toString() + "/" + 100 + ")" );
                        drilldownRow.getCell(15).setCellStyle(drilldownStyle);
                        amountWithTaxCell.setCellType(CellType.FORMULA);
                        drilldownRow.setRowStyle(drilldownStyle);
                    }
                    row++;
                    XSSFRow aggregateRow = sheet.createRow(row);
                    aggregateRow.createCell(0).setCellValue(department.getName());
                    aggregateRow.getCell(0).setCellStyle(greenHeader);
                    if(costElementData.getCategory()!=null){
                        aggregateRow.createCell(1).setCellValue(category.getName());
                        aggregateRow.getCell(1).setCellStyle(greenHeader);
                    }
                    if(costElementData.getSubCategory()!=null){
                        aggregateRow.createCell(2).setCellValue(subCategory.getName());
                        aggregateRow.getCell(2).setCellStyle(greenHeader);
                    }
                    aggregateRow.createCell(3).setCellValue("Total");
                    aggregateRow.getCell(3).setCellStyle(greenHeader);
                    aggregateRow.createCell(12).setCellValue(serviceReceiveItem.getReceivedQuantity().doubleValue());
                    aggregateRow.getCell(12).setCellStyle(greenHeader);
                    aggregateRow.createCell(13).setCellValue(serviceReceiveItem.getUnitPrice().doubleValue());
                    aggregateRow.getCell(13).setCellStyle(greenHeader);
                    aggregateRow.createCell(14).setCellValue(serviceReceiveItem.getTotalCost().doubleValue());
                    aggregateRow.getCell(14).setCellStyle(greenHeader);
                    aggregateRow.createCell(15).setCellValue(serviceReceiveItem.getTotalAmount().doubleValue());
                    aggregateRow.getCell(15).setCellStyle(greenHeader);

                    aggregateRow.setRowStyle(greenHeader);
                    row++;
                }
                FormulaEvaluator formulaEvaluator = workbook.getCreationHelper().createFormulaEvaluator();
                formulaEvaluator.evaluateAll();
                LOG.info("Time taken to generate Excel sheet for Measurement Book is : {} ms", System.currentTimeMillis() - startOfExcelGeneration);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                try {
                    workbook.write(bos);
                } catch (IOException e1) {
                    LOG.error("Error in Generating Excell Sheet For Measurement BOOk", e1);
                }
                byte[] barray = bos.toByteArray();
                InputStream is = new ByteArrayInputStream(barray);
                MultipartFile multipartFile = null;
                try {
                    multipartFile = new MockMultipartFile("MEASUREMENT_BOOK" + "." + MimeType.XLSX.name().toLowerCase(), is);
                } catch (Exception e) {
                    LOG.error("Error in Generating Excell Sheet For Measurement BOOk", e);
                }
                String fileNewName = "MEASUREMENT_BOOK_" + serviceReceive.getId() + "." + MimeType.XLSX.name().toLowerCase();
                File fileToUpload = null;
                try {
                    fileToUpload = fileArchiveService.convertFromMultiPart(fileNewName, multipartFile);
                } catch (IOException e) {
                    e.printStackTrace();
                }

                //sending eamil
                try {
                    VendorDetail vendorDetail = scmCache.getVendorDetail(serviceReceive.getVendor().getId());
                    MeasurementBookTemplate measurementBookTemplate = new MeasurementBookTemplate(serviceReceive , props.getBasePath(),vendorDetail);
                    notificationService.sendMeasurementBookEmail(measurementBookTemplate, fileToUpload, initiatedBy.getName(),toEmails,ccEmails);
                } catch (Exception e) {
                    LOG.error("Unable to send Email for Measurement Book ::: ", e.getMessage());
                    response.setStatus(500);

                }

                return workbook;
            }

            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
                response.setHeader("Content-Disposition", "MEASUREMENT_BOOK" + "." + MimeType.XLSX.name().toLowerCase());
            }


        };
    }

    public View generateBulkSoSheet(List<BulkSoDetail> bulkSoDetails) {
        View view = new AbstractXlsxView() {
            @Override
            protected Workbook createWorkbook(Map<String, Object> model, HttpServletRequest request) {
                XSSFWorkbook workbook = new XSSFWorkbook();
                XSSFSheet sheet = workbook.createSheet("Bulk So");
                CellStyle style = generateHeaderStyle(workbook);
                sheet.setDefaultColumnWidth(20);
                XSSFRow row = sheet.createRow(0);
                row.createCell(0).setCellValue("Unit Id");
                row.getCell(0).setCellStyle(style);
                row.createCell(1).setCellValue("Unit Name");
                row.getCell(1).setCellStyle(style);
                row.createCell(2).setCellValue("Allocated Cost");
                row.getCell(2).setCellStyle(style);
                int i=0;
                for (i=0; i < bulkSoDetails.size(); i++) {
                    XSSFRow rows = sheet.createRow(i+1);
                    rows.createCell(0).setCellValue(bulkSoDetails.get(i).getUnitId());
                    rows.createCell(1).setCellValue(bulkSoDetails.get(i).getUnitName());
                    rows.createCell(2).setCellValue(bulkSoDetails.get(i).getAllocatedCost());
                }
                i++;
                XSSFRow lastRow = sheet.createRow(i);
                sheet.addMergedRegion(new CellRangeAddress(i, i, 0, 1));
                lastRow.createCell(0).setCellValue("Total");
                lastRow.getCell(0).setCellStyle(style);
                XSSFCell total = lastRow.createCell(2);
                LOG.info("i value is : {}",i);
                String formula = "SUM(C2:C"+i+")";
                total.setCellFormula(formula);
                workbook.setForceFormulaRecalculation(true);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                try {
                    workbook.write(bos);
                }
                catch (IOException e1) {
                    LOG.error("Error in Saving File in S3", e1);
                }
                return workbook;
            }

            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.setHeader("Content-Disposition", "Sample_Bulk_SO" + "." + MimeType.XLSX.name().toLowerCase());
            }
        };
        return view;
    }

    public View getGrsView(List<GoodsReceived> goodsReceivedList){

        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {
                String fileName = "Good_Receiving_Sheet" + SCMUtil.getCurrentTimeISTStringWithNoColons();
                Map<Integer,GoodsReceivedItem> itemAggregateMap = getAggregatedGrItemMap(goodsReceivedList);
                XSSFSheet grSheet = (XSSFSheet) workbook.createSheet();
                List<String> columns = Arrays.asList("GR ID","SENT BY","TO ID","ASSET ID","CREATED BY","GENERATION TIME",
                        "SKU NAME","CATEGORY","UOM","RECEIVED QTY","TRANSFERRED QTY","EXCESS QTY" ,"GR STATUS");
                workbook.setSheetName(0,fileName);

                createHeaderRow(workbook,grSheet,0,columns);

                grSheet.createFreezePane(0,1);
                fillGRsSheet(grSheet,goodsReceivedList);
                addBorder(grSheet,0,grSheet.getLastRowNum(),0,12,workbook);


                //aggregate sheet
                List<String> aggregateColumns = Arrays.asList("SKU NAME","CATEGORY","UOM","TRANSFERRED QTY","RECEIVED QTY","EXCESS QTY" );
                XSSFSheet aggregateSheet = (XSSFSheet) workbook.createSheet("Aggregate Sheet");
                createHeaderRow(workbook,aggregateSheet,0,aggregateColumns);
                aggregateSheet.createFreezePane(0,1,0,1);
                fillGrAggregateSheet(aggregateSheet, itemAggregateMap.values(),1);
                Integer lastRow = aggregateSheet.getLastRowNum();
                addBorder(aggregateSheet,0,lastRow,0,5,workbook);

                response.addHeader("Content-Disposition", "attachment; filename=" + fileName + MimeType.XLSX.name().toLowerCase());

            }
        };
    }

    public View generateWastageExcelSheet(List<WastageData> wastageDataList, List<WastageAggregatedData> wastageAggregatedData, Boolean isKitchenOrWH) {
        View view = new AbstractXlsxView() {
            @Override
            protected Workbook createWorkbook(Map<String, Object> model, HttpServletRequest request) {
                XSSFWorkbook workbook = new XSSFWorkbook();
                XSSFSheet sheet = workbook.createSheet("Wastages Individual");
                CellStyle style = generateHeaderStyle(workbook);
                sheet.setDefaultColumnWidth(20);
                XSSFRow row = sheet.createRow(0);
                row.createCell(0).setCellValue("Wastage ID");
                row.getCell(0).setCellStyle(style);
                row.createCell(1).setCellValue("Employee Name");
                row.getCell(1).setCellStyle(style);
                row.createCell(2).setCellValue("Generation Time");
                row.getCell(2).setCellStyle(style);
                row.createCell(3).setCellValue("Status");
                row.getCell(3).setCellStyle(style);
                row.createCell(4).setCellValue("Sub Category");
                row.getCell(4).setCellStyle(style);
                row.createCell(5).setCellValue(isKitchenOrWH ? "Sku Name" : "ProductName");
                row.getCell(5).setCellStyle(style);
                row.createCell(6).setCellValue("UOM");
                row.getCell(6).setCellStyle(style);
                row.createCell(7).setCellValue("Quantity");
                row.getCell(7).setCellStyle(style);
                row.createCell(8).setCellValue("Price");
                row.getCell(8).setCellStyle(style);
                row.createCell(9).setCellValue("Toatl Amount");
                row.getCell(9).setCellStyle(style);
                row.createCell(10).setCellValue("Wastage Reason");
                row.getCell(10).setCellStyle(style);
                row.createCell(11).setCellValue("Comment");
                row.getCell(11).setCellStyle(style);
                int i=0;
                for (i=0; i < wastageDataList.size(); i++) {
                    XSSFRow rows = sheet.createRow(i+1);
                    rows.createCell(0).setCellValue(wastageDataList.get(i).getWastageEventId());
                    rows.createCell(1).setCellValue(wastageDataList.get(i).getEmployeeName());
                    rows.createCell(2).setCellValue(AppUtils.getFormattedTime(wastageDataList.get(i).getGenerationTime(),"dd/MM/yyyy HH:mm:ss"));
                    rows.createCell(3).setCellValue(wastageDataList.get(i).getStatus());
                    rows.createCell(4).setCellValue(wastageDataList.get(i).getSubCategory());
                    rows.createCell(5).setCellValue(isKitchenOrWH ? wastageDataList.get(i).getSku().getSkuName() : wastageDataList.get(i).getProduct().getProductName());
                    rows.createCell(6).setCellValue(wastageDataList.get(i).getUom());
                    rows.createCell(7).setCellValue(wastageDataList.get(i).getQuantity().doubleValue());
                    rows.createCell(8).setCellValue(wastageDataList.get(i).getPrice().doubleValue());
                    rows.createCell(9).setCellValue(wastageDataList.get(i).getTotalAmount().doubleValue());
                    rows.createCell(10).setCellValue(wastageDataList.get(i).getComment());
                    rows.createCell(11).setCellValue(wastageDataList.get(i).getEnteredComment());
                }
                XSSFSheet sheet1 = workbook.createSheet("Wastages Aggregate");
                sheet1.setDefaultColumnWidth(20);
                XSSFRow aggregateRow = sheet1.createRow(0);
                aggregateRow.createCell(0).setCellValue(isKitchenOrWH ? "Sku Name" : "ProductName");
                aggregateRow.getCell(0).setCellStyle(style);
                aggregateRow.createCell(1).setCellValue("Sub Category");
                aggregateRow.getCell(1).setCellStyle(style);
                aggregateRow.createCell(2).setCellValue("UOM");
                aggregateRow.getCell(2).setCellStyle(style);
                aggregateRow.createCell(3).setCellValue("Total Quantity");
                aggregateRow.getCell(3).setCellStyle(style);
                aggregateRow.createCell(4).setCellValue("Total Amount");
                aggregateRow.getCell(4).setCellStyle(style);
                aggregateRow.createCell(5).setCellValue("Wastage Events");
                aggregateRow.getCell(5).setCellStyle(style);
                int j=0;
                for (j=0; j<wastageAggregatedData.size(); j++) {
                    XSSFRow aggregateRows = sheet1.createRow(j+1);
                    aggregateRows.createCell(0).setCellValue(isKitchenOrWH ? wastageAggregatedData.get(j).getSkuName() : wastageAggregatedData.get(j).getProductName());
                    aggregateRows.createCell(1).setCellValue(wastageAggregatedData.get(j).getSubCategory());
                    aggregateRows.createCell(2).setCellValue(wastageAggregatedData.get(j).getUom());
                    aggregateRows.createCell(3).setCellValue(wastageAggregatedData.get(j).getTotalQuantity().doubleValue());
                    aggregateRows.createCell(4).setCellValue(wastageAggregatedData.get(j).getTotalCost().doubleValue());
                    aggregateRows.createCell(5).setCellValue(wastageAggregatedData.get(j).getWastageEvents());
                }
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                try {
                    workbook.write(bos);
                }
                catch (IOException e1) {
                    LOG.error("Error Occurred While writing into the Workbook... ::: ", e1);
                }
                return workbook;
            }

            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.setHeader("Content-Disposition", "Wastage" + "." + MimeType.XLSX.name().toLowerCase());
            }
        };
        return view;
    }

    // retrieve field names from a POJO class
    private static List<String> getFieldNamesForClass(Class<?> clazz , List<String> skipColumns) throws Exception {
        List<String> fieldNames = new ArrayList<String>();
        Field[] fields = clazz.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            if(Objects.nonNull(skipColumns) && skipColumns.contains(fields[i].getName())){
                continue;
            }
            if (fields[i].getType().isArray() || fields[i].getType().isAssignableFrom(Collection.class) ||
                    fields[i].getType().equals(List.class)){
                 continue;
            }

            fieldNames.add(fields[i].getName());
        }
        return fieldNames;
    }

    // capitalize the first letter of the field name for retriving value of the
    // field later
    private static String capitalize(String s) {
        if (s.length() == 0)
            return s;
        return s.substring(0, 1).toUpperCase() + s.substring(1);
    }

    public CellStyle getHeaderStyle(XSSFWorkbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());

        headerStyle.setFont(font);
        headerStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);

        return headerStyle;
    }

    CellStyle getDataStyle(XSSFWorkbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        return dataStyle;
    }

    private String splitCamelCase(String s) {
        s =  s.replaceAll(
                String.format("%s|%s|%s",
                        "(?<=[A-Z])(?=[A-Z][a-z])",
                        "(?<=[^A-Z])(?=[A-Z])",
                        "(?<=[A-Za-z])(?=[^A-Za-z])"
                ),
                " "
        );
        return s.substring(0, 1).toUpperCase() + s.substring(1);
    }


    public View getExcell(String fileName ,List<Object> objectsList , HttpServletResponse response , List<String> skipColumns ) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                try {

                    Sheet sheet = workbook.createSheet();
                    List<String> fieldNames = getFieldNamesForClass(objectsList.get(0).getClass(),skipColumns);
                    int rowCount = 0;
                    int columnCount = 0;
                    Row row = sheet.createRow(rowCount++);
                    for (String fieldName : fieldNames) {
                        Cell cell = row.createCell(columnCount++);
                        cell.setCellValue(splitCamelCase(fieldName));
                        cell.setCellStyle(getHeaderStyle((XSSFWorkbook) workbook));
                    }
                    Class<? extends Object> classz = objectsList.get(0).getClass();
                    for (Object t : objectsList) {
                        row = sheet.createRow(rowCount++);
                        columnCount = 0;
                        for (String fieldName : fieldNames) {
                            if (fieldName.equals("serialVersionUID")) {
                                continue;
                            }
                            Cell cell = row.createCell(columnCount);
                            Method method = null;
                            try {
                                method = classz.getMethod("get" + capitalize(fieldName));
                            } catch (NoSuchMethodException nme) {
                                try{
                                    method = classz.getMethod("get" + fieldName);
                                }catch (Exception e){
                                    if(classz.getDeclaredField(fieldName).getType().equals(boolean.class) ||
                                            classz.getDeclaredField(fieldName).getType().equals(Boolean.class) ){
                                        try {
                                            method = classz.getMethod("is" + capitalize(fieldName));
                                        }catch (Exception e1){
                                            LOG.info("Getter Method Not Found for {}","is" + capitalize(fieldName));
                                            columnCount++;
                                            continue;
                                        }
                                    }else{
                                        LOG.info("Getter Method Not Found for {}",fieldName);
                                        columnCount++;
                                        continue;
                                    }

                                }

                            }
                            Object value = method.invoke(t, (Object[]) null);
                            if (value != null) {
                                if (value instanceof String) {
                                    cell.setCellValue((String) value);
                                } else if (value instanceof Long) {
                                    cell.setCellValue((Long) value);
                                } else if (value instanceof Integer) {
                                    cell.setCellValue((Integer) value);
                                } else if (value instanceof Double) {
                                    cell.setCellValue((Double) value);
                                } else  if (value instanceof BigDecimal){
                                    cell.setCellValue(((BigDecimal) value).floatValue());
                                }else if (value instanceof Boolean){
                                    cell.setCellValue((String)SCMUtil.setStatus((Boolean) value));
                                }else if (value instanceof Float) {
                                    cell.setCellValue((Float) value);
                                }else if (value instanceof Date){
                                    setDate((XSSFWorkbook) workbook, (XSSFCell) cell,(Date) value , "dd/MM/yyyy");
                                }else if(value instanceof Enum ){
                                    cell.setCellValue((String) ((Enum<?>) value).name());
                                }else if(value instanceof IdCodeName){
                                    cell.setCellValue((String) ((IdCodeName) value).getName());
                                }else if (value.getClass().getPackage().getName().startsWith("com.stpl.tech")) {
                                    Class<? extends Object> domainClass = value.getClass();
                                    Method method1 = null;
                                    try {
                                        method1 = domainClass.getMethod("get" + "Id");
                                    } catch (Exception e) {
                                        try {
                                            method1 = domainClass.getMethod("get" + capitalize(fieldName) + "Id");
                                        } catch (Exception e1) {
                                            LOG.info("Could Not Find Value For Domain Object");
                                            cell.setCellStyle(getDataStyle((XSSFWorkbook) workbook));
                                            columnCount++;
                                            continue;
                                        }
                                    }
                                    if(Objects.nonNull(method1.invoke( value, (Object[]) null))){
                                        cell.setCellValue((Integer) method1.invoke( value, (Object[]) null));
                                    }
                                }
                            }
                            cell.setCellStyle(getDataStyle((XSSFWorkbook) workbook));
                            columnCount++;
                        }
                    }
                    for (int i = 0; i < columnCount; i++) {
                        sheet.autoSizeColumn(i);
                    }
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    try {
                        workbook.write(bos);
                        LOG.info("writing");
                    } catch (IOException e1) {
                        LOG.error("Error Occurred While writing into the Workbook... ::: ", e1);
                    }
                } catch (Exception e) {
                    LOG.error("error While Generating Excel {}" , e);
                }
            }
        };
    }

    public View createStandAloneTOTemplate(List<GoodsReceivedItem> selectedProducts , List<Integer> unitIds){
        View view = new AbstractXlsxView() {
            @Override
            protected Workbook createWorkbook(Map<String, Object> model, HttpServletRequest request) {
                XSSFWorkbook workbook = new XSSFWorkbook();
                XSSFSheet sheet = workbook.createSheet("Products Distribution");
                //CellStyle style = generateHeaderStyle(workbook);
                CellStyle headerStyle = generateHeaderStyle(workbook);

                CellStyle style = getHeaderStyle(workbook);
                sheet.setDefaultColumnWidth(20);
                XSSFRow row = sheet.createRow(0);
                row.createCell(0).setCellValue("Sub Total");
                row.getCell(0).setCellStyle(headerStyle);
                row.getCell(0).getCellStyle().setAlignment(HorizontalAlignment.CENTER);
                sheet.addMergedRegion(new CellRangeAddress(0,0,0,3));

                List<String> headers = new ArrayList<>();
                headers.add("Unit ID");
                headers.add("Unit Name");
                headers.add("Unit City");
                headers.add("is Sending");

                int col = 4;
                CellStyle dataStyle = getDataStyle(workbook);
                for(GoodsReceivedItem goodsReceivedItem : selectedProducts){
                    row.createCell(col).setCellValue(goodsReceivedItem.getCalculatedAmount());
                    row.getCell(col).setCellStyle(style);
                    headers.add(goodsReceivedItem.getSkuName());
                    col++;
                }

                XSSFRow row2 = sheet.createRow(1);
                row2.createCell(0).setCellValue("SKU ID");
                row2.getCell(0).setCellStyle(headerStyle);
                row2.getCell(0).getCellStyle().setAlignment(HorizontalAlignment.CENTER);
                row2.createCell(1);
                row2.createCell(2);
                row2.createCell(3);
                row2.getCell(1).setCellStyle(style);
                row2.getCell(2).setCellStyle(style);

                sheet.addMergedRegion(new CellRangeAddress(1,1,0,3));

                col = 4;
                for(GoodsReceivedItem goodsReceivedItem : selectedProducts){
                    row2.createCell(col).setCellValue(goodsReceivedItem.getSkuId());
                    row2.getCell(col).setCellStyle(style);
                    col++;
                }

                XSSFRow row3 = sheet.createRow(2);
                row3.createCell(0).setCellValue("Packaging");
                row3.getCell(0).setCellStyle(headerStyle);
                row3.getCell(0).getCellStyle().setAlignment(HorizontalAlignment.CENTER);
                row3.createCell(1);
                row3.createCell(2);
                row3.createCell(3);
                row3.getCell(1).setCellStyle(style);
                row3.getCell(2).setCellStyle(style);
                row3.getCell(3).setCellStyle(style);
                sheet.addMergedRegion(new CellRangeAddress(2,2,0,3));

                col = 4;
                for(GoodsReceivedItem goodsReceivedItem : selectedProducts){
                    row3.createCell(col).setCellValue(goodsReceivedItem.getUnitOfMeasure() + "-" + goodsReceivedItem.getId());
                    row3.getCell(col).setCellStyle(style);
                    col++;
                }



                createHeaderRow(workbook,sheet,3,headers);
                int rowCount = 4;
                for(Integer unitId : unitIds){
                    XSSFRow tempRow = sheet.createRow(rowCount);
                    Unit unit = dataCache.getUnit(unitId);
                    tempRow.createCell(0).setCellValue(unitId);
                    tempRow.getCell(0).setCellStyle(style);
                    tempRow.createCell(1).setCellValue(unit.getName());
                    tempRow.getCell(1).setCellStyle(style);
                    tempRow.createCell(2).setCellValue(unit.getAddress().getCity());
                    tempRow.getCell(2).setCellStyle(style);
                    tempRow.createCell(3).setCellValue("Y");
                    tempRow.getCell(3).setCellStyle(style);

                    rowCount++;
                }

                XSSFRow aggregateRow =  sheet.createRow(rowCount);
                aggregateRow.createCell(3).setCellValue("Units Aggregate");
                aggregateRow.getCell(3).setCellStyle(headerStyle);

                XSSFRow diffRow = sheet.createRow(rowCount+1);
                diffRow.createCell(3).setCellValue("Aggregate Difference");
                diffRow.getCell(3).setCellStyle(headerStyle);

                Integer aggregateCol = 4;
                for(GoodsReceivedItem goodsReceivedItem : selectedProducts){
                    XSSFCell aggregateCell = aggregateRow.createCell(aggregateCol);
                    CellReference start =  new CellReference(4,aggregateCol);
                    CellReference end = new CellReference(rowCount - 1,aggregateCol);
                    String formula = "SUM(" + start.formatAsString()+ ":" + end.formatAsString() + ")*" +
                            goodsReceivedItem.getExcessQuantity();
                    aggregateCell.setCellFormula(formula);
                    aggregateCell.setCellType(CellType.FORMULA);
                    aggregateRow.getCell(aggregateCol).setCellStyle(style);

                    XSSFCell diffCell = diffRow.createCell(aggregateCol);
                    CellReference subTotal = new CellReference(0,aggregateCol);
                    formula = subTotal.formatAsString() + "-" +"SUM(" + start.formatAsString()+ ":" + end.formatAsString() + ")*" +
                            goodsReceivedItem.getExcessQuantity();
                    diffCell.setCellFormula(formula);
                    diffCell.setCellType(CellType.FORMULA);
                    diffRow.getCell(aggregateCol).setCellStyle(style);
                    aggregateCol++;
                }

                FormulaEvaluator formulaEvaluator = workbook.getCreationHelper().createFormulaEvaluator();
                formulaEvaluator.evaluateAll();

                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                try {
                    workbook.write(bos);
                }
                catch (IOException e1) {
                    LOG.error("Error Generating StandAlone TO Sheet", e1);
                }
                return workbook;
            }

            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.setHeader("Content-Disposition", "Sample_Template" + "." + MimeType.XLSX.name().toLowerCase());
            }
        };
        return view;
    }

    public  Map<Integer,Map<Integer,GoodsReceivedItem>> parseDistributionSheet(MultipartFile file , Integer fulfillmentUnitId ) {
        Map<Integer,Map<Integer,GoodsReceivedItem>> skuToUnitMap = new HashMap<>();
        try {
            XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
            XSSFSheet sheet = workbook.getSheetAt(0);

            //sku ids start from 5th column  2nd row(1 indexing)
            Integer lastRowNum = sheet.getLastRowNum();

            //fetching sku ids
            XSSFRow skuRow = sheet.getRow(1);
            XSSFRow packagingRow = sheet.getRow(2);
            Integer skuColNums = Integer.valueOf(skuRow.getLastCellNum());

            for(int i = 4;i<lastRowNum - 1;i++){
                XSSFRow row = sheet.getRow(i);

                //Checks If Transferring to this unit
                XSSFCell tempCell = row.getCell(3);
                String isSending = tempCell.getStringCellValue();
                if(isSending.equalsIgnoreCase("N") || isSending.equalsIgnoreCase("")){
                    continue;
                }

                Integer unitId = Integer.valueOf((int) row.getCell(0).getNumericCellValue());
               // String unitName = scmCache.getUnitDetail(unitId).getUnitName();

                skuToUnitMap.put(unitId,new HashMap<>());

                Integer colNums = Integer.valueOf(row.getLastCellNum());
                for(int j =4 ;j<colNums;j++){
                    Integer skuId = Integer.valueOf((int) skuRow.getCell(j).getNumericCellValue());
                    Integer packagingId = getPackagingId(packagingRow.getCell(j).getStringCellValue());
                    XSSFCell cell = row.getCell(j);
                    if(Objects.isNull(cell)){
                        continue;
                    }
                    CellType cellType = cell.getCellType();
                    switch (cellType) {
                        case BLANK:
                            continue;
                        case NUMERIC:
                            BigDecimal quantity = BigDecimal.valueOf(cell.getNumericCellValue());
                            skuToUnitMap.get(unitId).put(skuId,transferOrderManagementService.setSkuDistribution(skuId,quantity.floatValue(),fulfillmentUnitId,packagingId,true));
                            break;
                    }
                }

            }
            // Closing file output streams
            file.getInputStream().close();
        } catch (Exception e) {
            LOG.error("error while parsing disrtibution sheet" , e);
        }
        return skuToUnitMap;
    }

    Integer getPackagingId(String packaging){
        String[] arrOfStr = packaging.split("-");
        return  Integer.parseInt(arrOfStr[1]);
    }

    public View generateRawMaterialExcel(Integer unitId,List<SCMProductItem> productList) throws DataNotFoundException, SumoException {

        try {
            Map<Integer, SCMProductItem> rawItemMap = new HashMap<>();
            for (SCMProductItem product : productList) {
                ProductionBooking productionBooking = productionBookingService.calculateConsumption(unitId, product.getProductId(), product.getQuantity());
                if (!productionBooking.getBookingConsumption().isEmpty()) {
                    getRawItemMap(productionBooking.getBookingConsumption(), rawItemMap,productionBooking.getProductName());
                }
            }
            return new AbstractXlsxView() {
                @Override
                protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                                  HttpServletRequest request, HttpServletResponse response) throws Exception {

                    String fileName = "\"Preview Production Booking Sheet -" + unitId + "-"
                            + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xlsx\"";
                    response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
                    generateRawMaterialWorkbook(workbook, rawItemMap);
                }
            };
        } catch (SumoException e) {
            throw new SumoException("Error generating production booking excel :: {}", e.getMessage());
        }

    }

    public Map<Integer,SCMProductItem> getRawItemMap(List<BookingConsumption> bookingConsumptionList,Map<Integer,SCMProductItem> rawItemMap,String productName){

        for(BookingConsumption bookingConsumption : bookingConsumptionList){
            if(Objects.isNull(bookingConsumption.getBookingConsumption()) || bookingConsumption.getBookingConsumption().isEmpty()){
                if(Objects.nonNull(rawItemMap.get(bookingConsumption.getProductId()))){
                    BigDecimal updatedQuantity = rawItemMap.get(bookingConsumption.getProductId()).getQuantity().add(bookingConsumption.getCalculatedQuantity());
                    rawItemMap.get(bookingConsumption.getProductId()).setQuantity(updatedQuantity);
                    rawItemMap.get(bookingConsumption.getProductId()).getPaths().add(productName+" -> "+bookingConsumption.getProductName()+" ( "+bookingConsumption.getCalculatedQuantity()+" )");
                }else {
                    SCMProductItem scmProductItem = new SCMProductItem();
                    scmProductItem.setProductName(bookingConsumption.getProductName());
                    scmProductItem.setProductId(bookingConsumption.getProductId());
                    scmProductItem.setQuantity(bookingConsumption.getCalculatedQuantity());
                    scmProductItem.setUom(bookingConsumption.getUom());
                    scmProductItem.getPaths().add(productName+" -> "+bookingConsumption.getProductName()+" ( "+bookingConsumption.getCalculatedQuantity()+" )");
                    rawItemMap.put(bookingConsumption.getProductId(),scmProductItem);
                }
            }else {
                 getRawItemMap(bookingConsumption.getBookingConsumption(),rawItemMap,productName+" -> "+bookingConsumption.getProductName());
            }
        }
        return rawItemMap;
    }




}
