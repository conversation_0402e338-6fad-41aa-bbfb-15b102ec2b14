package com.stpl.tech.scm.service.model;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class SkuToProductsRequestData {
    Integer unitId;
    List<Integer> roIds;
    Map<Integer,Integer> productToSkuMap;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public List<Integer> getRoIds() {
        return roIds;
    }

    public void setRoIds(List<Integer> roIds) {
        this.roIds = roIds;
    }


    public Map<Integer, Integer> getProductToSkuMap() {
        if(Objects.nonNull(productToSkuMap)){
            return productToSkuMap;
        }else{
            return new HashMap<>();
        }
    }

    public void setProductToSkuMap(Map<Integer, Integer> productToSkuMap) {
        this.productToSkuMap = productToSkuMap;
    }
}
