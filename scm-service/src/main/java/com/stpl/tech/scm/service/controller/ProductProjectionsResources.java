package com.stpl.tech.scm.service.controller;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.ProductProjectionsSevice;
import com.stpl.tech.scm.data.model.ProductProjectionsDetailsData;
import com.stpl.tech.scm.domain.model.ProductProjectionsUnitDetail;
import com.stpl.tech.scm.domain.model.ProductProjectionsUnits;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.PRODUCT_PROJECTIONS_ROOT_CONTEXT)
public class ProductProjectionsResources extends AbstractSCMResources {

    private static final Logger LOG = LoggerFactory.getLogger(ProductProjectionsResources.class);

    @Autowired
    private ExcelViewGenerator excelViewGenerator;

    @Autowired
    private ProductProjectionsSevice productProjectionsSevice;


    @RequestMapping(method = RequestMethod.POST, value = "generate-product-projections", consumes = MediaType.APPLICATION_JSON)
    public View generateProductProjections(@RequestBody ProductProjectionsUnits projectionsData)
            throws SumoException, IOException {
        try {
            LOG.info("start date is {} and end date is {}", projectionsData.getStartDate(), projectionsData.getEndDate());
            HashMap<String, Object> finalResult = productProjectionsSevice.generateProductProjections(projectionsData, false);
            List<ProductProjectionsUnitDetail> unitsInputData = (List<ProductProjectionsUnitDetail>) finalResult.get("unitsInputData");
            List<ProductProjectionsUnitDetail> menuOutput = (List<ProductProjectionsUnitDetail>) finalResult.get("menuOutput");
            List<ProductProjectionsUnitDetail> scmOutput = (List<ProductProjectionsUnitDetail>) finalResult.get("scmOutput");
            List<ProductProjectionsUnitDetail> finalOfDetailedScm = (List<ProductProjectionsUnitDetail>) finalResult.get("finalOfDetailedScm");
            ProductProjectionsDetailsData projectionsDetailsData = (ProductProjectionsDetailsData) finalResult.get("projectionsDetailsData");
            return excelViewGenerator.generateProductProjections(unitsInputData, menuOutput, scmOutput, finalOfDetailedScm, projectionsDetailsData, null);

        }
        catch (Exception e) {
            throw new SumoException("Can not generate the Product Projections..!");
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-projections-for-units", consumes = MediaType.APPLICATION_JSON)
    public View generateProductProjectionsForUnits(@RequestBody ProductProjectionsUnits data) throws SumoException, IOException {
        try {
            HashMap<String, Object> finalResult = productProjectionsSevice.generateProductProjections(data, true);
            List<ProductProjectionsUnitDetail> unitsInputData = (List<ProductProjectionsUnitDetail>) finalResult.get("unitsInputData");
            List<ProductProjectionsUnitDetail> menuOutput = (List<ProductProjectionsUnitDetail>) finalResult.get("menuOutput");
            List<ProductProjectionsUnitDetail> scmOutput = (List<ProductProjectionsUnitDetail>) finalResult.get("scmOutput");
            List<ProductProjectionsUnitDetail> finalOfDetailedScm = (List<ProductProjectionsUnitDetail>) finalResult.get("finalOfDetailedScm");
            ProductProjectionsDetailsData projectionsDetailsData = (ProductProjectionsDetailsData) finalResult.get("projectionsDetailsData");
            Map<Integer, ProductProjectionsUnitDetail> duplicateUnitsData = new HashMap<>();
            Set<ProductProjectionsUnitDetail> duplicateUnitsDataList = new HashSet<>();
            for (ProductProjectionsUnitDetail detail : unitsInputData) {
                if (!duplicateUnitsData.containsKey(detail.getUnitId())) {
                    duplicateUnitsData.put(detail.getUnitId(), detail);
                    duplicateUnitsDataList.add(detail);
                }
            }
            return excelViewGenerator.generateProductProjections(unitsInputData, menuOutput, scmOutput, finalOfDetailedScm, projectionsDetailsData, duplicateUnitsDataList);
        }
        catch (Exception e) {
            throw new SumoException("Can not generate the Product Projections..!");
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "read-uploaded-file", consumes = MediaType.MULTIPART_FORM_DATA)
    public List<ProductProjectionsUnitDetail> readUploadFile(@RequestParam(value = "file") final MultipartFile file) throws IOException, SumoException {
        return productProjectionsSevice.readUploadFile(file);
    }
}
