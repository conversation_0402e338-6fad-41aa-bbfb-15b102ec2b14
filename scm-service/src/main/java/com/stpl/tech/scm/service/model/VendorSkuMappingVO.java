/**
 * 
 */
package com.stpl.tech.scm.service.model;

import com.stpl.tech.scm.domain.model.IdCodeName;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class VendorSkuMappingVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8184285265195113373L;
	private Integer vendorId;
	private List<IdCodeName> skuIds;
	private int employeeId;
	private String employeeName;

	public Integer getVendorId() {
		return vendorId;
	}

	public void setVendorId(Integer vendorId) {
		this.vendorId = vendorId;
	}

	public List<IdCodeName> getSkuIds() {
		return skuIds;
	}

	public void setSkuIds(List<IdCodeName> skuIds) {
		this.skuIds = skuIds;
	}

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

}
