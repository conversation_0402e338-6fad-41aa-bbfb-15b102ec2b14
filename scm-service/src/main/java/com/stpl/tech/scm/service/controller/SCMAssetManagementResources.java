package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.recipe.model.RecipeMediaDetail;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.data.dao.impl.MutexFactory;
import com.stpl.tech.scm.data.model.AssetRecoveryDefinitionData;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.ASSET_MANAGEMENT_ROOT_CONTEXT)
public class SCMAssetManagementResources extends AbstractResources {

	private final Logger LOG = LoggerFactory.getLogger(SCMAssetManagementResources.class);


	@Autowired
	private SCMAssetManagementService scmAssetManagementService;

	@Autowired
	private SCMMetadataService scmMetadataService;

	@Autowired
	EnvProperties env;

	@Autowired
	private MutexFactory<String> factory;

	@RequestMapping(method = RequestMethod.POST, value = "asset", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public AssetDefinition addNewAsset(@RequestBody final AssetDefinition assetDefinition)
			throws DataUpdationException, SumoException{
		return scmAssetManagementService.addNewAsset(assetDefinition);
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-backdated-asset", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public AssetDefinition createBackDatedAsset(@RequestBody final AssetDefinition assetDefinition)
			throws DataUpdationException, SumoException{
		return scmAssetManagementService.createBackDatedAsset(assetDefinition);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "asset", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public AssetDefinition updateAsset(@RequestBody final AssetDefinition assetDefinition)
			throws DataUpdationException, SumoException{
		return scmAssetManagementService.updateAsset(assetDefinition);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "asset-completion", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public AssetDefinition completeAssetGeneration(@RequestBody final AssetDefinition assetDefinition)
			throws DataUpdationException, SumoException{
		return scmAssetManagementService.completeAssetGeneration(assetDefinition);
	}

	@RequestMapping(method = RequestMethod.POST, value = "assets", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public List<AssetDefinition> addNewAssets(@RequestBody final List<AssetDefinition> assetDefinitions)
			throws DataUpdationException, SumoException {
		return scmAssetManagementService.addNewAssets(assetDefinitions);
	}

	@RequestMapping(method = RequestMethod.GET, value = "asset", produces = MediaType.APPLICATION_JSON)
	public AssetDefinition viewAsset(@RequestParam(value = "assetId") final int assetId) {
		return scmAssetManagementService.viewAsset(assetId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "asset-tagValue", produces = MediaType.APPLICATION_JSON)
	public AssetDefinitionSlimObject viewAssetByTagValue(@RequestParam(value = "tagValue") final String tagValue) {
		return scmAssetManagementService.viewAssetByTagValue(tagValue);
	}

	@RequestMapping(method = RequestMethod.POST, value = "asset-print-tagValue", produces = MediaType.APPLICATION_JSON)
	public boolean viewAssetByTagValue(@RequestParam(value = "assetId") final int assetId,@RequestParam(value = "printedBy") final int printedBy) throws SumoException {
		return scmAssetManagementService.printAssetTagValue(assetId, printedBy);
	}

	@RequestMapping(method = RequestMethod.POST, value = "bulk-asset-print-tagValue", produces = MediaType.APPLICATION_JSON)
	public Boolean printBulkAssetTagValueByUnit(@RequestBody List<Integer> assetIds ,@RequestParam(value = "printedBy") final int printedBy) throws SumoException {
		return scmAssetManagementService.printBulkAssetTagValue(assetIds,printedBy);
	}


	@RequestMapping(method = RequestMethod.GET, value = "asset-status", produces = MediaType.APPLICATION_JSON)
	public List<String> assetStatusTypeList(){
		return scmAssetManagementService.assetStatusTypeList();
	}

	@RequestMapping(method = RequestMethod.GET, value = "asset-all", produces = MediaType.APPLICATION_JSON)
	public List<AssetDefinition> viewAllAssets() {
		return scmAssetManagementService.viewAllAssets();
	}

	@RequestMapping(method = RequestMethod.GET, value = "asset-recovery-unit-status", produces = MediaType.APPLICATION_JSON)
	public List<AssetRecoveryDefinition> viewAllAssetsInRecovery(@RequestParam(value = "unitId") final int unitId, @RequestParam(value = "recoveryStatus") final String recoveryStatus) {
		return scmAssetManagementService.getAllAssetsInRecoveryFromUnit(unitId, recoveryStatus);
	}

	@RequestMapping(method = RequestMethod.POST, value = "recover-asset-list", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean recoverAsset(@RequestBody final List<AssetRecoveryDefinition> assetRecoveryDefinitions) throws SumoException {
		return scmAssetManagementService.recoverAssetList(assetRecoveryDefinitions);
	}

	@RequestMapping(method = RequestMethod.POST, value = "approve-recover-asset-list", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean initiateAssetRecovery(@RequestBody final List<AssetRecoveryDefinition> assetRecoveryDefinitions) throws SumoException {
		return scmAssetManagementService.initiateAssetRecovery(assetRecoveryDefinitions);
	}

	@RequestMapping(method = RequestMethod.GET, value = "asset-unit", produces = MediaType.APPLICATION_JSON)
	public List<AssetDefinition> viewAllAssetFromUnit(@RequestParam(value = "unitId") final int unitId) {
		return scmAssetManagementService.viewAllAssetsFromUnit(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "asset-name-unit-slim", produces = MediaType.APPLICATION_JSON)
	public List<AssetDefinitionSlimObject> viewAllAssetSlimFromUnitByName(@RequestParam(value = "unitId") final int unitId, @RequestParam(value = "assetName") final String assetName) {
		return scmAssetManagementService.viewAllAssetsSlimFromUnitByName(unitId, assetName);
	}

	@RequestMapping(method = RequestMethod.GET, value = "asset-unit-slim", produces = MediaType.APPLICATION_JSON)
	public List<AssetDefinitionSlimObject> viewAllAssetSlimFromUnit(@RequestParam(value = "unitId") final int unitId) {
		return scmAssetManagementService.viewAllAssetsSlimFromUnit(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "asset-unit-status", produces = MediaType.APPLICATION_JSON)
	public List<AssetDefinition> viewAllAssetFromUnitWithStatus(@RequestParam(value = "unitId") final int unitId, @RequestParam(value = "assetStatus") final String assetStatus) {
		return scmAssetManagementService.viewAllAssetsFromUnitWithStatus(unitId, assetStatus);
	}

	@GetMapping(value = "asset-transferable-unit", produces = MediaType.APPLICATION_JSON)
	public List<AssetDefinition> viewAllTransferableAssetFromUnit(
			@RequestParam(value = "unitId") final int unitId) {
		return scmAssetManagementService.viewAllTransferableAssetsFromUnit(unitId, true);
	}


	@RequestMapping(method = RequestMethod.POST, value = "asset-transferable-unit-by-products", produces = MediaType.APPLICATION_JSON)
	public List<AssetDefinition> viewAllTransferableAssetFromUnitByProducts(
			@RequestParam(value = "unitId") final int unitId, @RequestBody List<Integer> productIds) {
		return scmAssetManagementService.viewAllTransferableAssetsFromUnitByProducts(unitId, productIds, true);
	}

	@RequestMapping(method = RequestMethod.GET, value = "asset-gritem", produces = MediaType.APPLICATION_JSON)
	public List<AssetDefinition> viewAllAssetForGRItem(@RequestParam(value="grItemId") final int grItemId) {
		return scmAssetManagementService.getAllAssetWithGRItemId(grItemId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "create-depreciation", produces = MediaType.APPLICATION_JSON)
	public boolean initiateDepreciation(@RequestParam(value = "t1") String t1,
										@RequestParam(value = "t2") String t2) throws ParseException, SumoException {

		Date startDate = AppUtils.getDate(t1, "yyyy-MM-dd");
		Date endDate = AppUtils.getDate(t2, "yyyy-MM-dd");
		scmAssetManagementService.createAssetDepreciationBetween(startDate, endDate);
		return true;
	}

	@RequestMapping(method = RequestMethod.POST , value = "stock-event", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public StockTakeInitResponse initiateNewStockEvent(@RequestBody StockEventDefinition stockEventDefinition) throws SumoException{
		StockTakeInitResponse response = new StockTakeInitResponse();
		response = scmAssetManagementService.initiateNewStockEvent(stockEventDefinition, stockEventDefinition.getIsSplit());
		response.setSubTypeList(scmAssetManagementService.getSubTypeListForApp(stockEventDefinition.getUnitId()));
		return response;
	}
	@RequestMapping(method = RequestMethod.POST , value = "child-stock-event", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public StockEventDefinition initiateChildStockEvent(@RequestBody StockEventDefinition stockEventDefinition) throws SumoException{
		return scmAssetManagementService.initiateChildStockEvent(stockEventDefinition);
	}

	@RequestMapping(method = RequestMethod.GET, value = "alter-asset-status", produces = MediaType.APPLICATION_JSON)
	public AssetDefinition changeStatus(@RequestParam(value = "assetId") int assetId,
										@RequestParam(value = "assetStatus") String assetStatus) throws SumoException {
		return scmAssetManagementService.updateAssetStatus(assetId, assetStatus);
	}

	@RequestMapping(method = RequestMethod.GET, value = "cafe-asset-put-to-use", produces = MediaType.APPLICATION_JSON)
	public List<AssetDefinition> massCafeAssetPutToUse() throws SumoException {
		return scmAssetManagementService.massAssetPutToUse();
	}


	@RequestMapping(method = RequestMethod.GET, value = "stock-event", produces = MediaType.APPLICATION_JSON)
	public StockEventDefinition viewEvent(@RequestParam(value = "eventId") final int eventId) {
		return scmAssetManagementService.viewEvent(eventId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "stock-take-list", produces = MediaType.APPLICATION_JSON)
	public List<String> getStockTakeList() {
		return scmAssetManagementService.getStockTakeList();
	}

	@RequestMapping(method = RequestMethod.GET, value = "stock-take-list-sub", produces = MediaType.APPLICATION_JSON)
	public Map<String, Boolean> getStockTakeSubList() {
		return scmAssetManagementService.getStockTakeSubMap();
	}


	@RequestMapping(method = RequestMethod.GET, value = "stock-event-unit", produces = MediaType.APPLICATION_JSON)
	public StockTakeInitResponse getAllEventByUnit(HttpServletRequest request,
														@RequestParam(value = "unitId") final int unitId,
														@RequestParam(value = "eventStatus", required = false)  String eventStatus ,
														@RequestParam(required = false) Integer roId) throws SumoException{
		synchronized (factory.getMutex(String.valueOf(unitId) + eventStatus)) {
			StockTakeInitResponse response = new StockTakeInitResponse();
			if (eventStatus == null) {
				eventStatus = StockEventStatusType.IN_PROCESS.value();
			}
			response = scmAssetManagementService.getAllEventByUnit(unitId, eventStatus, roId, getLoggedInUser(request));
			try {
				response.setSubTypeList(scmAssetManagementService.getSubTypeListForApp(unitId));
			} catch (Exception e) {
				LOG.error(e.toString());
			}
			return response;
		}
	}

    @RequestMapping(method = RequestMethod.POST, value = "stock-event-verification-creation",consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public StockEventAssetMappingDefinitionRequest verifyAndCreateEventAssetMapping(
	        @RequestBody StockEventAssetMappingDefinitionRequest stockEventAssetMappingDefinitionRequest) throws SumoException {
	    return scmAssetManagementService.verifyAndCreateEventAssetMapping(stockEventAssetMappingDefinitionRequest);
    }

	@RequestMapping(method = RequestMethod.POST, value = "stock-event-pause-submit",consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public Boolean pauseAndCreateEventAssetMapping(
			@RequestBody StockEventAssetMappingDefinitionRequest stockEventAssetMappingDefinitionRequest , @RequestParam Boolean isSubmit) throws SumoException {
		if(Boolean.TRUE.equals(isSubmit)){

			return  scmAssetManagementService.submitFAStockTakeEvent(stockEventAssetMappingDefinitionRequest);
		}else{
			return scmAssetManagementService.pauseFAStockTakeEvent(stockEventAssetMappingDefinitionRequest);
		}
	}

	@RequestMapping(method = RequestMethod.PUT , value = "update-event-device-info", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public Boolean updateDeviceInfo(@RequestParam Integer eventId,@RequestParam String deviceInfo) throws SumoException{
		return scmAssetManagementService.updateDeviceInfo(eventId, deviceInfo);
	}


	@RequestMapping(method = RequestMethod.PUT , value = "stock-event", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public StockEventDefinition updateStockEvent(@RequestBody StockEventDefinition stockEventDefinition) throws SumoException{
		return scmAssetManagementService.updateStockEvent(stockEventDefinition,true);
	}

	@RequestMapping(method = RequestMethod.POST , value = "stock-event-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public List<StockEventAssetMappingDefinition> addAssetStockEventMappings(@RequestBody List<StockEventAssetMappingDefinition> stockEventAssetMappingDefinitions)
			throws SumoException {
		return null;
	}

	@Scheduled(cron = "0 0 22 28,29,30,31 * ?")
	public void calculateMonthlyAssetDepreciation() throws SumoException{
		scmAssetManagementService.depreciationCalculationOnLastDayOfMonth();
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-monk-ingredients-assets")
	public Map<Integer, List<AssetDefinition>> getMonkAssets(@RequestParam Integer productId , @RequestParam Integer unitId) throws DataNotFoundException, SumoException {
		LOG.info("Got Request to Get Monk Ingredient Assets  for product id : {} unitId : {}",productId,unitId);
		return scmAssetManagementService.getMonkIngredientsAssets(productId,unitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "auto-transfer-monk-ingredients")
	public List<Integer> transferMonkIngredients(HttpServletRequest request, @RequestBody(required = false) Map<Integer,BigDecimal> skuQuantityMap, @RequestParam(required = false) List<Integer> assetIds, @RequestParam Integer unitId) throws DataNotFoundException, SumoException, TransferOrderCreationException, InventoryUpdateException, ParseException {
		LOG.info("Got Request to Auto Transfer  Monk Ingredient Ingredients  for  unitId : {}",unitId);
		return scmAssetManagementService.autoTransferOfMonkIngredients(skuQuantityMap,assetIds,unitId,getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.POST, value = "monk-ingredients-stock")
	public Map<Integer,BigDecimal> getMonkIngredientsStock(HttpServletRequest request, @RequestBody List<Integer> productIds, @RequestParam(required = false) List<Integer> assetIds, @RequestParam Integer unitId) {
		LOG.info("Got Request to get Stock of  Monk Ingredient Ingredients  for  unitId : {}",unitId);
		return scmAssetManagementService.getMonkIngredientsStock(productIds,unitId);
	}
     @RequestMapping(method = RequestMethod.POST , value = "fa-submit-transfer-out")
	 public  Integer getFaTransferSubmitEvent(@RequestParam Integer eventId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException {
		return scmAssetManagementService.getFaTransferSubmitEvent(eventId) ;
	}

	@RequestMapping(method = RequestMethod.POST, value = "not-found-in-audit")
	public Boolean markNotFoundInAudit(@RequestParam String assetTag, @RequestParam Integer updatedBy) {
		LOG.info("Request Received to mark Asset With tag : {} and asset is : NOT FOUND " , assetTag);
		return scmAssetManagementService.markNotFoundInAudit(assetTag, updatedBy);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-non-assets-in-asset-inventory")
	public Map<Integer,BigDecimal> getAllNonAssetStockInAssetInventory() {
		LOG.info("Request Received to Get Non Assets Stock In Asset Inventory ");
		return scmAssetManagementService.getNonAssetStockInAssetInventory();
	}

	@RequestMapping(method = RequestMethod.POST, value = "convert-non-assets-in-asset-inventory")
	public Boolean convertNonAssetStockInAssetInventory(HttpServletRequest request,@RequestBody Map<Integer,BigDecimal> productQtyMap) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, ParseException, SumoException {
		LOG.info("Request Received to Convert Non Assets Stock In Asset Inventory ");
		return scmAssetManagementService.transferAndConvertNonAssetsInAssetInventory(productQtyMap,getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.POST ,value = "save-non-scannable-products")
	public Boolean saveNonScannableAssetMapping(@RequestBody List<Integer> productIds){
		return scmAssetManagementService.saveNonScannableAssetMappings(productIds);
	}

	@RequestMapping(method = RequestMethod.GET ,value = "get-non-scannable-products")
	public List<Integer> getNonScannableAssetMapping() {
		return scmAssetManagementService.getNonScannableAssetMappings();
	}

	@RequestMapping(method = RequestMethod.POST, value = "save-excess-asset-found", consumes = MediaType.MULTIPART_FORM_DATA, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean saveExcessAssetFound(HttpServletRequest request, @RequestParam(value = "file") final MultipartFile file , @RequestParam(value = "eventId") Integer eventId,
										@RequestParam(value = "comment") String comment , @RequestParam(value = "productName") String productName) throws SumoException {
		return scmAssetManagementService.saveExcessAssetFoundData(file,comment,productName,eventId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "fa-stock-take-report" , produces = MediaType.APPLICATION_JSON)
	public Boolean sendFAStockTakeReport(@RequestParam(value = "eventId") Integer eventId, @RequestParam(value = "submit", required = false) Boolean submit){
		if(submit == null) { submit = false; }
		try{
			if(env.allowManualFaStockTakeReport()){
			       return scmAssetManagementService.sendFAStockTakeReport(eventId, submit);
		    }else{
			       LOG.info("Skipping manual stock take report!!");
		    }

		}catch(Exception e){
			LOG.info(e.getMessage());
		}
		return false;
	}

	@RequestMapping(method = RequestMethod.GET, value = "asset-recovery-data", produces = MediaType.APPLICATION_JSON)
	public Map<String,List<AssetRecovery>> getAssetsPendingRecovery(@RequestParam(value="unitId", required = false) Integer unitId,
														@RequestParam(value="status", required = false) String status,
														@RequestParam(value="assetName", required = false) String assetName,
														@RequestParam(value="assetId", required = false) Integer assetId,
														@RequestParam(value="startDate", required = false) String startDate,
														@RequestParam(value="endDate", required = false) String endDate) throws SumoException{
		return scmAssetManagementService.getAssetsPendingRecovery(unitId,status,assetName,assetId,startDate,endDate);
	}

	@RequestMapping(method = RequestMethod.POST, value = "submit-asset-recovery", produces = MediaType.APPLICATION_JSON)
	public Boolean getAssetRecoveryData(@RequestParam(value="recoveryId") final Integer recoveryId,
														@RequestBody List<AssetRecoveryDetail> recoveryList) throws SumoException {
		return scmAssetManagementService.submitRecovery(recoveryId, recoveryList);
	}

	@RequestMapping(method = RequestMethod.POST, value = "save-invalid-tag", produces = MediaType.APPLICATION_JSON)
	public Boolean saveInvalidTag(@RequestParam(value="eventId") final Integer eventId, @RequestParam(value="tagValue") final String tagValue) throws SumoException {
		return scmAssetManagementService.saveInvalidTag(eventId, tagValue);
	}

	@RequestMapping(method = RequestMethod.POST, value = "fa-to-consumable", produces = MediaType.APPLICATION_JSON)
	public Boolean convertFaToConsumable(@RequestBody final List<Integer> assetIds,@RequestParam(value="userId") Integer userId) throws SumoException, DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, ParseException {
		return scmAssetManagementService.convertFaToConsumable(assetIds, userId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "consumable-to-fa", produces = MediaType.APPLICATION_JSON)
	public Boolean convertConsumableToFa(@RequestBody final List<Integer> skuIds,@RequestParam(value="userId") Integer userId) throws SumoException, DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, ParseException {
		return scmAssetManagementService.convertConsumableToFa(skuIds, userId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "edit-asset-name" , produces = MediaType.APPLICATION_JSON)
	public Boolean editAssetName(HttpServletRequest request ,@RequestParam(value = "assetId") Integer assetId, @RequestParam(value = "newName", required = true) String newName) throws SumoException {
		return scmAssetManagementService.editAssetName(assetId, newName,getLoggedInUser(request));
	}

	@GetMapping(value = "get-stock-take-app-version")
	public String getStockTakeAppVersion() {
		return scmAssetManagementService.getStockTakeAppVersion(false);
	}

	@GetMapping(value = "update-asset-to-cache")
	public Boolean updateAsset(@RequestParam(value = "assetId") Integer assetId)
			throws SumoException {
		return scmAssetManagementService.updateAssetToCache(assetId);
	}
	@PostMapping("get-save-lost-asset-data")
	public AssetDefinition getAndSaveLostAssetData(@RequestParam final String assetTagValue,@RequestParam final Integer eventId) throws SumoException {
		return scmAssetManagementService.getAndSaveLostAssetData(assetTagValue,eventId);
	}

	@PostMapping("submit-found-asset-event")
	public Boolean submitFoundAssetEvent(@RequestParam final Integer eventId) throws SumoException {
		return scmAssetManagementService.submitFoundAssetEvent(eventId);
	}

	@PostMapping("/start-unit-closure-regular-event-day-close")
	public Boolean startUnitClosureRegularEventAndDayClose(@RequestBody final List<Integer> unitIds) throws SumoException {
		LOG.info("Starting startUnitClosureRegularEventAndDayClose, total unit Ids  : {}  ",unitIds.size());
		return scmAssetManagementService.startUnitClosureRegularEventAndDayClose(unitIds);
	}

	@PostMapping("/reset-asset-byId")
	public void resetAssetById(@RequestBody final Integer assetId ) {
		scmMetadataService.saveAssetDefinitionToRedis(assetId);
	}
}
