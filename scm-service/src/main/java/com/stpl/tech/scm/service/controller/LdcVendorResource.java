package com.stpl.tech.scm.service.controller;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.decorator.LdcVendorServiceDecorator;
import com.stpl.tech.scm.domain.model.LdcVendorDomain;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.LDC_VENDOR_ROOT_CONTEXT)
public class LdcVendorResource extends AbstractSCMResources{

    @Autowired
    private LdcVendorServiceDecorator ldcVendorServiceDecorator;

    @GetMapping("/get-ldc-data")
    public Map<Long,LdcVendorDomain> getLdcDataForVendor(@RequestParam Integer vendorId) throws SumoException {
            return ldcVendorServiceDecorator.getLdcForVendorId(vendorId);
    }

    @PostMapping("/add-ldc-data")
    public LdcVendorDomain addLdcDataForVendor(@RequestBody LdcVendorDomain ldcVendorDomain,HttpServletRequest request) throws SumoException {
        return ldcVendorServiceDecorator.addLdcForVendor(ldcVendorDomain,getLoggedInUser(request));
    }

    @PostMapping("/deactivate-ldc-data")
    public LdcVendorDomain deactivateLdcData(@RequestParam Long ldcId,HttpServletRequest request) throws SumoException {
        return ldcVendorServiceDecorator.deactiveLdcForVedndor(ldcId,getLoggedInUser(request));
    }

    @PostMapping("/update-ldc-data")
    public LdcVendorDomain updateLdcdata(@RequestBody LdcVendorDomain ldcVendorDomain) throws SumoException {
        return  ldcVendorServiceDecorator.updateLdcVendorData(ldcVendorDomain);
    }

}
