package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SalesPerformaInvoiceException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.SalesPerformaInvoiceService;
import com.stpl.tech.scm.core.util.EWayHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.SalesPerformaInvoiceDao;
import com.stpl.tech.scm.data.dao.TransferOrderManagementDao;
import com.stpl.tech.scm.data.transport.model.EPortalWrapper;
import com.stpl.tech.scm.data.transport.model.EWayWrapper;
import com.stpl.tech.scm.domain.model.CorrectedSalesInvoiceDetails;
import com.stpl.tech.scm.domain.model.CreditDebitNoteDetail;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GstInvoiceType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.OutwardRegister;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.service.model.InvoiceFileUploadType;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.TemplateRenderingException;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-07-2018.
 */

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.INVOICE_MANAGEMENT_ROOT_CONTEXT)
public class SalesInvoiceResource extends AbstractSCMResources {

    private static final Logger LOG = LoggerFactory.getLogger(SalesInvoiceResource.class);

    @Autowired
    private SalesPerformaInvoiceService service;

    @Autowired
    private SalesPerformaInvoiceDao dao;

    @Autowired
    private RequestOrderManagementService requestOrderService;

    @Autowired
    private ExcelViewGenerator excelViewGenerator;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private EnvProperties props;

    @Autowired
    private SCMNotificationService notificationService;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private SalesPerformaInvoiceDao invoiceDao;

    @Autowired
    private TransferOrderManagementDao transferOrderManagementDao;

    @Scheduled(cron = "0 15 05 * * *", zone = "GMT+05:30")
    public void cancelOldInvoiceRequests() throws Exception {
        try {
            if (!SCMUtil.isDev(props.getEnvType())) {
                Date currentDate = SCMUtil.getCurrentDate();
                Date previousDate = SCMUtil.getDayBeforeOrAfterDay(currentDate,-45);
                String prev = SCMUtil.getDateString(previousDate,SCMUtil.DATE_FORMAT_STRING);
                Date prevDate = SCMUtil.getDate(prev,SCMUtil.DATE_FORMAT_STRING);
                service.cancelOldInvoices(prevDate);
            }
        } catch (Exception e) {
            LOG.error("Error While Auto Cancelling Invoice Request ", e);
            throw e;
        }
    }


    @RequestMapping(method = RequestMethod.POST, value = "create-invoice-request")
    public SalesPerformaInvoice createInvoiceRequest(@RequestBody SalesPerformaInvoice invoice)
            throws SalesPerformaInvoiceException, DataNotFoundException, InventoryUpdateException, SumoException {
        return service.createInvoice(invoice);
    }

    @RequestMapping(method = RequestMethod.POST, value = "approve-invoice-request")
    public SalesPerformaInvoice approveInvoiceRequest(@RequestBody SalesPerformaInvoice invoice)
            throws SalesPerformaInvoiceException, SumoException, FileNotFoundException {
        return service.approveInvoice(invoice, invoice.getCreatedBy().getId());
    }

    @RequestMapping(method = RequestMethod.POST, value = "reject-invoice-request")
    public SalesPerformaInvoice rejectInvoiceRequest(@RequestParam Integer invoiceId, @RequestParam Integer userId)
            throws SalesPerformaInvoiceException, SumoException {
        return service.rejectInvoice(invoiceId, userId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "ready-to-dispatch")
    public SalesPerformaInvoice approveInvoiceRequest(@RequestParam Integer invoiceId, @RequestParam Integer userId)
            throws SalesPerformaInvoiceException, SumoException {
        return service.markInvoiceReadyToDispatch(invoiceId, userId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "update-invoice")
    public Integer updateInvoiceWithGeneratedId() throws SalesPerformaInvoiceException {
        return service.updateGeneratedId();
    }


    @RequestMapping(method = RequestMethod.GET, value = "dispatch")
    public SalesPerformaInvoice dispatchInvoice(@RequestParam Integer invoiceId, @RequestParam Integer userId,@RequestParam String dateOfDelivery)
            throws SalesPerformaInvoiceException, SumoException, ParseException {
        return service.dispatchInvoice(invoiceId, userId,AppUtils.getDate(dateOfDelivery,"yyyy-MM-dd"));
    }

    @RequestMapping(method = RequestMethod.GET, value = "cancel")
    public SalesPerformaInvoice cancelInvoices(@RequestParam Integer invoiceId,
                                               @RequestParam Integer userId,
                                               @RequestParam(required = false) Integer docId )
            throws SalesPerformaInvoiceException, SumoException, InventoryUpdateException {
        return service.cancelInvoice(invoiceId, userId, docId);
    }


    @RequestMapping(method = RequestMethod.GET, value = "closed-invoices")
    public List<SalesPerformaInvoice> cancelInvoice(@RequestParam Integer vendorId,
                                                    @RequestParam Integer dispatchId,
                                                    @RequestParam Integer sendingUnit)
            throws SalesPerformaInvoiceException, SumoException {
        return service.getClosedInvoicesForVendor(vendorId, dispatchId, sendingUnit);
    }


    @RequestMapping(method = RequestMethod.GET, value = "download-excel")
    public View downloadInvoice(@RequestParam Integer invoiceId)
            throws SalesPerformaInvoiceException, SumoException {
        SalesPerformaInvoice invoice = service.getInvoice(invoiceId);
        return excelViewGenerator.generateInvoiceExcel(invoice);
    }

    @RequestMapping(method = RequestMethod.GET, value = "download-cn-dn-excel")
    public View downloadCnOrDnInvoice(@RequestParam Integer invoiceId, @RequestParam String correctedType)
            throws SalesPerformaInvoiceException, SumoException {
        SalesPerformaInvoice invoice = service.getCorrectedInvoice(invoiceId);
        return excelViewGenerator.downloadCnOrDnInvoice(invoice, correctedType);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-invoice-cancel", consumes = MediaType.MULTIPART_FORM_DATA)
    public DocumentDetail uploadCancelInvoiceDocument(HttpServletRequest request,
                                         @RequestParam(value = "type") FileType type,
                                         @RequestParam(value = "mimeType") MimeType mimeType,
                                         @RequestParam(value = "userId") Integer userId,
                                         @RequestParam(value = "docType") DocUploadType docType,
                                         @RequestParam(value = "file") final MultipartFile file) {
        return service.uploadCancelInvoiceDocument(type, mimeType, docType, userId, file);
    }

    @RequestMapping(method = RequestMethod.GET, value = "download-json")
    public void downloadJson(@RequestParam Integer invoiceId, HttpServletResponse response)
            throws SalesPerformaInvoiceException, SumoException, IOException {

        LOG.info("Downloading report for invoice id {}", invoiceId);
        SalesPerformaInvoice invoice = service.getInvoice(invoiceId);
        EWayWrapper data = EWayHelper.convertToEWayData(invoice, masterDataCache, scmCache);
        String filePath = props.getBasePath() + File.separator + "invoices";
        String fileName = "INVOICE_EWAY_JSON_" + invoiceId + "." + MimeType.JSON.extension();

        File file = new File(filePath);
        if (!file.exists()) {
            file.mkdirs();
        }

        String json = JSONSerializer.toJSON(data);
        String path = SCMUtil.write(json.getBytes(StandardCharsets.UTF_8), filePath, "json", fileName, LOG);

        File downloadFile = new File(path);
        FileInputStream inputStream = new FileInputStream(downloadFile);

        response.setContentType(MimeType.JSON.value());
        response.setContentLength((int) downloadFile.length());

        // set headers for the response
        String headerKey = "Content-Disposition";
        String headerValue = String.format("attachment; filename=\"%s\"", downloadFile.getName());
        response.setHeader(headerKey, headerValue);

        // get output stream of the response
        OutputStream outStream = response.getOutputStream();

        byte[] buffer = new byte[4096];
        int bytesRead = -1;

        // write bytes read from the input stream into the output stream
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, bytesRead);
        }

        inputStream.close();
        outStream.close();
        FileUtils.deleteQuietly(downloadFile);
    }

    @RequestMapping(method = RequestMethod.POST, value = "download-Eportal-json")
    public void downloadEportalJson(@RequestParam Integer invoiceId, @RequestParam Integer calculatedDistance, HttpServletResponse response)
            throws SalesPerformaInvoiceException, SumoException, IOException {

        LOG.info("Downloading report for invoice id {}", invoiceId);

        SalesPerformaInvoice invoice = service.getInvoice(invoiceId);

        List<EPortalWrapper> data = EWayHelper.covertToEPortalData(invoice, masterDataCache, scmCache, calculatedDistance);
        String filePath = props.getBasePath() + File.separator + "invoices";
        String fileName = "INVOICE_EPORTAL_JSON_" + invoiceId + "." + MimeType.JSON.extension();

        File file = new File(filePath);
        if (!file.exists()) {
            file.mkdirs();
        }

        String json = JSONSerializer.toJSON(data);
        String path = SCMUtil.write(json.getBytes(StandardCharsets.UTF_8), filePath, "json", fileName, LOG);

        File downloadFile = new File(path);
        FileInputStream inputStream = new FileInputStream(downloadFile);

        response.setContentType(MimeType.JSON.value());
        response.setContentLength((int) downloadFile.length());

        // set headers for the response
        String headerKey = "Content-Disposition";
        String headerValue = String.format("attachment; filename=\"%s\"", downloadFile.getName());
        response.setHeader(headerKey, headerValue);

        // get output stream of the response
        OutputStream outStream = response.getOutputStream();

        byte[] buffer = new byte[4096];
        int bytesRead = -1;

        // write bytes read from the input stream into the output stream
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, bytesRead);
        }

        inputStream.close();
        outStream.close();
        FileUtils.deleteQuietly(downloadFile);
    }

    @RequestMapping(method = RequestMethod.GET, value = "download-correction-Eportal-json")
    public void downloadCorrectionEportalJson(@RequestParam Integer invoiceId,
                                              @RequestParam Integer calculatedDistance,
                                              @RequestParam String type,
                                              HttpServletResponse response)
            throws Exception{

        LOG.info("Downloading corrected report for invoice id {} and correction type {}", invoiceId,type);

        SalesPerformaInvoice invoice = service.getInvoice(invoiceId);
        List<CorrectedSalesInvoiceDetails> correctionDetails = service.getCorrectedInvoiceDetails(invoiceId,type);
        if(correctionDetails.isEmpty()){
            LOG.error("No details found ");
            return;
        }

        CorrectedSalesInvoiceDetails correctedInvoice = correctionDetails.get(0);

        List<EPortalWrapper> data = EWayHelper.covertToCorrectionEPortalData(invoice,correctedInvoice, masterDataCache, scmCache, calculatedDistance, type);
        String filePath = props.getBasePath() + File.separator + "invoices";
        String fileName = "INVOICE_EPORTAL_JSON_" + invoiceId + "." + MimeType.JSON.extension();

        File file = new File(filePath);
        if (!file.exists()) {
            file.mkdirs();
        }

        String json = JSONSerializer.toJSON(data);
        String path = SCMUtil.write(json.getBytes(StandardCharsets.UTF_8), filePath, "json", fileName, LOG);

        File downloadFile = new File(path);
        FileInputStream inputStream = new FileInputStream(downloadFile);

        response.setContentType(MimeType.JSON.value());
        response.setContentLength((int) downloadFile.length());

        // set headers for the response
        String headerKey = "Content-Disposition";
        String headerValue = String.format("attachment; filename=\"%s\"", downloadFile.getName());
        response.setHeader(headerKey, headerValue);

        // get output stream of the response
        OutputStream outStream = response.getOutputStream();

        byte[] buffer = new byte[4096];
        int bytesRead = -1;

        // write bytes read from the input stream into the output stream
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, bytesRead);
        }

        inputStream.close();
        outStream.close();
        FileUtils.deleteQuietly(downloadFile);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-uploadDocId-invoice")
        public Boolean updateUploadDocID(@RequestParam(value = "invoiceId") Integer invoiceId,
                                         @RequestParam(value = "uploadDocId") String uploadDocId) throws IOException, SumoException {
            return service.saveDownloadJsonDocIdNo(invoiceId, uploadDocId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-invoice-sheet", consumes = MediaType.MULTIPART_FORM_DATA)
    public Boolean uploadInvoiceSheet(@RequestParam(value = "file") final MultipartFile file,
                                      @RequestParam(value = "InvoiceId") Integer invoiceId,
                                      @RequestParam(value = "type") String type) throws IOException, SumoException {
        return service.uploadInvoiceSheet(file, invoiceId, type);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-po", consumes = MediaType.MULTIPART_FORM_DATA)
    public DocumentDetail uploadDocument(HttpServletRequest request,
                                         @RequestParam(value = "type") FileType type,
                                         @RequestParam(value = "mimeType") MimeType mimeType,
                                         @RequestParam(value = "poId") String poId,
                                         @RequestParam(value = "userId") Integer userId,
                                         @RequestParam(value = "docType") DocUploadType docType,
                                         @RequestParam(value = "file") final MultipartFile file) {
        return service.uploadDocument(type, mimeType, docType, poId, userId, file);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-invoice", consumes = MediaType.MULTIPART_FORM_DATA)
    public Map<String, String> uploadInvoice(@RequestParam(value = "mimeType") MimeType mimeType,
                                             @RequestParam(value = "invoiceId") Integer invoiceId,
                                             @RequestParam(value = "fileType") InvoiceFileUploadType fileType,
                                             @RequestParam(value = "file") final MultipartFile multipartFile,
                                             @RequestParam(value = "userId") final int userId)
            throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException {

        if (fileType == null) {
            throw new SalesPerformaInvoiceException("No document type detected, Please select a file type");
        }

        if (fileType.equals(InvoiceFileUploadType.INVOICE)) {
            try {
                String fileName = "INVOICE_" + invoiceId +  "." + mimeType.extension();
                File file = fileArchiveService.convertFromMultiPart(fileName, multipartFile);
                return service.uploadInvoice(mimeType, invoiceId, file, userId);
            } catch (IOException | TemplateRenderingException e) {
                LOG.error("Error converting multipart file", e);
                return null;
            }
        }

        if (fileType.equals(InvoiceFileUploadType.EWAY)) {
            return service.uploadEwayBill(mimeType, invoiceId, multipartFile, userId);
        }
        if(fileType.equals(InvoiceFileUploadType.DELIVER)){
            return service.uploadDeliveredDocument(mimeType,invoiceId,multipartFile,userId);
        }

        throw new SalesPerformaInvoiceException("Not a valid request. Please check if the file is correct");
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-url")
    public URL downloadDoc(@RequestParam(value = "invoiceId") Integer invoiceId,
                             @RequestParam(value = "fileType") InvoiceFileUploadType fileType)
            throws SalesPerformaInvoiceException, SumoException, MalformedURLException {

        if (fileType == null) {
            throw new SalesPerformaInvoiceException("No document type detected, Please select a file type");
        }

        if (fileType.equals(InvoiceFileUploadType.INVOICE)) {
            return service.downloadInvoice(invoiceId);
        }

        if (fileType.equals(InvoiceFileUploadType.EWAY)) {
            return service.downloadEway(invoiceId);
        }
        if(fileType.equals(InvoiceFileUploadType.DELIVER)){
            return service.downloadDeliverDoc(invoiceId);
        }
        if(fileType.equals(InvoiceFileUploadType.PURCHASE_ORDER)){
            return service.downloadPoDoc(invoiceId);
        }
        if(fileType.equals(InvoiceFileUploadType.CANCEL_INVOICE)){
            return service.downloadCancelDoc(invoiceId);
        }
        if(fileType.equals(InvoiceFileUploadType.CREDIT_NOTE)){
            return service.downloadCreditNoteDoc(invoiceId);
        }
        if(fileType.equals(InvoiceFileUploadType.DEBIT_NOTE)){
            return service.downloadDebitNoteDoc(invoiceId);
        }
        if(fileType.equals(InvoiceFileUploadType.CORRECTION_CREDIT_NOTE)){
            return service.downloadCorrectionCreditNoteDoc(invoiceId);
        }
        if(fileType.equals(InvoiceFileUploadType.CORRECTION_DEBIT_NOTE)){
            return service.downloadCorrectionDebitNoteDoc(invoiceId);
        }

        throw new SalesPerformaInvoiceException("Not a valid request. Please check if the invoice id is correct");
    }

    @RequestMapping(method = RequestMethod.GET, value="get-outward-form-entries")
    public List<OutwardRegister> getOutWardFormEntries(@RequestParam(value = "sendingUnit") Integer sendingUnit,
                                                       @RequestParam(value = "startDate") String start,
                                                       @RequestParam(value = "endDate") String end,
                                                       @RequestParam(value = "businessType") String businessType) throws IOException, SumoException{
        Date startDate = SCMUtil.parseDate(start);
        Date endDate = AppUtils.getDateAfterDays(SCMUtil.parseDate(end),1);
        return service.getEntries(sendingUnit, startDate, endDate,businessType);
    }


    @RequestMapping(method = RequestMethod.GET, value = "get-invoices")
    public List<SalesPerformaInvoice> getInvoices(
            @RequestParam(value = "sendingUnit") Integer sendingUnit,
            @RequestParam(value = "startDate", required = false) String start,
            @RequestParam(value = "endDate", required = false) String end,
            @RequestParam(value = "fetchPending", required = false) Boolean fetchPending,
            @RequestParam(value = "dispatchId", required = false) Integer dispatchId,
            @RequestParam(value = "vendorId", required = false) Integer vendorId,
            @RequestParam(value = "businessType", required = false) String businessType,
            @RequestParam(value = "raiseCreditNote", required = false) Boolean raiseCreditNote,
            @RequestParam(required = false) List<String> status) {

        Date startDate = SCMUtil.parseDate(start);
        Date endDate = SCMUtil.parseDate(end);
        return service.viewInvoices(sendingUnit, startDate, endDate, status, vendorId, dispatchId, fetchPending,businessType,raiseCreditNote);}

    @RequestMapping(method = RequestMethod.GET, value = "request-orders")
    public List<RequestOrder> getRequestOrders(@RequestParam(value = "from") Integer sendingUnit,
            @RequestParam(value = "to") Integer receivingUnit) {

        return requestOrderService.getRequestOrdersForInvoice(sendingUnit, receivingUnit);
    }

    @RequestMapping(method = RequestMethod.POST, value = "notify-error")
    public void notifyError(@RequestBody Map<String,String> error){
        String productsWithError = error.get("products");
        String vendorName = error.get("vendor");
        String deliveryLocation = error.get("location");
        String dispatchUnit = error.get("dispatchUnit");
        StringBuilder message = new StringBuilder("::::::::::::::::::SALES INVOICE UOM PRICING ERROR:::::::::::::::::::::\n")
                .append(vendorName).append("["+deliveryLocation+"]").append(" for ").append(dispatchUnit + "\n")
                .append("List of products with error: \n")
                .append(productsWithError + "\n");

        notificationService.sendInvoiceErrorNotification(message, SlackNotification.SUPPLY_CHAIN);
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-and-save-b2b-invoice")
    public Map<String, String> generateAndSaveB2BInvoice(@RequestParam Integer invoiceId, @RequestParam Integer userId) throws SalesPerformaInvoiceException, SumoException {
        return service.generateB2BInvoice(invoiceId, userId);
    }

    @PostMapping(
            value = "/generate-b2b-invoice",
            produces = org.springframework.http.MediaType.APPLICATION_PDF_VALUE
    )
    public ResponseEntity<byte[]> generateB2BInvoice(
            @RequestBody Integer invoiceId,
            @RequestParam(required = false) Boolean saveFile
    ) throws Exception {

        byte[] pdfBytes = service.generateB2BInvoicePdf(invoiceId, saveFile);

        return ResponseEntity.ok()
                .contentType(org.springframework.http.MediaType.APPLICATION_PDF)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=b2b-invoice.pdf")
                .body(pdfBytes);
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-and-save-specialized-order-invoice")
    public Pair<URL,GoodsReceived> generateAndSaveSpecializedOrderInvoice(@RequestBody List<GoodsReceived> goodsReceivedList, @RequestParam Integer userId , @RequestParam Integer vendorId , @RequestParam(required = false) Boolean isRegenerated) throws SalesPerformaInvoiceException, SumoException {
        GoodsReceived aggregatedGr = Boolean.TRUE.equals(isRegenerated) ? goodsReceivedList.get(0) : null;
        return service.generateSpecializedOrderInvoice(goodsReceivedList,aggregatedGr,userId,vendorId);
    }


    @RequestMapping(method = RequestMethod.POST, value = "save-special-gr-pr-diff")
    public Boolean generateAndSaveB2BInvoice( @RequestBody GoodsReceived goodsReceived , @RequestParam Integer prId) throws SalesPerformaInvoiceException, SumoException {
        return service.saveGrPrDeviations(goodsReceived, prId);
    }



    @RequestMapping(method = RequestMethod.POST, value="save-outward-register-entry")
    public Boolean saveOutWardRegisterEntry(@RequestBody OutwardRegister outWardRegister) throws IOException, SumoException {
        return service.saveEntry(outWardRegister);
    }

    @GetMapping (value = "approve-cancellation")
    public SalesPerformaInvoice approveCancellation(@RequestParam Integer invoiceId,
                                               @RequestParam Integer userId,
                                               @RequestParam(required = false) Integer docId ,
                                                    @RequestParam String status  )
            throws SalesPerformaInvoiceException, SumoException, InventoryUpdateException {
        return service.approveCancellation(invoiceId, userId, docId,status);
    }


    @PostMapping(value = "save-corrected-invoice-details")
    public boolean saveCorrectedInvoiceDetails(@RequestBody SalesPerformaInvoice salesPerformaInvoice,@RequestParam Integer userId)
            throws Exception {
        return service.saveCorrectedInvoiceDetails(salesPerformaInvoice,userId);
    }

    @GetMapping(value = "get-corrected-invoice-details")
    public List<CorrectedSalesInvoiceDetails> getCorrectedInvoiceDetails(@RequestParam(value = "invoiceId") Integer invoiceId)
            throws Exception {
        return service.getCorrectedInvoiceDetails(invoiceId,null);
    }

    @PostMapping(value = "save-credit-debit-note-details")
    public boolean saveCreditDebitNoteDetails(@RequestBody CreditDebitNoteDetail creditDebitNoteDetail)
            throws Exception {
        return service.saveCreditDebitNoteDetails(creditDebitNoteDetail);
    }

    @GetMapping(value = "get-credit-debit-note-details")
    public List<CreditDebitNoteDetail> getCreditNoteDetails(@RequestParam(value = "startDate") String start,
                                                      @RequestParam(value = "endDate") String end,
                                                            @RequestParam(value = "vendorId") Integer vendorId,
                                                      @RequestParam(value = "status") String status)
            throws Exception {
        Date startDate = SCMUtil.parseDate(start);
        Date endDate = SCMUtil.parseDate(end);
        return service.getCreditDebitNoteDetails(startDate,endDate,status, vendorId);
    }

    @PostMapping(value = "approve-credit-note")
    public boolean approveCreditNote(@RequestBody CreditDebitNoteDetail creditDebitNoteDetail,
                                     @RequestParam(value = "userId") Integer userId)
            throws Exception {
        return service.approveCreditNote(creditDebitNoteDetail,userId);
    }

    @PostMapping(value = "reject-credit-note")
    public boolean rejectCreditNote(@RequestBody CreditDebitNoteDetail creditDebitNoteDetail,
                                     @RequestParam(value = "userId") Integer userId)
            throws Exception {
        return service.rejectCreditNote(creditDebitNoteDetail,userId);
    }

    @PostMapping(value = "upload-vendor-invoice")
    public DocumentDetail uploadVendorInvoice(@RequestParam(value = "mimeType") MimeType mimeType,
                                       @RequestParam(value = "file") final MultipartFile multipartFile,
                                       @RequestParam(value = "userId") final int userId)
            throws Exception {
         return service.uploadVendorInvoice(FileType.SALES_INVOICE,mimeType,DocUploadType.VENDOR_INVOICE,userId,multipartFile);
    }

    @GetMapping(value = "get-credit-note-detail")
    public CreditDebitNoteDetail getDetail(@RequestParam(value = "id") Integer id)
            throws Exception {
        return service.getCreditDebitDetail(id);
    }

    @PostMapping(value = "generate-credit-note")
    public Map<String, String> generateCreditNote(@RequestParam Integer invoiceId, @RequestParam Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException {
        return service.generateCreditNote(invoiceId, userId);
    }

    @PostMapping(value = "generate-debit-note")
    public Map<String, String> generateDebitNote(@RequestParam Integer invoiceId, @RequestParam Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException {
        return service.generateDebitNote(invoiceId, userId);
    }

    @PostMapping(value = "generate-correction-credit-note")
    public Map<String, String> generateCorrectionCreditNote(@RequestParam Integer invoiceId, @RequestParam Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException {
        return service.generateCorrectionCreditNote(invoiceId, userId);
    }

    @PostMapping(value = "generate-correction-debit-note")
    public Map<String, String> generateCorrectionDebitNote(@RequestParam Integer invoiceId, @RequestParam Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException {
        return service.generateCorrectionDebitNote(invoiceId, userId);
    }

    @PostMapping(value = "upload-correction-invoice-sheet", consumes = MediaType.MULTIPART_FORM_DATA)
    public Boolean uploadCorrectionInvoiceSheet(@RequestParam(value = "file") final MultipartFile file,
                                                @RequestParam(value = "InvoiceId") Integer invoiceId,
                                                @RequestParam(value = "type") String type) throws IOException, SumoException {
        return service.uploadCorrectionInvoiceSheet(file, invoiceId, type);
    }

    @PostMapping(value = "validate-and-close")
    public Boolean validateAndClose(@RequestParam(value = "invoiceId") Integer invoiceId,
                                    @RequestParam(value = "userId") Integer userId) throws IOException, SumoException, SalesPerformaInvoiceException {
        return service.validateAndClose(invoiceId,userId);
    }

    @GetMapping("get-gst-types")
    public ApiResponse getGstTypes() {
        return new ApiResponse(GstInvoiceType.values());
    }

}
