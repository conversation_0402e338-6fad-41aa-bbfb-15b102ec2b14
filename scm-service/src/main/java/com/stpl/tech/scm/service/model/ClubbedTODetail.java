package com.stpl.tech.scm.service.model;

import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.TransferOrder;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 02-03-2017.
 */
public class ClubbedTODetail {

    private List<Integer> clubRoIds;
    private Date fulfilmentDate;
    private IdCodeName requestCompany;
    private IdCodeName fulfillmentCompany;
    private TransferOrder transferOrder;

    public List<Integer> getClubRoIds() {
        return clubRoIds;
    }

    public void setClubRoIds(List<Integer> clubRoIds) {
        this.clubRoIds = clubRoIds;
    }

    public Date getFulfilmentDate() {
        return SCMUtil.getDate(fulfilmentDate);
    }

    public void setFulfilmentDate(Date fulfilmentDate) {
        this.fulfilmentDate = fulfilmentDate;
    }

    public TransferOrder getTransferOrder() {
        return transferOrder;
    }

    public void setTransferOrder(TransferOrder transferOrder) {
        this.transferOrder = transferOrder;
    }

    public IdCodeName getRequestCompany() {
        return requestCompany;
    }

    public void setRequestCompany(IdCodeName requestCompany) {
        this.requestCompany = requestCompany;
    }

    public IdCodeName getFulfillmentCompany() {
        return fulfillmentCompany;
    }

    public void setFulfillmentCompany(IdCodeName fulfillmentCompany) {
        this.fulfillmentCompany = fulfillmentCompany;
    }
}
