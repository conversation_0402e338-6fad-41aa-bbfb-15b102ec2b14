package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.StockRedistributionService;
import com.stpl.tech.scm.data.dao.impl.MutexFactory;
import com.stpl.tech.scm.domain.model.RiderActionEnum;
import com.stpl.tech.scm.domain.model.RiderRoutePlanDataDto;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.StockType;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR + SCMServiceConstants.STOCK_REDISTRIBUTION_ROOT_CONTEXT)
@Log4j2
public class StockRedistributionManagementResource extends AbstractSCMResources{

    @Autowired
    private StockRedistributionService stockRedistributionService;

    @Autowired
    private MutexFactory<String> factory;

    @GetMapping(value = "get-rider-route-plan-by-employee-id", produces = MediaType.APPLICATION_JSON)
    public List<RiderRoutePlanDataDto> getRiderRoutePlanByEmployeeId(@RequestParam Integer employeeId) throws SumoException {
        return stockRedistributionService.getRiderRoutePlanByEmployeeId(employeeId);
    }

    @PutMapping(value = "update-rider-route-plan-item", produces = MediaType.APPLICATION_JSON)
    public boolean updateRiderRoutePlanItem(HttpServletRequest httpServletRequest,
                                            @RequestParam Integer riderRoutePlanItemDataId, @RequestParam BigDecimal finalQuantity, @RequestParam String comment) throws SumoException {
        synchronized (factory.getMutex(riderRoutePlanItemDataId + "_updateRiderRoutePlanItem")) {
            Integer loggedInUser = getLoggedInUser(httpServletRequest);
            return stockRedistributionService.updateRiderRoutePlanItem(riderRoutePlanItemDataId, finalQuantity, loggedInUser, comment);
        }
    }

    @PutMapping(value = "complete-route-step-of-ride", produces = MediaType.APPLICATION_JSON)
    public boolean completeRouteStepOfRide(HttpServletRequest httpServletRequest, @RequestParam Integer riderRoutePlanStepDataId, @RequestParam Integer riderRoutePlanDataId,
                                           @RequestParam(name = "riderAction") RiderActionEnum riderAction, @RequestParam BigDecimal temperature) throws SumoException {
        synchronized (factory.getMutex(riderRoutePlanDataId + riderAction.name())) {
            Integer loggedInUser = getLoggedInUser(httpServletRequest);
            boolean result = stockRedistributionService.completeRouteStepOfRide(riderRoutePlanStepDataId, riderAction, loggedInUser, temperature);
            stockRedistributionService.checkAndMarkRideAsCompleted(riderRoutePlanDataId);
            return result;
        }
    }

    @GetMapping(value = "get-rider-unit-inventory", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, Pair<String, BigDecimal>> getRiderUnitInventory(HttpServletRequest httpServletRequest) throws SumoException {
        Integer loggedInUser = getLoggedInUser(httpServletRequest);
        return stockRedistributionService.getRiderUnitInventory(loggedInUser);
    }

    @Scheduled(cron="0 5 8,14 * * *", zone = "GMT+05:30")
    @PostMapping(value = "set-packaging-info-to-route-items", produces = MediaType.APPLICATION_JSON)
    public void setPackagingInfoToRouteItems() throws SumoException {
        stockRedistributionService.setPackagingInfoToRouteItems();
    }

    @Scheduled(cron="0 30 6 * * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value = "expire-pending-route-plans")
    public void expirePendingRoutePlans() {
        log.info("::: Started expirePendingRoutePlans :::");
        stockRedistributionService.expirePendingRoutePlans();
    }


}
