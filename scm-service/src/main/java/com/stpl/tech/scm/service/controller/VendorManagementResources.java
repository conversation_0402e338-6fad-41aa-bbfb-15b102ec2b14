package com.stpl.tech.scm.service.controller;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMVendorManagementService;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.IdCodeNameType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.UnitVendorMapping;
import com.stpl.tech.scm.domain.model.VendorAccountVO;
import com.stpl.tech.scm.domain.model.VendorBlockResponse;
import com.stpl.tech.scm.domain.model.VendorComplianceType;
import com.stpl.tech.scm.domain.model.VendorComplianceValidationRequest;
import com.stpl.tech.scm.domain.model.VendorDebitBalanceVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorEditVO;
import com.stpl.tech.scm.domain.model.VendorRegistrationRequest;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.scm.domain.model.VendorType;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.service.model.DateLimitData;
import com.stpl.tech.scm.domain.model.SkuPriceDataObject;
import com.stpl.tech.scm.domain.model.SkuPriceHistoryObject;
import com.stpl.tech.scm.service.model.VendorApproveVO;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 07-05-2016.
 */

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.VENDOR_MANAGEMENT_ROOT_CONTEXT)
public class VendorManagementResources extends AbstractSCMResources {
	private static final Logger LOG = LoggerFactory.getLogger(VendorManagementResources.class);

	@Autowired
	private SCMVendorManagementService scmVendorManagementService;


	@Autowired
	private EnvProperties props;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Autowired
	private ExcelViewGenerator excelViewGenerator;

	@RequestMapping(method = RequestMethod.GET, value = "vendor", produces = MediaType.APPLICATION_JSON)
	public VendorDetail viewVendor(@RequestParam final int vendorId) {
		return scmVendorManagementService.viewVendor(vendorId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "vendors", produces = MediaType.APPLICATION_JSON)
	public List<VendorDetail> viewAllVendors() {
		return scmVendorManagementService.viewAllVendors();
	}

	@RequestMapping(method = RequestMethod.GET, value = "vendors-trimmed", produces = MediaType.APPLICATION_JSON)
	public Collection<IdCodeNameType> viewAllVendorsTrimmed() {
		return scmVendorManagementService.viewAllVendorsTrimmed();
	}

	@RequestMapping(method = RequestMethod.GET, value = "service-vendors", produces = MediaType.APPLICATION_JSON)
	public Collection<IdCodeName> getServiceVendors() {
		return scmVendorManagementService.getServiceVendors();
	}

	@RequestMapping(method = RequestMethod.GET, value = "vendor-locations", produces = MediaType.APPLICATION_JSON)
	public Collection<IdCodeNameStatus> getVendorLocations(@RequestParam final int vendorId) {
		return scmVendorManagementService.getVendorLocations(vendorId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "vendor-type", produces = MediaType.APPLICATION_JSON)
	public VendorType[] viewAllVendorTypes() {
		return VendorType.values();
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public VendorDetail addVendor(@RequestBody final VendorDetail vendorDetail) throws SumoException {
		return scmVendorManagementService.addVendor(vendorDetail);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "vendor", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public VendorDetail updateVendor(@RequestBody final VendorDetail vendorDetail) {
		return scmVendorManagementService.updateVendor(vendorDetail);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "vendor-deactivate", produces = MediaType.APPLICATION_JSON)
	public boolean deactivateVendor(@RequestParam final int vendorId) {
		return scmVendorManagementService.deactivateVendor(vendorId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "vendor-activate", produces = MediaType.APPLICATION_JSON)
	public boolean activateVendor(@RequestBody final int vendorId) {
		return scmVendorManagementService.activateVendor(vendorId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor-edit", produces = MediaType.APPLICATION_JSON)
	public boolean editVendor(@RequestBody final VendorEditVO edit, HttpServletRequest request) throws SumoException {
		return scmVendorManagementService.editVendorRequest(edit.getVendorId(), edit.getUserId(), getLoggedInUnit(request));
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor-request-approve", produces = MediaType.APPLICATION_JSON)
	public boolean approveVendorRequest(@RequestBody final VendorApproveVO reqObj) {
		boolean flag = false;
		if (reqObj.getRequestId() != 0 && reqObj.getUserId() != 0 && reqObj.getCreditCycle() >= 0) {
			flag = scmVendorManagementService.approveVendor(reqObj.getCreditCycle(), reqObj.getRequestId(),
					reqObj.getUserId(), reqObj.getLeadTime());
		}
		return flag;
	}

	@RequestMapping(method = RequestMethod.GET, value = "unit-vendors", produces = MediaType.APPLICATION_JSON)
	public List<UnitVendorMapping> getUnitVendors(@RequestParam final int unitId) {
		return scmVendorManagementService.getUnitVendors(unitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "unit-vendor-add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public UnitVendorMapping unitVendorAdd(@RequestBody final UnitVendorMapping mapping) throws SumoException {
		return scmVendorManagementService.unitVendorAdd(mapping);
	}

	@RequestMapping(method = RequestMethod.POST, value = "unit-vendor-update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean unitVendorUpdate(@RequestBody final UnitVendorMapping mapping) {
		return scmVendorManagementService.unitVendorUpdate(mapping);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "unit-vendor-activate", produces = MediaType.APPLICATION_JSON)
	public boolean unitVendorActivate(@RequestBody final int mappingId) {
		return scmVendorManagementService.unitVendorActivate(mappingId);
	}

	@RequestMapping(method = RequestMethod.PUT, value = "unit-vendor-deactivate", produces = MediaType.APPLICATION_JSON)
	public boolean unitVendorDeactivate(@RequestBody final int mappingId) {
		return scmVendorManagementService.unitVendorDeactivate(mappingId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/registration/request", produces = MediaType.APPLICATION_JSON)
	public boolean addVendorRegistrationRequest(@RequestBody final VendorRegistrationRequest request, HttpServletRequest servletRequest) throws SumoException {
		request.setUnitId(getLoggedInUnit(servletRequest));
		return scmVendorManagementService.addVendorRegistrationRequest(request, true);
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/registrations", produces = MediaType.APPLICATION_JSON)
	public List<VendorRegistrationRequest> getVendorRegistrationRequests(@RequestBody final DateLimitData data) {
		return scmVendorManagementService.getAllVendorRegistrationRequests(data.getStartDate(), data.getEndDate(),
				data.isGetPendingOnly());
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/registrations/status", produces = MediaType.APPLICATION_JSON)
	public List<VendorRegistrationRequest> getVendorRegistrationRequests(@RequestBody Map<String, Object> payload) {
		List<String> status = (List<String>) payload.get("status");
		Integer userId = (Integer) payload.get("userId");
		return scmVendorManagementService.getAllVendorRegistrationRequests(status, userId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/submit-document", produces = MediaType.APPLICATION_JSON)
	public boolean submitVendorDocument (@RequestBody VendorRegistrationRequest registrationRequest) {
		return scmVendorManagementService.submitVendorDocument(registrationRequest);
	}


	@RequestMapping(method = RequestMethod.GET, value = "all-Vendor", produces = MediaType.APPLICATION_JSON)
	public Collection<IdCodeNameType> getVendorName() {
			return scmVendorManagementService.getAllVendorName();
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/status/{requestStatus}", produces = MediaType.APPLICATION_JSON)
	public List<VendorDetail> getVendorDetails(@PathVariable(value = "requestStatus") String requestStatus) {
		if(requestStatus.toLowerCase().equals("all")){
		    return scmVendorManagementService.getAllVendors();
        }else{
            return scmVendorManagementService.getAllVendorFromCache(requestStatus, false);
        }

	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/status/short/{requestStatus}", produces = MediaType.APPLICATION_JSON)
	public List<VendorDetail> getShortVendorDetails(@PathVariable(value = "requestStatus") String requestStatus) {
		if(requestStatus.toLowerCase().equals("all")){
			return scmVendorManagementService.getAllVendorFromCache(null,false);
		}else {
			return scmVendorManagementService.getAllShortVendor(requestStatus, false);
		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "findVendorDetail", produces = MediaType.APPLICATION_JSON)
	public VendorDetail getShortVendorDetail(@RequestParam(value = "vendorId") Integer vendorId) {
			return scmVendorManagementService.getShortVendor(vendorId);
	}


	@RequestMapping(method = RequestMethod.POST, value = "vendor/registration/request/cancel", produces = MediaType.APPLICATION_JSON)
	public boolean cancelVendorRegistrationRequest(@RequestBody final Integer id) {
		return scmVendorManagementService.cancelVendorRegistrationRequest(id);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-vendor-status", produces = MediaType.APPLICATION_JSON)
	public VendorStatus[] getVendorStatus() throws VendorRegistrationException {
		return VendorStatus.values();
	}

	@RequestMapping(method = RequestMethod.POST, value = "download-document")
	public void downloadFile(HttpServletResponse response,@RequestBody DocumentDetail document) throws IOException{
		scmVendorManagementService.downloadFile(response,document);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-document-detail-by-id")
	public DocumentDetail getDocumentDetailById(@RequestParam(value = "documentId") Integer documentId){
		return scmVendorManagementService.getDocumentDetailById(documentId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/payments/block", produces = MediaType.APPLICATION_JSON)
	public boolean blockVendorPayments(@RequestBody final VendorEditVO request) throws SumoException {
		return scmVendorManagementService.blockVendorPayments(request);
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/payments/un-block", produces = MediaType.APPLICATION_JSON)
	public boolean unBlockVendorPayments(@RequestBody final VendorEditVO request) throws SumoException {
		return scmVendorManagementService.unBlockVendorPayments(request);
	}

	@RequestMapping(method = RequestMethod.GET, value = "vendor/debit-balance-sheet/download")
	public View getVendorDebitBalanceView(@RequestParam final int companyId) throws SumoException {
		List<VendorDetail> vendorDetails = scmVendorManagementService.getAllVendorFromCache("ACTIVE", true).stream()
				.filter(vendorDetail -> vendorDetail.getAccountDetails()!=null).collect(Collectors.toList());
		return excelViewGenerator.getVendorDebitBalanceView(vendorDetails, companyId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/debit-balance-sheet/upload", consumes = MediaType.MULTIPART_FORM_DATA)
	public List<VendorDebitBalanceVO> uploadDebitBalanceSheet(HttpServletRequest request,
															  @RequestParam(value = "file") final MultipartFile file
	) throws IOException, SumoException {
		return scmVendorManagementService.uploadDebitBalanceSheet(file);
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/tds-mailing-sheet/upload", consumes = MediaType.MULTIPART_FORM_DATA)
	public boolean uploadTDSMailingSheet(
			@RequestParam(value = "mimeType") MimeType mimeType,
			@RequestParam(value = "email") String emails,
			@RequestParam(value = "fy") String financialYear,
			@RequestParam(value = "vendorName") String vendorName,
			@RequestParam(value = "panNumber") String panNumber,
			@RequestParam(value = "quarter") String quarter,
			@RequestParam(value = "file") final MultipartFile file
	) throws IOException, SumoException {
		return scmVendorManagementService.uploadTDSMailingSheet(mimeType,emails,financialYear,vendorName,panNumber,quarter,file);
	}

	@RequestMapping(method = RequestMethod.POST, value = "vendor/save-debit-balances")
	public boolean saveDebitBalances(@RequestBody List<VendorDebitBalanceVO> vendorDebitBalanceVOS) throws SumoException, EmailGenerationException {
		return scmVendorManagementService.saveDebitBalances(vendorDebitBalanceVOS);
	}

	@RequestMapping(method = RequestMethod.GET, value = "vendor/get-account")
	public VendorAccountVO saveDebitBalances(@RequestParam(name = "vendorId", required = true) Integer vendorId) throws SumoException, EmailGenerationException {
		VendorDetail vendorDetail = scmVendorManagementService.getVendor(vendorId);
		if(vendorDetail.getAccountDetails()!=null){
			return new VendorAccountVO(vendorDetail.getVendorId(), vendorDetail.getEntityName(),
					vendorDetail.getAccountDetails().getAccountNumber(), vendorDetail.getAccountDetails().getIfscCode());
		}
		return null;
	}

	@RequestMapping(method = RequestMethod.GET, value = "vendor-credit-cycle")
	public boolean updateCompanyCreditCycle(@RequestParam(name = "vendorcompanyId", required = true) String vendorcompanyId,@RequestParam(name = "vendorCompCreditCycle", required = true) String vendorCompCreditCycle) {
		return scmVendorManagementService.updateCreditCycle(Integer.parseInt(vendorcompanyId),Integer.parseInt(vendorCompCreditCycle));
	}

	@RequestMapping(method = RequestMethod.GET, value = "vendor-deactivate-partial", produces = MediaType.APPLICATION_JSON)
	public String vendorDeactivateStatus(@RequestParam(name = "vendorId", required = true)Integer vendorId) {
		return scmVendorManagementService.checkVendorDeactivateStatus(vendorId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "vendor-reactivate", produces = MediaType.APPLICATION_JSON)
	public boolean reactivateVendor(@RequestParam(name = "vendorId", required = true)Integer vendorId) {
		return scmVendorManagementService.reactivateVendor(vendorId);
	}


	@RequestMapping(method = RequestMethod.POST, value = "save-lead-time", produces = MediaType.APPLICATION_JSON)
	public boolean saveLeadTime(@RequestBody final VendorApproveVO reqObj) {
		return scmVendorManagementService.saveLeadTime(reqObj.getRequestId(),reqObj.getLeadTime());
		}

	@RequestMapping(method = RequestMethod.POST, value = "get-price-history", produces = MediaType.APPLICATION_JSON)
	public List<SkuPriceHistoryObject> getPriceHistory(@RequestBody SkuPriceDataObject reqObj){
		return scmVendorManagementService.getPriceHistory(reqObj);
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-unblock-till-date", produces = MediaType.APPLICATION_JSON)
	public Boolean updateVendorUnblockTillDate(@RequestParam Integer vendorId, @RequestParam String unblockTillDate,@RequestParam Integer updatedBy) {
		return scmVendorManagementService.updateVendorUnblockTillDate(vendorId, unblockTillDate, updatedBy);
	}

	@PostMapping(value = "block-unblock-vendor", produces = MediaType.APPLICATION_JSON)
	public Boolean blockUnblockVendor(@RequestParam Integer vendorId,@RequestParam Integer updatedBy, @RequestParam Boolean isBlock) {
		return scmVendorManagementService.blockUnblockVendor(vendorId, updatedBy, isBlock);
	}

	@GetMapping(value = "get-pending-orders-to-block-vendor", produces = MediaType.APPLICATION_JSON)
	public VendorBlockResponse getPendingOrdersToBlockVendor(@RequestParam Integer vendorId) {
		return scmVendorManagementService.getPendingOrdersToBlockVendor(vendorId);
	}

	@PostMapping(value = "vendor-detail-edit-request", produces = MediaType.APPLICATION_JSON)
	public boolean setVendorRequest(@RequestParam("vendorId")  final Integer vendorId,
									@RequestParam("userId")  final Integer userId,
									HttpServletRequest request,
									@RequestBody  final Object vendorDetailChangeObj) throws Exception {
		 return scmVendorManagementService.setVendorEditRequest(userId,vendorId, getLoggedInUnit(request), vendorDetailChangeObj);
	}

	@PostMapping(value = "check-vendor-compliance-monthly", produces = MediaType.APPLICATION_JSON)
	@Scheduled(cron = "0 0 7 14-18 * *", zone = "GMT+05:30")
	public boolean checkVendorComplianceMonthly() {
		if (AppUtils.isProd(props.getEnvType())) {
			LOG.info(":: RUNNING MONTHLY VENDOR COMPLIANCE VALIDATION ::");
			if (AppUtils.getMonth(AppUtils.getCurrentTimestamp()) == 1 || AppUtils.getMonth(AppUtils.getCurrentTimestamp()) == 4 || AppUtils.getMonth(AppUtils.getCurrentTimestamp()) == 7 || AppUtils.getMonth(AppUtils.getCurrentTimestamp()) == 10) {
				LOG.info(":: RUNNING Monthly Along With Quarterly Validation :: ");
				return false;
			} else {
				return scmVendorManagementService.checkVendorCompliance(null, Arrays.asList(VendorComplianceType.GSTIN_VERIFICATION.value(), VendorComplianceType.SECTION_206.value()), null);
			}
		}
		return false;
	}

	@PostMapping(value = "check-vendor-compliance-quarterly", produces = MediaType.APPLICATION_JSON)
	@Scheduled(cron = "0 0 7 14-18 1/3 *", zone = "GMT+05:30")
	public boolean checkVendorComplianceQuarterly() {
		if (AppUtils.isProd(props.getEnvType())) {
			LOG.info(":: RUNNING QUARTERLY VENDOR COMPLIANCE VALIDATION ::");
			return scmVendorManagementService.checkVendorCompliance(null, null, null);
		}
		return false;
	}

	@PostMapping(value = "validate-vendor-compliances", produces = MediaType.APPLICATION_JSON)
	public boolean checkVendorCompliance(@RequestBody VendorComplianceValidationRequest vendorComplianceValidationRequest) {
		return scmVendorManagementService.checkVendorCompliance(vendorComplianceValidationRequest.getVendorIds(), vendorComplianceValidationRequest.getComplianceTypes(),
				vendorComplianceValidationRequest.getForceRetry());
	}

	@GetMapping("check-vendor-contract-status")
	public boolean checkVendorContractStatus(@RequestParam final Integer vendorId){
		return scmVendorManagementService.checkVendorContractStatusV2(vendorId);
	}

	@GetMapping("update-pan-status-sandbox")
	public Boolean updateVendorPanStatusFromComplianceData() throws IOException {
		return scmVendorManagementService.updateVendorPanStatusFromComplianceData();
	}

}
