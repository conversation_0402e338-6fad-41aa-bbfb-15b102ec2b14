package com.stpl.tech.scm.service.controller;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.CreateVendorGrVO;
import com.stpl.tech.scm.data.dao.impl.MutexFactory;
import com.stpl.tech.scm.domain.model.BulkRequestVO;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GrItemQuantityUpdation;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SpecializedOrderInvoice;
import com.stpl.tech.scm.domain.model.VendorGR;
import com.stpl.tech.scm.service.annotation.DayClosureCheck;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.data.transport.model.GrStockEvent;
import com.stpl.tech.scm.service.model.InvoiceFileUploadType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT)
public class GoodReceiveManagementResources extends AbstractSCMResources {

    private static final Logger LOG = LoggerFactory.getLogger(GoodReceiveManagementResources.class);

    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;

    @Autowired
    private EnvProperties props;

    @Autowired
    private ExcelViewGenerator excelViewGenerator;

    @Autowired
    private MutexFactory<Integer> mutex;

    @RequestMapping(method = RequestMethod.GET, value = "good-received-pending", produces = MediaType.APPLICATION_JSON)
    public List<GoodsReceived> getPendingGrs(@RequestParam final Integer unitId , @RequestParam(required = false) final Integer skuId  ,
                                             @RequestParam(required = false) Boolean fetchRejected) {
        return goodsReceiveManagementService.getPendingGrs(unitId,skuId , fetchRejected);
    }

    @RequestMapping(method = RequestMethod.GET, value = "good-received-pending-disputed", produces = MediaType.APPLICATION_JSON)
    public List<GoodsReceived> getPendingDisputedGrs(@RequestParam final Integer unitId , @RequestParam(required = false) final Integer skuId) {
        return goodsReceiveManagementService.getPendingDisputedGrs(unitId,skuId,false);
    }



    @RequestMapping(method = RequestMethod.GET, value = "good-received-raised-disputed", produces = MediaType.APPLICATION_JSON)
    public List<GoodsReceived> getDisputedGrs(@RequestParam final Integer unitId , @RequestParam(required = false) final Integer skuId) {
        return goodsReceiveManagementService.getRaisedDisputedGrs(unitId ,skuId,false);
    }

    @RequestMapping(method = RequestMethod.GET, value = "good-received", produces = MediaType.APPLICATION_JSON)
    public GoodsReceived getGoodsReceivedDetail(@RequestParam(required = true) final int grId) {
        return goodsReceiveManagementService.getGoodsReceivedDetail(grId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-POD", consumes = MediaType.MULTIPART_FORM_DATA )
    public DocumentDetail uploadPOD(HttpServletRequest request,
                                         @RequestParam(value = "type") FileType type,
                                         @RequestParam(value = "mimeType") MimeType mimeType,
                                         @RequestParam(value = "userId") Integer userId,
                                         @RequestParam(value = "docType") DocUploadType docType,
                                         @RequestParam(value = "file") final MultipartFile file,
                                         @RequestParam(value = "grId")Integer grId) throws DocumentException, IOException, SumoException {
        return goodsReceiveManagementService.uploadPOD(type, mimeType, docType, userId, file,grId);
    }
    @PostMapping(value = "upload-proof-of-rejection", consumes = MediaType.MULTIPART_FORM_DATA )
    public DocumentDetail uploadProofOfRejection(HttpServletRequest request,
                                                 @RequestParam(value = "type") FileType type,
                                                 @RequestParam(value = "mimeType") MimeType mimeType,
                                                 @RequestParam(value = "userId") Integer userId,
                                                 @RequestParam(value = "docType") DocUploadType docType,
                                                 @RequestParam(value = "fileName") String fileNameExt,
                                                 @RequestParam(value = "file") final MultipartFile file,
                                                 @RequestParam(value = "grId") Integer grId) throws DocumentException, IOException, SumoException {
        return goodsReceiveManagementService.uploadProofOfRejection(type, mimeType, docType, userId, file,grId,fileNameExt);
    }

    @PostMapping(value = "por-image-urls", produces = MediaType.APPLICATION_JSON)
    public List<String> getPorImageUrls(@RequestBody final int grId) throws IOException {
        return goodsReceiveManagementService.getPorImageUrls(grId);
    }

    @DayClosureCheck
    @RequestMapping(method = RequestMethod.PUT, value = "good-received-settle", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean settleGoodsReceivedDetail(HttpServletRequest request, @RequestBody final GoodsReceived goodsReceived)
            throws InventoryUpdateException, SumoException, DayCloseInitiatedException, ParseException {
        LOG.info("received settle good received for gr Id {} at time {} milliseconds", goodsReceived.getId(),
                System.currentTimeMillis());
        Boolean success = goodsReceiveManagementService.settleGoodsReceivedDetail(goodsReceived);
        if (success) {
            LOG.info("Successfully settled gr with gr Id {}", goodsReceived.getId());
        } else {
            LOG.info("Failed in settling gr with gr Id {}", goodsReceived.getId());
        }
        return success;
    }

    @RequestMapping(method = RequestMethod.GET, value = "is-gr-settled", produces = MediaType.APPLICATION_JSON)
    public boolean isGrSettled(@RequestParam Integer grId) {
        return goodsReceiveManagementService.isGrSettled(grId);
    }

    @DayClosureCheck
    @RequestMapping(method = RequestMethod.PUT, value = "good-received-cancel", produces = MediaType.APPLICATION_JSON)
    public boolean cancelGoodsReceivedDetail(HttpServletRequest request, @RequestBody final Integer grId)
            throws DayCloseInitiatedException {
        return goodsReceiveManagementService.cancelGoodsReceivedDetail(grId);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "rejected-good-received-found", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean rejectedGoodsReceivedFound(HttpServletRequest request, @RequestBody final GoodsReceived goodsReceived) throws InventoryUpdateException, SumoException, ParseException {
        return goodsReceiveManagementService.rejectedGoodsReceivedFound(goodsReceived);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "rejected-good-received-lost", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean rejectedGoodsReceivedLost(HttpServletRequest request, @RequestBody final GoodsReceived goodsReceived) throws InventoryUpdateException, SumoException, ParseException, DataNotFoundException {
        return goodsReceiveManagementService.rejectedGoodsReceivedLost(goodsReceived);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "rejected-good-received-decline", produces = MediaType.APPLICATION_JSON)
    public boolean rejectedGoodsReceivedDecline(HttpServletRequest request, @RequestBody final GoodsReceived goodsReceived) throws SumoException {
        return goodsReceiveManagementService.rejectedGoodsReceivedDecline(goodsReceived);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "accept-good-received-declined", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean acceptDeclinedGoodsReceived(HttpServletRequest request, @RequestBody final GoodsReceived goodsReceived)
            throws InventoryUpdateException, SumoException, ParseException, DataNotFoundException {
        return goodsReceiveManagementService.rejectedGoodsReceivedLost(goodsReceived);
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-grItem-quantity-update-reasons", produces = MediaType.APPLICATION_JSON)
    public List<GrItemQuantityUpdation> grItemQuantityDeviations() {
        return goodsReceiveManagementService.grItemQuantityDeviations();
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-milk-grs-for-payment", produces = MediaType.APPLICATION_JSON)
    public List<GoodsReceived> getMilkGrsForPayment(@RequestParam(required = true) Integer vendorId,
    @RequestParam Integer deliveryUnitId,
    @RequestParam(required = false) String startDate,
    @RequestParam(required = false) String endDate){
        Date start = SCMUtil.getDate(SCMUtil.parseDate(startDate));
        Date end = SCMUtil.getDate(SCMUtil.getNextDate(SCMUtil.parseDate(endDate)));
        return goodsReceiveManagementService.findMilkGrsForPayment(deliveryUnitId,start,end,vendorId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-milk-invoices-for-payment", produces = MediaType.APPLICATION_JSON)
    public List<SpecializedOrderInvoice> getMilkInvoicesForPayment(@RequestParam(required = true) Integer vendorId,
                                                                   @RequestParam Integer deliveryUnitId,
                                                                   @RequestParam(required = false) String startDate,
                                                                   @RequestParam(required = false) String endDate){
        Date start = SCMUtil.getDate(SCMUtil.parseDate(startDate));
        Date end = SCMUtil.getDate(SCMUtil.getNextDate(SCMUtil.parseDate(endDate)));
        return goodsReceiveManagementService.getMilkInvoicesForPayment(deliveryUnitId,start,end,vendorId);
    }



    @RequestMapping(method = RequestMethod.GET, value = "good-received-find", produces = MediaType.APPLICATION_JSON)
    public List<GoodsReceived> findGoodsReceived(@RequestParam(required = false) final Integer generationUnitId,
                                                 @RequestParam(required = false) final Integer generatedForUnitId,
                                                 @RequestParam(required = true) final String startDate,
                                                 @RequestParam(required = true) final String endDate,
                                                 @RequestParam(required = false) final SCMOrderStatus status,
                                                 @RequestParam(required = false) final Integer goodsReceiveOrderId,
                                                 @RequestParam(required = false) final Integer skuId) {
        Date start = SCMUtil.getDate(SCMUtil.parseDate(startDate));
        Date end = SCMUtil.getDate(SCMUtil.getNextDate(SCMUtil.parseDate(endDate)));
        return goodsReceiveManagementService.findGoodsReceived(generationUnitId, generatedForUnitId, start, end, status, goodsReceiveOrderId,skuId);
    }


    /*********************
     * Vendor Receivings related mappings this point onwards
     * @throws InventoryUpdateException
     * @throws SumoException
     * @throws DataUpdationException
     * @throws Exception
     ***************************/

    @DayClosureCheck
    @RequestMapping(method = RequestMethod.POST, value = "create-vendor-gr", produces = MediaType.APPLICATION_JSON)
    public int createVendorGR(HttpServletRequest request, @RequestBody final CreateVendorGrVO grVO)
            throws DataUpdationException, SumoException, InventoryUpdateException,DayCloseInitiatedException {
        try {
            return goodsReceiveManagementService.createVendorGR(grVO);
        } catch (SumoException | InventoryUpdateException | DataUpdationException e) {
            LOG.error("Encountered error while doing vendor GR :::::", e);
            StringBuilder message = new StringBuilder("Failed to create VENDOR RECEIVINGS \n");
            message.append("Unit ID :: " + grVO.getDeliveryUnitId() + "\n");
            message.append("Vendor ID :: " + grVO.getVendorId() + "\n");
            message.append("Dispatch ID :: " + grVO.getDispatchId() + "\n");
            message.append("Encountered error ::::: " + e.getMessage());
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
                    SlackNotification.SYSTEM_ERRORS, message.toString());
            throw e;
        }
    }

    @DayClosureCheck
    @RequestMapping(method = RequestMethod.POST, value = "approve-regular-vendor-gr/{userId}", produces = MediaType.APPLICATION_JSON)
    public boolean approveRegularVendorGr(HttpServletRequest request, @PathVariable(value = "userId") int userId,
                                          @RequestBody final VendorGR vendorGR)
            throws DataUpdationException, SumoException, InventoryUpdateException,DayCloseInitiatedException {
        try {
            return goodsReceiveManagementService.approveRegularVendorGR(vendorGR, userId);
        } catch (SumoException | InventoryUpdateException | DataUpdationException e) {
            LOG.error("Encountered error while doing vendor GR :::::", e);
            StringBuilder message = new StringBuilder("Failed to APPROVE VENDOR RECEIVINGS \n");
            message.append("Encountered error ::::: " + e.getMessage());
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
                    SlackNotification.SYSTEM_ERRORS, message.toString());
            throw e;
        }
    }

    @DayClosureCheck
    @RequestMapping(method = RequestMethod.POST, value = "approve-vendor-gr", produces = MediaType.APPLICATION_JSON)
    public boolean approveVendorGR(HttpServletRequest request,@RequestParam(value = "grId") final int grId, @RequestParam(value = "userId") final int userId)
            throws DayCloseInitiatedException, SumoException {
        try {
            return goodsReceiveManagementService.setVendorGRtoCreated(grId, userId);
        } catch (Exception e) {
            LOG.error("Error while updating vendor GR", e);
            throw new SumoException("Error while updating vendor GR", e);
        }
    }


    @DayClosureCheck
    @RequestMapping(method = RequestMethod.POST, value = "cancel-vendor-gr/{grId}/{userId}", produces = MediaType.APPLICATION_JSON)
    public boolean cancelVendorGR(HttpServletRequest request, @PathVariable(value = "grId") final int grId,
                                  @PathVariable(value = "userId") final int userId) throws DataUpdationException,
            DayCloseInitiatedException, SumoException {
        return goodsReceiveManagementService.cancelVendorGR(grId, userId);
    }

    @DayClosureCheck
    @RequestMapping(method = RequestMethod.POST, value = "upload-gr", consumes = MediaType.MULTIPART_FORM_DATA)
    public DocumentDetail uploadDocument(HttpServletRequest request,
                                         @RequestParam(value = "type") FileType type,
                                         @RequestParam(value = "mimeType") MimeType mimeType,
                                         @RequestParam(value = "userId") Integer userId,
                                         @RequestParam(value = "docType") DocUploadType docType,
                                         @RequestParam(value = "file") final MultipartFile file)
            throws DayCloseInitiatedException {
        String fileName = "VENDOR_GR_" + SCMUtil.getCurrentTimeISTStringWithNoColons();
        return goodsReceiveManagementService.uploadDocument(type, mimeType, docType, userId, file);
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-regular-vendors-grs", produces = MediaType.APPLICATION_JSON)
    public List<VendorGR> findRegularVendorsGrs(@RequestParam Integer unitId,
                                                @RequestParam(required = false) Integer vendorId) {
        return goodsReceiveManagementService.findRegularVendorsGrs(unitId, vendorId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-vendor-grs-rejected", produces = MediaType.APPLICATION_JSON)
    public List<VendorGR> findVendorGRsRejected(@RequestParam(required = false) Integer vendorId,
                                                @RequestParam int deliveryUnitId,
                                                @RequestParam(required = false) Integer dispatchId,
                                                @RequestParam(required = false) String startDate,
                                                @RequestParam(required = false) String endDate,
                                                @RequestParam(required = false) Integer goodReceivedId) {
        return goodsReceiveManagementService.findVendorGRsRejected(vendorId, dispatchId, deliveryUnitId,
                SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), goodReceivedId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-vendor-grs", produces = MediaType.APPLICATION_JSON)
    public List<VendorGR> findVendorGRs(@RequestParam(required = false) Integer vendorId,
                                        @RequestParam int deliveryUnitId,
                                        @RequestParam(required = false) List<Integer> skus,
                                        @RequestParam(required = false) Integer dispatchId,
                                        @RequestParam(required = false) String startDate,
                                        @RequestParam(required = false) String endDate,
                                        @RequestParam(required = false) Integer goodReceivedId) {
        return goodsReceiveManagementService.findVendorGRs(vendorId, dispatchId, deliveryUnitId, skus,
                SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), goodReceivedId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-vendor-gr-for-po", produces = MediaType.APPLICATION_JSON)
    public List<VendorGR> findVendorGRsForPo(@RequestParam(required = false) Integer vendorId,
                                             @RequestParam(required = false) Integer deliveryUnitId,
                                             @RequestParam(required = false) List<Integer> skus,
                                             @RequestParam(required = false) Integer dispatchId,
                                             @RequestParam(required = false) String startDate,
                                             @RequestParam(required = false) String endDate,
                                             @RequestParam(required = false) Integer prId,
                                             @RequestParam(required = false) Integer grId) {
        return goodsReceiveManagementService.findVendorGRsForPo(vendorId, dispatchId, deliveryUnitId, skus,
                SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), prId, grId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-vendor-grs-for-payment", produces = MediaType.APPLICATION_JSON)
    public List<VendorGR> findVendorGRsForPayment(@RequestParam Integer vendorId,
                                                  @RequestParam int deliveryUnitId,
                                                  @RequestParam(required = false) Integer dispatchId,
                                                  @RequestParam String startDate,
                                                  @RequestParam String endDate) {
        return goodsReceiveManagementService.findVendorGRsForPayment(vendorId, dispatchId, deliveryUnitId,
                SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate));
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-vendor-grs-for-no-payment", produces = MediaType.APPLICATION_JSON)
    public Boolean setVendorGRsForNoPayment(@RequestBody BulkRequestVO request) throws SumoException {
        return goodsReceiveManagementService.setVendorGRsForNoPayment(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-grs-view")
    public View getGRView(@RequestParam Integer unitId , @RequestParam String type) throws SumoException {
        List<GoodsReceived> goodsReceivedList = new ArrayList<>();
        if(type.equals("Pending Receivings")){
            goodsReceivedList = goodsReceiveManagementService.getPendingGrsWithGrItems(unitId);
        }else if(type.equals("Pending Disputes")){
            goodsReceivedList = goodsReceiveManagementService.getPendingDisputedGrs(unitId,null,true);
        }else if(type.equals("Raised Disputes")){
            goodsReceivedList = goodsReceiveManagementService.getRaisedDisputedGrs(unitId,null,true);
        }else{
            throw new SumoException("Invalid GR Type");
        }

        if(goodsReceivedList.size()>0){
            return excelViewGenerator.getGrsView(goodsReceivedList);
        }else{
            throw new SumoException("No GR Found");
        }

    }

    @RequestMapping(method = RequestMethod.GET, value = "find-asset-for-pr", produces = MediaType.APPLICATION_JSON)
    public List<AssetDefinition> findAssetsForPayment(@RequestParam(required = true) List<Integer> grIds) {
        return goodsReceiveManagementService.getAssetsForPayment(grIds);
    }
    @PostMapping("/initiate-gr-event")
    public Boolean initiateGREvent(@RequestBody  GoodsReceived goodsReceived, @RequestParam Integer userId, @RequestParam Integer unitId) throws SumoException {
        boolean res;
        synchronized(mutex.getMutex(goodsReceived.getId())){
             res = goodsReceiveManagementService.initiateGREvent(goodsReceived,unitId,userId);
        }
        return res;
    }

    @GetMapping("/get-gr-event")
    public GrStockEvent getGrEvent(@RequestParam Integer grId) throws SumoException {
        return goodsReceiveManagementService.getGrStockEvent(grId);
    }
    @GetMapping("/cancel-gr-event")
    public Boolean cancelGrEvent(@RequestParam Integer grId) throws SumoException {
        return  goodsReceiveManagementService.cancelGrEvent(grId);
    }

    @GetMapping(value = "get-url")
    public URL downloadDoc(@RequestParam(value = "vgrId") Integer vgrId,
                           @RequestParam(value = "fileType") InvoiceFileUploadType fileType)
            throws SumoException, MalformedURLException {

        if (Objects.isNull(fileType)) {
            throw new SumoException("No document type detected, Please select a file type");
        }
        if (fileType.equals(InvoiceFileUploadType.VENDOR_INVOICE)) {
            return goodsReceiveManagementService.downloadVendorInvoice(vgrId);
        }

        throw new SumoException("Not a valid request. Please check if the vendor gr id is correct");
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-vendor-invoice", consumes = MediaType.MULTIPART_FORM_DATA)
    public DocumentDetail uploadVendorInvoice(HttpServletRequest request,
                                         @RequestParam(value = "type") FileType type,
                                         @RequestParam(value = "mimeType") MimeType mimeType,
                                         @RequestParam(value = "userId") Integer userId,
                                         @RequestParam(value = "docType") DocUploadType docType,
                                         @RequestParam(value = "file") final MultipartFile file) {
        return goodsReceiveManagementService.uploadVendorInvoice(type, mimeType, docType, userId, file);
    }

    @GetMapping("is-special-order")
    public Boolean checkIfSpecialOrder(@RequestParam Integer grId) throws SumoException {
        return goodsReceiveManagementService.checkIfSpecialOrder(grId);
    }

}
