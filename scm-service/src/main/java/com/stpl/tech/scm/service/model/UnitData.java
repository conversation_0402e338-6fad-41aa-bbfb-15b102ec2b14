/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.service.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockTakeType;

public class UnitData {

    private int unitId;

    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    private Date businessDate;
    private int userId;
    private StockTakeType stockTakeType;
    private StockEventType stockEventType;

    public StockTakeType getStockTakeType() {
        return stockTakeType;
    }

    public void setStockTakeType(StockTakeType stockTakeType) {
        this.stockTakeType = stockTakeType;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }


    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public StockEventType getStockEventType() {
        return stockEventType;
    }

    public void setStockEventType(StockEventType stockEventType) {
        this.stockEventType = stockEventType;
    }
}
