package com.stpl.tech.scm.service.controller;

import java.math.BigDecimal;
import java.util.List;

import javax.ws.rs.core.MediaType;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.scm.data.model.AdditionalDocumentsMaster;
import com.stpl.tech.scm.domain.model.IdCodeName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.ServiceMappingManagementService;
import com.stpl.tech.scm.domain.model.CostElement;
import com.stpl.tech.scm.domain.model.CostElementPriceUpdate;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.service.model.MappingStatusChange;
import com.stpl.tech.scm.service.model.MappingUpdate;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT)
public class ServiceMappingManagementResources extends AbstractSCMResources {

	private static final Logger LOG = LoggerFactory.getLogger(ServiceMappingManagementResources.class);
	
	@Autowired
	ServiceMappingManagementService serviceMappingManagementService;
	
	@RequestMapping(method = RequestMethod.GET, value = "cost-element-data" , produces = MediaType.APPLICATION_JSON)
	public List<CostElement> getCostELement() throws SumoException{
		return serviceMappingManagementService.getCostElementData();
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "get-vendor-list", produces = MediaType.APPLICATION_JSON)
	public List<IdCodeNameStatus> getAllVendors(){
		return serviceMappingManagementService.allVendors();
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "costelement-to-vendor", produces = MediaType.APPLICATION_JSON)
    public List<IdCodeNameStatus> searchCostELementVendorMapping(@RequestParam final Integer costElementId) {
		return serviceMappingManagementService.searchCostELementVendorMappings(costElementId);
    }
	
	@RequestMapping(method = RequestMethod.POST, value = "add-costelement-to-vendor", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean addCostELementVendorMapping(@RequestBody final MappingUpdate mapping) {
	     return serviceMappingManagementService.addCostELementVendorMapping(mapping.getEmployeeId(), mapping.getEmployeeName(),
	                mapping.getId(), mapping.getMappingIds());
	}
	
	@RequestMapping(method = RequestMethod.POST, value = "update-costelement-to-vendor", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean updateCostElementVendorMapping(@RequestBody final MappingStatusChange mapping) {
        return serviceMappingManagementService.updateCostElementVendorMapping(mapping.getEmployeeId(), mapping.getEmployeeName(),
                mapping.getVendorId(), mapping.getCostElementId(), mapping.getStatus());
    }
	
	@RequestMapping(method = RequestMethod.GET, value = "vendor-to-costElement", produces = MediaType.APPLICATION_JSON)
    public List<IdCodeNameStatus> searchVendorToCostElementMapping(@RequestParam final Integer vendorId) {
		return serviceMappingManagementService.searchVendorToCostElementMappings(vendorId);
    }
	
	@RequestMapping(method = RequestMethod.POST, value = "add-vendor-to-costelement", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean addVendorCostElementMapping(@RequestBody final MappingUpdate mapping) {
	     return serviceMappingManagementService.addVendorCostElementMapping(mapping.getEmployeeId(), mapping.getEmployeeName(),
	                mapping.getId(), mapping.getMappingIds());
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "get-costcentre-list", produces = MediaType.APPLICATION_JSON)
	public List<IdCodeNameStatus> getAllCostCentre(){
		return serviceMappingManagementService.allCostCentre();
	}
	
	@RequestMapping(method = RequestMethod.POST, value = "add-costelement-to-costcenter", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean addCostElementCostCenterMapping(@RequestBody final MappingUpdate mapping) {
	     return serviceMappingManagementService.addCostElementCostCenterMapping(mapping.getEmployeeId(), mapping.getEmployeeName(),
	                mapping.getId(), mapping.getMappingIds());
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "costelement-to-costcenter", produces = MediaType.APPLICATION_JSON)
    public List<IdCodeNameStatus> searchCostELementCostCenterMapping(@RequestParam final Integer costElementId) {
		return serviceMappingManagementService.searchCostELementCostCenterMappings(costElementId);
    }
	
	@RequestMapping(method = RequestMethod.POST, value = "update-costelement-to-costcenter", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean updateCostElementCostCenterMapping(@RequestBody final MappingStatusChange mapping) {
        return serviceMappingManagementService.updateCostElementCostCenterMapping(mapping.getEmployeeId(), mapping.getEmployeeName(),
                mapping.getCostElementId(), mapping.getCostCenterId(), mapping.getStatus());
    }
	
	@RequestMapping(method = RequestMethod.POST, value = "add-costcentre-to-costelement", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean addCostCenterToCostElementMapping(@RequestBody final MappingUpdate mapping) {
	     return serviceMappingManagementService.addCostCenterToCostElementMapping(mapping.getEmployeeId(), mapping.getEmployeeName(),
	                mapping.getId(), mapping.getMappingIds());
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "costcenter-to-costelement", produces = MediaType.APPLICATION_JSON)
    public List<IdCodeNameStatus> searchCostCenterToCostElementMapping(@RequestParam final Integer costCenterId) {
		return serviceMappingManagementService.searchCostCenterToCostElementMappings(costCenterId);
    }
	
	@RequestMapping(method = RequestMethod.GET, value = "get-priced-costelements", produces = MediaType.APPLICATION_JSON)
	public List<CostElementPriceUpdate> getPricedCostElement(@RequestParam final Integer vendorId,@RequestParam final Integer costCenterId){
		return serviceMappingManagementService.getPricedCostElements(vendorId, costCenterId);
	}
	
	@RequestMapping(method = RequestMethod.POST, value = "update-status-costelement-price", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean updateStatusCostElementPriceMapping(@RequestBody final MappingStatusChange mapping) {
        return serviceMappingManagementService.updateStatusCostElementPriceMapping(mapping.getEmployeeId(), mapping.getEmployeeName(),
                mapping.getCostElementId(), mapping.getStatus());
    }
	
	@RequestMapping(method = RequestMethod.GET, value = "add-costelement-mapping-price", produces = MediaType.APPLICATION_JSON)
	public boolean addCostElementPriceMapping(@RequestParam final Integer vendorId,@RequestParam final Integer costCenterId,@RequestParam final Integer costElementId,@RequestParam final BigDecimal price,@RequestParam final Integer employeeId,@RequestParam final String employeeName){
		return serviceMappingManagementService.addCostElementPriceMapping(vendorId, costCenterId,costElementId,price,employeeId,employeeName);
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "update-price-costelement-mapping", produces = MediaType.APPLICATION_JSON)
	public boolean updateCostElementPriceMapping(@RequestParam final Integer costElementMappingId,@RequestParam final BigDecimal price,@RequestParam final Integer employeeId,@RequestParam final String employeeName){
		return serviceMappingManagementService.updateCostElementPriceMapping(costElementMappingId,price,employeeId,employeeName);
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "vendor-price-clone", produces = MediaType.APPLICATION_JSON)
	public List<CostElementPriceUpdate> updateCostElementPriceMapping(@RequestParam final String costElementIds,@RequestParam final Integer vendorId, @RequestParam final Integer costCenterId){
		return serviceMappingManagementService.getClonePriceForCostElementId(costElementIds,vendorId,costCenterId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "costelement-to-document", produces = MediaType.APPLICATION_JSON)
	public List<IdCodeName> searchCostElementDocumentMapping(@RequestParam final Integer costElementId) {
		return serviceMappingManagementService.searchCostElementDocumentMappings(costElementId);
	}
	@RequestMapping(method = RequestMethod.POST, value = "add-master-document", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean addMasterDocument(@RequestParam final String documentName, @RequestParam final Integer createdBy) throws DataUpdationException {
		return serviceMappingManagementService.addMasterDocument(documentName,createdBy);
	}

	@RequestMapping(method = RequestMethod.POST, value = "add-costelement-to-document", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean addCostElementDocumentMapping(@RequestBody final MappingUpdate mapping) {
		return serviceMappingManagementService.addCostElementDocumentMapping(mapping.getEmployeeId(), mapping.getEmployeeName(),
				mapping.getId(), mapping.getMappingIds());
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-document-list",produces = MediaType.APPLICATION_JSON)
	public List<AdditionalDocumentsMaster> getDocumentList(){
		return serviceMappingManagementService.getDocumentList();
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-costelement-to-document", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public boolean updateCostElementDocumentMapping(@RequestBody final MappingStatusChange mapping) {
		return serviceMappingManagementService.updateCostElementDocumentMapping(mapping.getDocumentId(), mapping.getCostElementId());
	}

	@RequestMapping(method = RequestMethod.GET,value = "get-additional-docs",produces = MediaType.APPLICATION_JSON)
	public List<AdditionalDocumentsMaster> getAdditionalDocs(){
		return serviceMappingManagementService.getAdditionalDocs();
	}
}

