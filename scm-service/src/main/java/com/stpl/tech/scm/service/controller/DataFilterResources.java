package com.stpl.tech.scm.service.controller;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.ws.rs.core.MediaType;

import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.UnitSubCategory;
import com.stpl.tech.scm.core.service.SCMFilterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.itextpdf.tool.xml.exceptions.NotImplementedException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.DerivedMapping;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.DATA_FILTER_ROOT_CONTEXT)
public class DataFilterResources {

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private SCMFilterService scmFilterService;

	@RequestMapping(method = RequestMethod.GET, value = "units", produces = MediaType.APPLICATION_JSON)
	public Set<Integer> filerAvailableUnits(@RequestParam final int unitId) {

		Set<Integer> l = new HashSet<>();
		UnitDetail u = scmCache.getUnitDetail(unitId);
		UnitBasicDetail ubd = masterCache.getUnitBasicDetail(unitId);
		Integer company = scmFilterService.getCompanyIdWithFilterChecks();
		if (SCMUtil.isCafe(u)) {
			// same company and active check
			// update - warehouse and kitchen of same company are available
			for (UnitDetail ud : scmCache.getUnitDetails().values()) {
				UnitBasicDetail bd = masterCache.getUnitBasicDetail(ud.getUnitId());
				if (u.getCompanyId().equals(ud.getCompanyId()) && (SCMUtil.isCafe(ud)
						|| SCMUtil.isMaintenanceStore(bd.getSubCategory())) && ud.getUnitId() != unitId
						&& SwitchStatus.ACTIVE.equals(ud.getUnitStatus()) && (company == null || company.equals(ud.getCompanyId()))) {
					l.add(ud.getUnitId());
				}
			}
			// fulfillment type unit
			for (FulfillmentType f : FulfillmentType.values()) {
				Integer i = scmCache.getFulfillmentUnit(f, unitId);
				if (i != null && !i.equals(unitId)) {
					l.add(i);
				}
			}
		}

		else if (SCMUtil.isWareHouseOrKitchen(u) && SCMUtil.isInventoryWarehouseOrKitchen(ubd.getSubCategory())) {
			// STPL warehouse can only send to DKC WH/KCN
			// * Update : STPL WH/KHN will have access to all
			l.addAll(scmCache.getUnitDetails().values().stream()
					.filter(p -> p.getUnitId() != unitId && SwitchStatus.ACTIVE.equals(p.getUnitStatus()) && (company == null || company.equals(p.getCompanyId())))
					.map(UnitDetail::getUnitId).collect(Collectors.toList()));
		}

		else if (SCMUtil.isWareHouseOrKitchen(u) && SCMUtil.isMaintenanceStore(ubd.getSubCategory())) {
			// fulfillment type unit
			for (UnitDetail ud : scmCache.getUnitDetails().values()) {
				UnitBasicDetail sbd = masterCache.getUnitBasicDetail(unitId);
				if (((SCMUtil.isCafe(ud) && ud.getUnitRegion().equalsIgnoreCase(u.getUnitRegion())) || SCMUtil.isMaintenanceStore(sbd.getSubCategory()))
						&& ud.getUnitId() != unitId && SwitchStatus.ACTIVE.equals(ud.getUnitStatus()) && (company == null || company.equals(ud.getCompanyId()))) {
					l.add(ud.getUnitId());
				}
			}
			for (FulfillmentType f : FulfillmentType.values()) {
				Integer i = scmCache.getFulfillmentUnit(f, unitId);
				if (i != null && !i.equals(unitId)) {
					l.add(i);
				}
			}
		}

		else if (SCMUtil.isWareHouseOrKitchen(u) && SCMUtil.isDistributionStore(ubd.getSubCategory())) {
			// fulfillment type unit
			for (UnitDetail ud : scmCache.getUnitDetails().values()) {
				UnitBasicDetail bd = masterCache.getUnitBasicDetail(ud.getUnitId());
				if (SCMUtil.isDistributionStore(bd.getSubCategory()) && ud.getUnitId() != unitId
						&& SwitchStatus.ACTIVE.equals(ud.getUnitStatus())) {
					l.add(ud.getUnitId());
				}
			}
			for (FulfillmentType f : FulfillmentType.values()) {
				Integer i = scmCache.getFulfillmentUnit(f, unitId);
				if (i != null && !i.equals(unitId)) {
					l.add(i);
				}
			}
		}

		else if (SCMUtil.isWareHouseOrKitchen(u) && SCMUtil.isInternal(ubd.getSubCategory())) {
			for (FulfillmentType f : FulfillmentType.values()) {
				Integer i = scmCache.getFulfillmentUnit(f, unitId);
				if (i != null && !i.equals(unitId)) {
					l.add(i);
				}
			}
		}

		/*
		 * else if (SCMUtil.isWareHouseOrKitchen(u) && SCMUtil.isDkcUnit(u)) { // all
		 * active units l.addAll(scmCache.getUnitDetails().values().stream() .filter(p
		 * -> p.getUnitId() != unitId && SwitchStatus.ACTIVE.equals(p.getUnitStatus()))
		 * .map(UnitDetail::getUnitId).collect(Collectors.toList())); }
		 */

		return l;
	}

	@RequestMapping(method = RequestMethod.GET, value = "products", produces = MediaType.APPLICATION_JSON)
	public Set<Integer> filerAvailableProducts(@RequestParam final int requestingUnit,
			@RequestParam final int fulfillmentUnit) {
		Set<Integer> l = new HashSet<Integer>();
		UnitDetail rUnit = scmCache.getUnitDetail(requestingUnit);
		UnitDetail fUnit = scmCache.getUnitDetail(fulfillmentUnit);

		Unit rUnitKettle = masterCache.getUnit(requestingUnit);
		Unit fUnitKettle = masterCache.getUnit(fulfillmentUnit);

		/**
		 * Requesting unit Cafe, Fulfillment Unit Cafe
		 * <p>
		 * check for -
		 * <li>Available at cafe and auto production is false
		 * <li>Default fulfillment type for product should not be null
		 */
		if (SCMUtil.isCafe(rUnit) && SCMUtil.isCafe(fUnit)) {
			l.addAll(
					scmCache.getProductDefinitions().values().stream().filter(p -> filterForCafeToCafe(p, fUnit, rUnit))
							.map(ProductDefinition::getProductId).collect(Collectors.toList()));
		}

		/**
		 * Requesting unit Cafe, Fulfillment Unit Warehouse/Kitchen
		 * <p>
		 * check for -
		 * <li>Available at cafe and auto production is false
		 * <li>Default fulfillment type for product should be equal to fulfillment unit
		 * OR external
		 * <li>Check unit to SKU mapping for product availability at the fulfillment
		 * unit
		 */
		else if (SCMUtil.isCafe(rUnit) && SCMUtil.isWareHouseOrKitchen(fUnit)) {
			/*			for(Integer key : scmCache.getProductDefinitions().keySet()) {
				ProductDefinition d = scmCache.getProductDefinitions().get(key);
				//Log.info(String.format("#####Checking for Product %s[%d]",d.getProductName(), d.getProductId()));
				if(filterForKitchenOrWarehouseToCafe(d, fUnit, rUnit)) {
					//Log.info(String.format("#####Including Product %s[%d]",d.getProductName(), d.getProductId()));
					l.add(key);
				}else {
					Log.info(String.format("#####Excluding Product %s[%d]",d.getProductName(), d.getProductId()));

				}
			}
*/			l.addAll(scmCache.getProductDefinitions().values().stream()
					.filter(p -> filterForKitchenOrWarehouseToCafe(p, fUnit, rUnit, fUnitKettle, rUnitKettle))
					.map(ProductDefinition::getProductId).collect(Collectors.toSet()));
			// unit to SKU mapping
			l.retainAll(scmCache.getAvailableProductForUnit(fulfillmentUnit));
		}

		/**
		 * Requesting unit Warehouse/Kitchen, Fulfillment Unit cafe
		 * <p>
		 * check for -
		 * <li>Available at cafe and auto production is false
		 * <li>Default fulfillment type for product should not be null
		 */
		else if (SCMUtil.isWareHouseOrKitchen(rUnit) && SCMUtil.isCafe(fUnit)) {
			/*
			 * if (SCMUtil.isCafe(fUnit)) {
			 */

			Boolean isMaintenanceFaWh = masterCache.getUnit(rUnit.getUnitId()).getSubCategory().
					equals(UnitSubCategory.FA_WH_MAINTENANCE);
			Boolean isMaintenanceWh =  masterCache.getUnit(rUnit.getUnitId()).getSubCategory().
					equals(UnitSubCategory.MAINTENANCE);

			l.addAll(scmCache.getProductDefinitions().values().stream()
					.filter(p -> filterForCafeToKitchenOrWarehouse(p, fUnit, rUnit) && filterForCafeToMaintenanceWh(p, isMaintenanceFaWh, isMaintenanceWh))
					.map(ProductDefinition::getProductId).collect(Collectors.toSet()));
			// unit to SKU mapping
			l.retainAll(scmCache.getAvailableProductForUnit(requestingUnit));

		}

		/**
		 * Requesting unit warehouse/kitchen, Fulfillment Unit Warehouse/Kitchen
		 * <p>
		 * Check for -
		 * <li>auto production is false
		 * <li>Check unit to SKU mapping for product availability at the fulfillment
		 * unit
		 */
		else if (SCMUtil.isWareHouseOrKitchen(rUnit) && SCMUtil.isWareHouseOrKitchen(fUnit)) {
			Boolean isNpdUnit = SCMUtil.isNpdUnit(fUnit);
			l.addAll(scmCache.getProductDefinitions().values().stream().filter(p -> (!p.isAutoProduction() || isNpdUnit))
					.map(ProductDefinition::getProductId).collect(Collectors.toList()));
			l.retainAll(scmCache.getAvailableProductForUnit(requestingUnit));
			l.retainAll(scmCache.getAvailableProductForUnit(fulfillmentUnit));
		}

		return l;
	}

	/**
	 * check for -
	 * <li>Available at cafe and auto production is false
	 * <li>Default fulfillment type for product should be equal to fulfillment unit
	 * OR external
	 *
	 * @param p
	 * @param fUnit
	 * @return
	 */

	private Boolean filterForCafeToMaintenanceWh(ProductDefinition p, Boolean isMaintenanceFaWh, Boolean isMaintenanceWh) {
		if ((p.getCategoryDefinition().getId().equals(SCMServiceConstants.CATEGORY_FIXED_ASSETS))) {
			return true;
		}

		Boolean isKitchenEquipment = p.getSubCategoryDefinition().getId().equals(SCMServiceConstants.SUB_CATEGORY_FA_KITCHEN);
		if (isMaintenanceWh.equals(Boolean.TRUE)) {
			return !isKitchenEquipment;
		}
		if (isMaintenanceFaWh.equals(Boolean.TRUE)) {
			return isKitchenEquipment;
		}

		return true;

	}


	private boolean filterForKitchenOrWarehouseToCafe(ProductDefinition p, UnitDetail fUnit, UnitDetail rUnit,
			Unit fUnitKettle, Unit rUnitKettle) {

		if (p.isAvailableAtCafe() && !p.isAutoProduction()) {
			if (SCMUtil.isDerivedFulfillmentType(p)) {
				if (p.getDerivedMappings() != null && !p.getDerivedMappings().isEmpty()) {
					for (DerivedMapping m : p.getDerivedMappings()) {
						if (m.getUnit() == rUnit.getUnitId()) {
							if (m.getType() == null) {
								// Log.info(String.format("#####Excluding Product %s[%d] - Step Derived Mapping
								// is null",p.getProductName(), p.getProductId()));
								return false;
							} else if (SCMUtil.isExternalFulfillmentType(m.getType())) {
								// Log.info(String.format("#####Including Product %s[%d] - Step Derived Mapping
								// is #NOT# null and fullfillment type is external",p.getProductName(),
								// p.getProductId()));
								return true;
							} else {
								Integer i = scmCache.getFulfillmentUnit(m.getType(), rUnit.getUnitId());
								if (i != null && fUnit.getUnitId() == i.intValue()) {
									// Log.info(String.format("#####Including Product %s[%d] - Step Derived Mapping
									// is #NOT# null and fullfillment type is K/W and fullfilled by the
									// unit",p.getProductName(), p.getProductId()));
									return true;
								}
							}
						}
					}
				} else {
					Integer i = scmCache.getFulfillmentUnit(p.getDefaultFulfillmentType(), rUnit.getUnitId());
					if (i != null && fUnit.getUnitId() == i.intValue()) {
						// Log.info(String.format("#####Including Product %s[%d] - Step Derived Mapping
						// is null and fullfillment type is K/W and fullfilled by the
						// unit",p.getProductName(), p.getProductId()));
						return true;
					}
				}
			} else if (SCMUtil.isKitchenOrWareHouseFullfillmentType(p.getFulfillmentType())
					&& SCMUtil.isInventoryWarehouseOrKitchen(fUnitKettle.getSubCategory())) {
				Integer i = scmCache.getFulfillmentUnit(p.getFulfillmentType(), rUnit.getUnitId());
				if (i != null && fUnit.getUnitId() == i.intValue()) {
					// Log.info(String.format("#####Including Product %s[%d] - Step No derived
					// Mapping fullfillment type is K/W and fullfilled by the
					// unit",p.getProductName(), p.getProductId()));
					return true;
				}
			} else if (SCMUtil.isKitchenOrWareHouseFullfillmentType(p.getFulfillmentType())
					&& !SCMUtil.isInventoryWarehouseOrKitchen(fUnitKettle.getSubCategory())) {
				return true;
			} else {
				// Log.info(String.format("#####Including Product %s[%d] - Step Catch
				// All",p.getProductName(), p.getProductId()));
				return true;
			}
		}
		// Log.info(String.format("#####Including Product %s[%d] - Step Not Available At
		// Cafe OR is Auto Production",p.getProductName(), p.getProductId()));
		return false;
	}
																					//cafe				//wh
	private boolean filterForCafeToKitchenOrWarehouse(ProductDefinition p, UnitDetail fUnit, UnitDetail rUnit) {

		if (p.isAvailableAtCafe() && !p.isAutoProduction()) {
			if (SCMUtil.isDerivedFulfillmentType(p)) {
				if (p.getDerivedMappings() != null && !p.getDerivedMappings().isEmpty()) {
					for (DerivedMapping m : p.getDerivedMappings()) {
						if (m.getUnit() == fUnit.getUnitId()) {
							if (m.getType() == null) {
								//Log.info(String.format("#####Excluding Product %s[%d] - Step Derived Mapping is null",p.getProductName(), p.getProductId()));
								return false;
							} else if (SCMUtil.isExternalFulfillmentType(m.getType())) {
								//Log.info(String.format("#####Including Product %s[%d] - Step Derived Mapping is #NOT# null and fullfillment type is external",p.getProductName(), p.getProductId()));
								return true;
							} else {
								Integer i = scmCache.getFulfillmentUnit(m.getType(), fUnit.getUnitId());
								if (i != null && rUnit.getUnitId() == i.intValue()) {
									//Log.info(String.format("#####Including Product %s[%d] - Step Derived Mapping is #NOT# null and fullfillment type is K/W and fullfilled by the unit",p.getProductName(), p.getProductId()));
									return true;
								}
							}
						}
					}
				} else {
					return true;
				}
			} else {
				//Log.info(String.format("#####Including Product %s[%d] - Step Catch All",p.getProductName(), p.getProductId()));
				return true;
			}
		}
		//Log.info(String.format("#####Including Product %s[%d] - Step Not Available At Cafe OR is Auto Production",p.getProductName(), p.getProductId()));
		return false;
	}


	private boolean filterForCafeToCafe(ProductDefinition p, UnitDetail fUnit, UnitDetail rUnit) {
		boolean fUnitDerived = false;
		boolean rUnitDerived = false;
		if (p.isAvailableAtCafe() && !p.isAutoProduction()) {
			if (SCMUtil.isDerivedFulfillmentType(p)) {
				if (p.getDerivedMappings() != null && !p.getDerivedMappings().isEmpty()) {
					for (DerivedMapping m : p.getDerivedMappings()) {
						if (m.getUnit() == fUnit.getUnitId()) {
							if (m.getType() != null) {
								fUnitDerived = true;
							}
						}
						if (m.getUnit() == rUnit.getUnitId()) {
							if (m.getType() != null) {
								rUnitDerived = true;
							}
						}
						if (fUnitDerived && rUnitDerived) {
							return true;
						}
					}
				} else {
					return false;
				}
			}
			else {
				return true;
			}
		}
		// Log.info(String.format("#####Including Product %s[%d] - Step Not Available At
		// Cafe OR is Auto Production",p.getProductName(), p.getProductId()));
		return false;
	}

	@RequestMapping(method = RequestMethod.GET, value = "skus", produces = MediaType.APPLICATION_JSON)
	public List<Integer> filerAvailableSKUforUnit(@RequestParam final int unitId) {
		UnitDetail u = scmCache.getUnitDetail(unitId);
		Set<Integer> s = null;
		if (SCMUtil.isWareHouseOrKitchen(u)) {
			s = scmCache.getAvailableSKUForUnit(unitId);
		} else {
			s = scmCache.getSkuDefinitions().values().stream()
					.filter(sku -> SCMUtil.isActive(sku.getSkuStatus().name())).map(SkuDefinition::getSkuId)
					.collect(Collectors.toSet());
		}
		if (s == null) {
			return new ArrayList<Integer>();
		}
		return new ArrayList<Integer>(s);
	}

	@RequestMapping(method = RequestMethod.GET, value = "unitSkuProducts", produces = MediaType.APPLICATION_JSON)
	public List<Integer> filerAvailableProductsforUnit(@RequestParam final int unitId) {
		UnitDetail u = scmCache.getUnitDetail(unitId);
		Set<Integer> s = null;
		if (SCMUtil.isWareHouseOrKitchen(u)) {
			s = scmCache.getAvailableProductForUnit(unitId);
		} else {
			throw new NotImplementedException("wh/products API call is not available for Non WH or Kitchen Units");
		}
		if (s == null) {
			return new ArrayList<Integer>();
		}
		return new ArrayList<Integer>(s);
	}

}
