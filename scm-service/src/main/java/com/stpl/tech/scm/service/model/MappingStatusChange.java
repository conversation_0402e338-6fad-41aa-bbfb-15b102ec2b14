/**
 * 
 */
package com.stpl.tech.scm.service.model;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class MappingStatusChange implements Serializable {

	private static final long serialVersionUID = -4303065780966096505L;
	private int vendorId;
	private int costElementId;
	private int costCenterId;
	private int businessId;
	private int skuId;
	private String status;
	private int employeeId;
	private String employeeName;

	private int documentId;

	public int getDocumentId() {
		return documentId;
	}

	public void setDocumentId(int documentId) {
		this.documentId = documentId;
	}

	public int getVendorId() {
		return vendorId;
	}

	public void setVendorId(int id) {
		this.vendorId = id;
	}

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public int getSkuId() {
		return skuId;
	}

	public void setSkuId(int mappingId) {
		this.skuId = mappingId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public int getCostElementId() {
		return costElementId;
	}

	public void setCostElementId(int costElementId) {
		this.costElementId = costElementId;
	}

	public int getCostCenterId() {
		return costCenterId;
	}

	public void setCostCenterId(int costCenterId) {
		this.costCenterId = costCenterId;
	}

	public int getBusinessId() {
		return businessId;
	}

	public void setBusinessId(int businessId) {
		this.businessId = businessId;
	}
}
