package com.stpl.tech.scm.service.controller;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.service.KettleStockOutService;
import com.stpl.tech.scm.data.enums.SchedulerType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * REST Controller for Kettle Stock Out management operations
 * Provides endpoints for stock out percentage calculations and data processing
 * Also maintains scheduled jobs for automated processing
 */
@Slf4j
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.KETTLE_STOCK_OUT_ROOT_CONTEXT)
public class KettleStockOutController extends AbstractSCMResources {

    @Autowired
    private KettleStockOutService kettleStockOutService;

    /**
     * Scheduler that runs every day at 9:00 AM IST
     * Processes morning stock out data and calculates stock out percentages
     */
    @Scheduled(cron = "0 0 9,10 * * *", zone = "GMT+05:30")
    public void createStockOutPercentageDataScheduler() {
        boolean exists = kettleStockOutService.checkWhetherPercentageDataAlreadyCreated(SchedulerType.STOCK_OUT_PERCENTAGE);
        if (exists) {
            log.info("Stock Out Percentage Data Already Created");
            return;
        }
        kettleStockOutService.createStockOutPercentageData(null);
    }

    /**
     * Scheduler that runs every day at 11:00 AM IST
     */
    @Scheduled(cron = "0 0 11 * * *", zone = "GMT+05:30")
    public void processStockOutDataAndCreateDumpScheduler() {
        kettleStockOutService.processStockOutDataAndCreateDump(null);
    }

    /**
     * REST endpoint to manually trigger stock out percentage data creation
     * @return ResponseEntity with operation status
     */
    @PostMapping("/create-percentage-data")
    public ResponseEntity<String> createStockOutPercentageData(@RequestParam(required = false) Integer unitId) {
        try {
            boolean exists = kettleStockOutService.checkWhetherPercentageDataAlreadyCreated(SchedulerType.STOCK_OUT_PERCENTAGE);
            if (exists) {
                    return ResponseEntity.ok("Stock Out Percentage Data Already Created");
            }
            
            kettleStockOutService.createStockOutPercentageData(unitId);
            return ResponseEntity.ok("Stock Out Percentage Data Created Successfully");
        } catch (Exception e) {
            log.error("Error in createStockOutPercentageData endpoint :: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating stock out percentage data: " + e.getMessage());
        }
    }

    /**
     * REST endpoint to manually trigger stock out data dump creation
     * @return ResponseEntity with operation status
     */
    @PostMapping("/create-dump")
    public ResponseEntity<String> processStockOutDataAndCreateDump(@RequestParam(required = false) Integer unitId) {
        try {
            kettleStockOutService.processStockOutDataAndCreateDump(unitId);
            return ResponseEntity.ok("Stock Out Data Dump Created Successfully");
        } catch (Exception e) {
            log.error("Error in processStockOutDataAndCreateDump endpoint :: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating stock out data dump: " + e.getMessage());
        }
    }

    /**
     * REST endpoint to check if percentage data already exists for current business date
     * @return ResponseEntity with check result
     */
    @GetMapping("/percentage-data-status")
    public ResponseEntity<Boolean> checkPercentageDataStatus() {
        try {
            boolean exists = kettleStockOutService.checkWhetherPercentageDataAlreadyCreated(SchedulerType.STOCK_OUT_PERCENTAGE);
            return ResponseEntity.ok(exists);
        } catch (Exception e) {
            log.error("Error in checkPercentageDataStatus endpoint :: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(false);
        }
    }

    /**
     * REST endpoint to create stock out percentage data for a specific date range
     * @param startDate Start date for processing (optional)
     * @param endDate End date for processing (optional)  
     * @param unitId Unit ID to process (optional)
     * @return ResponseEntity with operation status
     */
    @PostMapping("/create-percentage-data-by-business-date")
    public ResponseEntity<String> createStockOutPercentageDataByBusinessDate(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) Integer unitId) {
        try {
            // Set default dates if not provided
            if (startDate == null) {
                startDate = com.stpl.tech.util.AppUtils.getBusinessDate(); // Current business date
            }
            if (endDate == null) {
                endDate = startDate; // Same as start date if not provided
            }

            log.info("Starting stock out percentage data creation for date range: {} to {}, unitId: {}", startDate, endDate, unitId);

            // Convert dates to LocalDate for easier iteration
            java.time.LocalDate start = startDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            java.time.LocalDate end = endDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

            // Loop through each business date
            java.time.LocalDate currentDate = start;
            while (!currentDate.isAfter(end)) {
                Date businessDate = Date.from(currentDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
                
                log.info("Processing business date: {} for unitId: {}", businessDate, unitId);
                
                // Call service method for each date
                kettleStockOutService.createStockOutPercentageDataByBusinessDate(businessDate, unitId);
                
                currentDate = currentDate.plusDays(1);
            }

            log.info("Completed stock out percentage data creation for date range: {} to {}", startDate, endDate);
            return ResponseEntity.ok("Stock Out Percentage Data Created Successfully for date range");
        } catch (Exception e) {
            log.error("Error in createStockOutPercentageDataByBusinessDate endpoint :: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating stock out percentage data by business date: " + e.getMessage());
        }
    }
}