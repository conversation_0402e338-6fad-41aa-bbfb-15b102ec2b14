package com.stpl.tech.scm.service.model;

import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.domain.model.RequestScmItem;

public class UnitProductReferenceData {

	private int unitId;
	private Date fulfillmentDate;
	private int noOfDays;
	private List<RequestScmItem> scmProductList;

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public Date getFulfillmentDate() {
		return fulfillmentDate;
	}

	public void setFulfillmentDate(Date fulfillmentDate) {
		this.fulfillmentDate = fulfillmentDate;
	}

	public int getNoOfDays() {
		return noOfDays;
	}

	public void setNoOfDays(int noOfDays) {
		this.noOfDays = noOfDays;
	}

	public List<RequestScmItem> getScmProductList() {
		return scmProductList;
	}

	public void setScmProductList(List<RequestScmItem> scmProductList) {
		this.scmProductList = scmProductList;
	}

	@Override
	public String toString() {
		return "UnitProductReferenceData [unitId=" + unitId + ", fulfillmentDate=" + fulfillmentDate + ", noOfDays="
				+ noOfDays + ", scmProductList=" + scmProductList + "]";
	}
	
	

}
