package com.stpl.tech.scm.service.controller;

import java.util.List;
import java.util.Map;

import javax.ws.rs.core.MediaType;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.AttributeValueData;
import com.stpl.tech.scm.domain.model.ValidateStateOutput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.service.AttributeManagementService;
import com.stpl.tech.scm.domain.model.AttributeDefinition;
import com.stpl.tech.scm.domain.model.AttributeType;
import com.stpl.tech.scm.domain.model.AttributeValue;

/**
 * Created by Rahul Singh on 05-05-2016.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.ATTRIBUTE_MANAGEMENT_ROOT_CONTEXT)
public class AttributeManagementResources {

    @Autowired
    private AttributeManagementService attributeManagementService;

    // Attribute Definition Resources

    @RequestMapping(method = RequestMethod.GET, value = "attribute-definitions", produces = MediaType.APPLICATION_JSON)
    public Map<AttributeType, List<AttributeDefinition>> viewAllAttributeDefinitions() {
        return attributeManagementService.viewAllAttributeDefinitions();
    }


    // Attribute Value Resources
    @RequestMapping(method = RequestMethod.GET, value = "attribute-values", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, List<AttributeValue>> viewAllAttributeValues() {
        return attributeManagementService.viewAllAttributeValues();
    }

    @RequestMapping(method = RequestMethod.POST, value = "attribute-value", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public List<AttributeValueData> addAttributeValue(@RequestBody final List<AttributeValue> attributeValues) throws SumoException {
        return attributeManagementService.addNewAttributeValue(attributeValues);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add/attribute-definition", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public AttributeDefinition addAttributeDefinition(@RequestBody final AttributeDefinition attributeDefinition) throws SumoException {
        return attributeManagementService.addAttribute(attributeDefinition);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "update/attribute-definition", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public AttributeDefinition updateAttributeDefinition(@RequestBody final AttributeDefinition attributeDefinition) throws SumoException {
        return attributeManagementService.updateAttribute(attributeDefinition);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "attribute-value", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean updateAttributeValue(@RequestBody final AttributeValue attributeValue) {
        return attributeManagementService.updateAttributeValue(attributeValue);
    }

    @RequestMapping(method = RequestMethod.GET, value = "attribute-values-by-attribute", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, List<AttributeValue>> getAllAttributeValuesByAttribute(@RequestParam int categoryId, @RequestParam boolean onlyActive) {
        return attributeManagementService.getAllAttributeValuesByAttribute(categoryId, onlyActive);
    }

    @GetMapping(value = "/validate-state")
    public List<ValidateStateOutput> validateState(@RequestParam Long stateId, @RequestParam Integer unitId) {
        return attributeManagementService.validateState(stateId,unitId);
    }


}
