package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.service.MonkDayCloseService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.MonkDayCloseEvent;
import com.stpl.tech.scm.domain.model.MonkStatusDTO;
import com.stpl.tech.scm.domain.model.MonkStatusDayClose;
import com.stpl.tech.scm.domain.model.MonkStatusDayCloseHistory;
import com.stpl.tech.util.domain.RequestContext;
import com.stpl.tech.util.notification.model.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR + SCMServiceConstants.MONK_DAY_CLOSE_MANAGEMENT_ROOT_CONTEXT)
public class MonkDayCloseManagementResource {

    @Autowired
    private MonkDayCloseService monkDayCloseService;

    @Autowired
    private MasterDataCache masterDataCache;

    @PostMapping(value = "initialize", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public ResponseData<MonkDayCloseEvent> initializeMonkDayCloseEvent(@RequestParam Integer unitId,
                                                                       @RequestParam(required = false) String businessDate) {
        ResponseData<MonkDayCloseEvent> response = new ResponseData<>();
        try {
            Date date = businessDate != null ? SCMUtil.parseDate(businessDate) : SCMUtil.getCurrentBusinessDate();
            MonkDayCloseEvent event = monkDayCloseService.initializeMonkDayCloseEvent(unitId, date, null);
            response.setPayload(event);
            response.setSuccess(true);
            response.setMessage("Monk day close event initialized successfully");
        } catch (Exception e) {
            log.error("Error initializing monk day close event for unit: {}", unitId, e);
            response.setSuccess(false);
            response.setMessage("Error initializing monk day close event: " + e.getMessage());
        }
        return response;
    }

    @GetMapping(value = "event", produces = MediaType.APPLICATION_JSON)
    public ResponseData<MonkDayCloseEvent> getMonkDayCloseEvent(@RequestParam Integer unitId,
                                                                @RequestParam(required = false) String businessDate) {
        ResponseData<MonkDayCloseEvent> response = new ResponseData<>();
        try {
            Date date = businessDate != null ? SCMUtil.parseDate(businessDate) : SCMUtil.getCurrentBusinessDate();
            MonkDayCloseEvent event = monkDayCloseService.getMonkDayCloseEvent(unitId, date);
            response.setPayload(event);
            response.setSuccess(true);
            response.setMessage("SUCCESSFUL");
            if (event == null) {
                response.setMessage("No monk day close event found for the given parameters");
            }
        } catch (Exception e) {
            log.error("Error getting monk day close event for unit: {}", unitId, e);
            response.setSuccess(false);
            response.setMessage("Error retrieving monk day close event: " + e.getMessage());
        }
        return response;
    }

    @PutMapping(value = "monk-status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public ResponseData<MonkStatusDayClose> updateMonkStatus(@RequestParam Long monkStatusDayCloseId,
                                                             @RequestParam String monkStatus) {
        ResponseData<MonkStatusDayClose> response = new ResponseData<>();
        try {
            MonkStatusDayClose updatedStatus = monkDayCloseService.updateMonkStatus(monkStatusDayCloseId, monkStatus, RequestContext.getContext().getLoggedInUserId());
            response.setPayload(updatedStatus);
            response.setSuccess(true);
            response.setMessage("Monk status updated successfully");
        } catch (Exception e) {
            log.error("Error updating monk status: {}", monkStatusDayCloseId, e);
            response.setSuccess(false);
            response.setMessage("Error updating monk status: " + e.getMessage());
        }
        return response;
    }

    @GetMapping(value = "monk-statuses", produces = MediaType.APPLICATION_JSON)
    public ResponseData<List<MonkStatusDTO>> getAvailableMonkStatuses() {
        ResponseData<List<MonkStatusDTO>> response = new ResponseData<>();
        try {
            log.info("Getting available monk statuses...");
            List<MonkStatusDTO> statuses = monkDayCloseService.getAvailableMonkStatuses();
            response.setPayload(statuses);
            response.setSuccess(true);
            log.info("Successfully retrieved {} monk statuses", statuses.size());
        } catch (Exception e) {
            log.error("Error getting available monk statuses", e);
            response.setSuccess(false);
            response.setMessage("Error retrieving monk statuses: " + e.getMessage());
        }
        return response;
    }

    @GetMapping(value = "pending-events", produces = MediaType.APPLICATION_JSON)
    public ResponseData<List<MonkDayCloseEvent>> getPendingMonkDayCloseEvents() {
        ResponseData<List<MonkDayCloseEvent>> response = new ResponseData<>();
        try {
            List<MonkDayCloseEvent> events = monkDayCloseService.getPendingMonkDayCloseEvents();
            response.setPayload(events);
            response.setSuccess(true);
        } catch (Exception e) {
            log.error("Error getting pending monk day close events", e);
            response.setSuccess(false);
            response.setMessage("Error retrieving pending events: " + e.getMessage());
        }
        return response;
    }

    @PutMapping(value = "update-monk-by-name", produces = MediaType.APPLICATION_JSON)
    public ResponseData<MonkStatusDayClose> updateMonkStatusByName(@RequestParam Integer unitId,
                                                                   @RequestParam String monkName,
                                                                   @RequestParam String monkStatus,
                                                                   @RequestParam Integer updatedBy) {
        ResponseData<MonkStatusDayClose> response = new ResponseData<>();
        try {

            // Get the monk day close event for the unit and date
            MonkDayCloseEvent event = null;
            if (event == null) {
                // Try to get the latest event for this unit
                event = monkDayCloseService.getLatestMonkDayCloseEventByUnit(unitId);
                if (event == null) {
                    response.setSuccess(false);
                    response.setMessage("No monk day close event found for unit " + unitId );
                    return response;
                } else {
                    log.info("Using latest event from {} for unit {}",
                            SCMUtil.formatDate(event.getBusinessDate(), "yyyy-MM-dd"),
                            unitId);
                }
            }

            // Find the monk status by name
            MonkStatusDayClose targetMonkStatus = null;
            if (event.getMonkStatuses() != null) {
                for (MonkStatusDayClose monkStatusObj : event.getMonkStatuses()) {
                    if (monkName.equals(monkStatusObj.getMonkName())) {
                        targetMonkStatus = monkStatusObj;
                        break;
                    }
                }
            }

            MonkStatusDayClose updatedStatus;
            if (targetMonkStatus == null) {
                // Monk not found, create a new monk entry
                log.info("Monk '{}' not found in event for unit {}, creating new monk entry", monkName, unitId);
                updatedStatus = monkDayCloseService.createMonkStatus(event.getMonkDayCloseEventStatusId(), monkName, monkStatus, updatedBy);
                if (updatedStatus == null) {
                    response.setSuccess(false);
                    response.setMessage("Failed to create new monk '" + monkName + "' for unit " + unitId);
                    return response;
                }
                log.info("Successfully created new monk '{}' with status '{}' for unit {}", monkName, monkStatus, unitId);
            } else {
                // Update existing monk status
                updatedStatus = monkDayCloseService.updateMonkStatus(targetMonkStatus.getMonkStatusDayCloseId(), monkStatus, updatedBy);
            }
            response.setPayload(updatedStatus);
            response.setSuccess(true);
            if (targetMonkStatus == null) {
                response.setMessage("New monk '" + monkName + "' created successfully with status " + monkStatus);
            } else {
                response.setMessage("Monk status updated successfully for " + monkName);
            }

        } catch (Exception e) {
            log.error("Error updating monk status by name for unit: {}, monk: {}", unitId, monkName, e);
            response.setSuccess(false);
            response.setMessage("Error updating monk status: " + e.getMessage());
        }
        return response;
    }

    @GetMapping(value = "latest-monk-statuses", produces = MediaType.APPLICATION_JSON)
    public ResponseData<List<MonkStatusDayClose>> getLatestMonkStatusesByUnit(@RequestParam Integer unitId) {
        ResponseData<List<MonkStatusDayClose>> response = new ResponseData<>();
        try {
            // Get the latest monk statuses based on required monks count for this unit
            List<MonkStatusDayClose> monkStatuses = monkDayCloseService.getLatestMonkStatusesByUnit(unitId);

            if (monkStatuses == null || monkStatuses.isEmpty()) {
                response.setSuccess(false);
                response.setMessage("No monk statuses found for unit " + unitId + ". Either no monks are configured or no monk day close events exist.");
                return response;
            }

            // Get unit basic details to check monkDayCloseEnabled flag
            UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId);
            Boolean monkDayCloseEnabled = unitBasicDetail != null ? unitBasicDetail.getMonkDayCloseEnabled() : false;

            // Set the monkDayCloseEnabled flag for all monk statuses
            for (MonkStatusDayClose monkStatus : monkStatuses) {
                monkStatus.setMonkDayCloseEnabled(monkDayCloseEnabled);
            }

            response.setPayload(monkStatuses);
            response.setSuccess(true);
            response.setMessage("Latest monk statuses retrieved successfully for unit " + unitId +
                    " (" + monkStatuses.size() + " monks based on unit requirements)");

        } catch (Exception e) {
            log.error("Error getting latest monk statuses for unit: {}", unitId, e);
            response.setSuccess(false);
            response.setMessage("Error retrieving latest monk statuses: " + e.getMessage());
        }
        return response;
    }

    @PutMapping(value = "monk-status-with-history", produces = MediaType.APPLICATION_JSON)
    public ResponseData<MonkStatusDayClose> updateMonkStatusWithHistory(@RequestParam Long monkStatusDayCloseId,
                                                                        @RequestParam String monkStatus,
                                                                        @RequestParam(required = false) String comment,
                                                                        @RequestParam(required = false) Integer updatedBy) {
        ResponseData<MonkStatusDayClose> response = new ResponseData<>();
        try {
            MonkStatusDayClose updatedStatus = monkDayCloseService.updateMonkStatusWithHistory(monkStatusDayCloseId, monkStatus, comment, updatedBy);
            if (updatedStatus != null) {
                response.setPayload(updatedStatus);
                response.setSuccess(true);
                response.setMessage("Monk status updated successfully with history");
                log.info("Successfully updated monk status with history for ID: {} to status: {}", monkStatusDayCloseId, monkStatus);
            } else {
                response.setSuccess(false);
                response.setMessage("Failed to update monk status - record not found");
                log.warn("Failed to update monk status - record not found for ID: {}", monkStatusDayCloseId);
            }
        } catch (Exception e) {
            log.error("Error updating monk status with history for ID: {}", monkStatusDayCloseId, e);
            response.setSuccess(false);
            response.setMessage("Error updating monk status: " + e.getMessage());
        }
        return response;
    }

    @GetMapping(value = "monk-status-history", produces = MediaType.APPLICATION_JSON)
    public ResponseData<List<MonkStatusDayCloseHistory>> getMonkStatusHistory(@RequestParam Long monkStatusDayCloseId) {
        ResponseData<List<MonkStatusDayCloseHistory>> response = new ResponseData<>();
        try {
            List<MonkStatusDayCloseHistory> history = monkDayCloseService.getMonkStatusHistory(monkStatusDayCloseId);
            response.setPayload(history);
            response.setSuccess(true);
            response.setMessage("Monk status history retrieved successfully");
            log.info("Successfully retrieved {} history records for monk status ID: {}", history.size(), monkStatusDayCloseId);
        } catch (Exception e) {
            log.error("Error retrieving monk status history for ID: {}", monkStatusDayCloseId, e);
            response.setSuccess(false);
            response.setMessage("Error retrieving monk status history: " + e.getMessage());
        }
        return response;
    }

}
