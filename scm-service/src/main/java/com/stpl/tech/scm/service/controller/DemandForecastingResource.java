/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.service.controller;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.DemandForecastingService;
import com.stpl.tech.scm.domain.model.DemandForecastingRequestDto;
import com.stpl.tech.scm.domain.model.ProductDemandForecast;
import com.stpl.tech.scm.domain.model.UnitWiseOrderingStrategyDataDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping(SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.DEMAND_FORECAST_ROOT_CONTEXT)
public class DemandForecastingResource {

    @Autowired
    private DemandForecastingService demandForecastingService;

    @PostMapping("/get-demand-forecast-for-dates")
    public Map<String, ProductDemandForecast> getDemandForecastForDates(@RequestBody DemandForecastingRequestDto request) throws SumoException {
        return demandForecastingService.getDemandForecastForDates(request);
    }

    @GetMapping("/getUnitWiseOrderingStrategyData")
    public UnitWiseOrderingStrategyDataDto getUnitWiseOrderingStrategyData(@RequestParam Integer unitId) {
        return demandForecastingService.getUnitWiseOrderingStrategyData(unitId);
    }

}
