package com.stpl.tech.scm.service.controller;

import java.util.List;

import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.VehicleMasterService;
import com.stpl.tech.scm.domain.model.Vehicle;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.VEHICLE_MANAGEMENT)
public class VehicleMasterResources extends AbstractSCMResources {

	private static final Logger LOG = LoggerFactory.getLogger(VehicleMasterResources.class);

	@Autowired
	VehicleMasterService vehicleMasterService;

	@RequestMapping(method = RequestMethod.GET, value = "vehicle-list", produces = MediaType.APPLICATION_JSON)
	public List<Vehicle> getVehicleList() {
		List<Vehicle> vehicleData = vehicleMasterService.getVehicleList();
		return vehicleData;
	}

	@RequestMapping(method = RequestMethod.POST, value = "addVehicle", produces = MediaType.APPLICATION_JSON)
	public boolean addVehicle(@RequestBody final Vehicle vehicle) throws SumoException {
		LOG.info("Adding vehicle with name  " + vehicle.getName());
		return vehicleMasterService.saveVehicleDetails(vehicle);

	}

	@RequestMapping(method = RequestMethod.GET, value = "get-Vehicle", produces = MediaType.APPLICATION_JSON)
	public Vehicle getVehicle(@RequestParam final Integer vehicleId) {
		Vehicle vehicle = vehicleMasterService.getSingleVehicleData(vehicleId);
		return vehicle;
	}

	@RequestMapping(method = RequestMethod.POST, value = "saveUpdatedVehicle", produces = MediaType.APPLICATION_JSON)
	public boolean saveUpdatedVehicle(@RequestBody final Vehicle vehicle) throws SumoException {
		LOG.info("Adding vehicle with name  " + vehicle.getName());
		return vehicleMasterService.saveUpdatedVehicleDetails(vehicle);

	}

}
