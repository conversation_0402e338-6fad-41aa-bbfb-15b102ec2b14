package com.stpl.tech.scm.service.controller;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.service.MonkWastageManagementService;
import com.stpl.tech.scm.data.dao.impl.MutexFactory;
import com.stpl.tech.scm.data.model.MonkWastageDetailData;
import com.stpl.tech.scm.domain.model.MonkWastageDetailDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.MONK_WASTAGE_ROOT_CONTEXT)
public class MonkWastageManagementResource {

    private static final Logger LOG = LoggerFactory.getLogger(MonkWastageManagementResource.class);

    @Autowired
    private MonkWastageManagementService monkWastageManagementService;

    @Autowired
    private MutexFactory<String> mutexFactory;

    /**
     * Endpoint to save monk wastage detail
     * @param wastageDetailDto the wastage detail DTO
     * @param unitId the unit ID
     * @return ResponseEntity with success/error message
     */
    @PostMapping("/save")
    public ResponseEntity<String> saveMonkWastageDetail(@RequestBody MonkWastageDetailDto wastageDetailDto, 
                                                       @RequestParam Integer unitId) {
        try {
            LOG.info("Saving monk wastage detail for unit: {}, product: {}", 
                    unitId, wastageDetailDto.getMilkProductId());
            
            monkWastageManagementService.saveMonkWastageDetail(wastageDetailDto, unitId);
            
            return ResponseEntity.ok("Monk wastage detail saved successfully");
        } catch (Exception e) {
            LOG.error("Error saving monk wastage detail for unit {}: ", unitId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to save monk wastage detail: " + e.getMessage());
        }
    }

    /**
     * Endpoint to process unprocessed wastage details for a specific unit
     * @param unitId the unit ID to process
     * @param bypassThreshold if true, bypasses the quantity threshold check
     * @return ResponseEntity with success/error message
     */
    @PostMapping("/process/{unitId}")
    public ResponseEntity<String> processWastageForUnit(@PathVariable Integer unitId, 
                                                       @RequestParam(defaultValue = "false") boolean bypassThreshold) {
        try {
            LOG.info("Processing unprocessed wastage details for unit: {} (bypassThreshold: {})", unitId, bypassThreshold);
            
            monkWastageManagementService.processUnprocessedWastageDetailsForUnit(unitId, bypassThreshold);
            
            return ResponseEntity.ok("Wastage processing completed successfully for unit: " + unitId);
        } catch (Exception e) {
            LOG.error("Error processing wastage for unit {}: ", unitId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to process wastage for unit " + unitId + ": " + e.getMessage());
        }
    }

    /**
     * Endpoint to process unprocessed wastage details for all units
     * @param bypassThreshold if true, bypasses the quantity threshold check
     * @return ResponseEntity with success/error message
     */
    @PostMapping("/process/all")
    public ResponseEntity<String> processWastageForAllUnits(@RequestParam(defaultValue = "false") boolean bypassThreshold) {
        try {
            LOG.info("Processing unprocessed wastage details for all units (bypassThreshold: {})", bypassThreshold);
            
            monkWastageManagementService.processUnprocessedWastageDetailsForUnit(null, bypassThreshold);
            
            return ResponseEntity.ok("Wastage processing completed successfully for all units");
        } catch (Exception e) {
            LOG.error("Error processing wastage for all units: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to process wastage for all units: " + e.getMessage());
        }
    }

    /**
     * Endpoint to get unprocessed wastage details for a specific unit
     * @param unitId the unit ID
     * @return ResponseEntity with list of unprocessed wastage details
     */
    @GetMapping("/unprocessed/{unitId}")
    public ResponseEntity<List<MonkWastageDetailData>> getUnprocessedWastageDetails(@PathVariable Integer unitId) {
        try {
            LOG.info("Getting unprocessed wastage details for unit: {}", unitId);
            
            List<MonkWastageDetailData> unprocessedDetails = monkWastageManagementService.getUnprocessedWastageDetails(unitId);
            
            return ResponseEntity.ok(unprocessedDetails);
        } catch (Exception e) {
            LOG.error("Error getting unprocessed wastage details for unit {}: ", unitId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Endpoint to get unprocessed wastage details grouped by unit
     * @return ResponseEntity with map of unit ID to list of unprocessed wastage details
     */
    @GetMapping("/unprocessed/all")
    public ResponseEntity<Map<Integer, List<MonkWastageDetailData>>> getAllUnprocessedWastageDetails() {
        try {
            LOG.info("Getting all unprocessed wastage details grouped by unit");
            
            Map<Integer, List<MonkWastageDetailData>> unprocessedDataByUnit = 
                    monkWastageManagementService.getAllUnprocessedWastageDetailsGroupedByUnit();
            
            return ResponseEntity.ok(unprocessedDataByUnit);
        } catch (Exception e) {
            LOG.error("Error getting all unprocessed wastage details: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Scheduled endpoint that runs every 20 minutes to process unprocessed monk wastage details
     * Creates wastage events for accumulated wastage data and updates processing status
     * Uses mutex locking on unit_id to ensure thread safety
     */
    @Scheduled(cron = "0 */20 * * * *", zone = "GMT+05:30")
    @PostMapping("/scheduled-process")
    public ResponseEntity<String> scheduledProcessMonkWastageDetails() {
        try {
            LOG.info("Starting scheduled monk wastage processing job");

            // Get all unprocessed wastage details grouped by unit
            Map<Integer, List<MonkWastageDetailData>> unprocessedDataByUnit =
                    monkWastageManagementService.getUnprocessedWastageDetailsGroupedByUnit(null);

            if (unprocessedDataByUnit.isEmpty()) {
                LOG.info("No unprocessed wastage details found for any unit");
                return ResponseEntity.ok("No unprocessed wastage details found for any unit");
            }

            LOG.info("Processing unprocessed wastage details for {} units", unprocessedDataByUnit.size());

            // Process each unit with mutex locking
            for (Map.Entry<Integer, List<MonkWastageDetailData>> entry : unprocessedDataByUnit.entrySet()) {
                Integer unitId = entry.getKey();

                // Apply mutex lock on unit_id outside of transaction
                synchronized (mutexFactory.getMutex(unitId + "_WASTAGE_PROCESSING")) {
                    LOG.info("Acquired mutex lock for unit: {}", unitId);
                    try {
                        // Process wastage details for this specific unit
                        monkWastageManagementService.processUnprocessedWastageDetailsForUnit(unitId, false);
                        LOG.info("Successfully processed wastage details for unit: {}", unitId);
                    } catch (Exception e) {
                        LOG.error("Error processing wastage details for unit {}: ", unitId, e);
                        // Continue processing other units even if one fails
                    } finally {
                        LOG.info("Released mutex lock for unit: {}", unitId);
                    }
                }
            }

            LOG.info("Completed scheduled monk wastage processing job successfully");
            return ResponseEntity.ok("Scheduled monk wastage processing completed successfully");
        } catch (Exception e) {
            LOG.error("Error in scheduled monk wastage processing job: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error in scheduled monk wastage processing job: " + e.getMessage());
        }
    }
} 