package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.SCMUnitManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.UnitDetailData;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;
import com.stpl.tech.scm.domain.model.BusinessCostCenter;
import com.stpl.tech.scm.domain.model.DeactivateValidateResponse;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.NsoEventAssetsResponse;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.util.AppConstants;
import net.bull.javamelody.internal.common.LOG;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.net.URISyntaxException;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;

/**
 * Created by Rahul Singh on 07-05-2016.
 */

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.UNIT_MANAGEMENT_ROOT_CONTEXT)
public class UnitManagementResources {

	@Autowired
	private SCMUnitManagementService scmUnitManagementService;

	@Autowired
	private MasterDataCache masterDataCache;

	@RequestMapping(method = RequestMethod.GET, value = "unit", produces = MediaType.APPLICATION_JSON)
	public UnitDetail viewUnit(@RequestParam final int unitId) {
		return scmUnitManagementService.viewUnit(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "units", produces = MediaType.APPLICATION_JSON)
	public List<UnitDetail> viewAllUnits() {
		return scmUnitManagementService.viewAllUnits();
	}

	@RequestMapping(method = RequestMethod.POST, value = "unit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public UnitDetailData addUnit(@RequestBody final UnitBasicDetail unitDetail) throws SumoException {
		return scmUnitManagementService.addUnitDetail(SCMDataConverter.convert(unitDetail));
	}

	@RequestMapping(method = RequestMethod.PUT, value = "unit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public boolean updateUnit(@RequestBody final UnitDetail unitDetail) {
		return scmUnitManagementService.updateUnit(unitDetail);
	}

	@RequestMapping(method = RequestMethod.POST, value = "unit-deactivate", produces = MediaType.APPLICATION_JSON)
	public DeactivateValidateResponse deactivateUnit(@RequestBody final int unitId) throws SumoException, URISyntaxException {
		try {
			return scmUnitManagementService.deactivateUnit(unitId);
		}catch (Exception e){
			LOG.info("Error While Deactivating Unit In Sumo ", e);
		}
		return new DeactivateValidateResponse();

	}

	@RequestMapping(method = RequestMethod.POST, value = "unit-activate", produces = MediaType.APPLICATION_JSON)
	public boolean activateUnit(@RequestBody final int unitId) throws SumoException {
		return scmUnitManagementService.activateUnit(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "units-for-adhoc-request", produces = MediaType.APPLICATION_JSON)
	public List<UnitDetail> viewUnitsForAdhocRequest(@RequestParam final int unitId) {
		return scmUnitManagementService.viewUnitsForAdhocRequest(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "business-date", produces = MediaType.APPLICATION_JSON)
	public Date getBusinessDate() {
		return SCMUtil.getCurrentBusinessDate();
	}

	@RequestMapping(method = RequestMethod.POST, value = "unit/add", produces = MediaType.APPLICATION_JSON)
	public boolean addUnit(@RequestBody final int unitId) throws SumoException {
		return scmUnitManagementService.addUnit(unitId);
	}

	/**
	 * Unit to FulfillmentUnit Mappings API
	 */
	@RequestMapping(method = RequestMethod.GET, value = "fulfillment-types", produces = MediaType.APPLICATION_JSON)
	public FulfillmentType[] getFulfillmentType() throws SumoException {
		return FulfillmentType.values();
	}

	@RequestMapping(method = RequestMethod.GET, value = "fulfillment-type-mapping", produces = MediaType.APPLICATION_JSON)
	public List<FulfillmentUnitMappingData> getUnitToFulfillmentTypeMapping(@RequestParam final int unitId)
			throws SumoException {
		return scmUnitManagementService.getUnitToFulfillmentTypeMapping(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "distance-mapping", produces = MediaType.APPLICATION_JSON)
	public List<UnitDistanceMappingData> getUnitDistanceMapping(@RequestParam final int sourceUnitId,
			@RequestParam final int destinationUnitId) throws SumoException {
		return scmUnitManagementService.getUnitDistanceMapping(sourceUnitId, destinationUnitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "distance-mapping/update", produces = MediaType.APPLICATION_JSON)
	public List<UnitDistanceMappingData> updateUnitDistanceMapping(
			@RequestBody final List<UnitDistanceMappingData> list) throws SumoException {
		return scmUnitManagementService.updateUnitDistanceMapping(list);
	}

	@RequestMapping(method = RequestMethod.POST,value="scm-unit-update", produces = MediaType.APPLICATION_JSON)
	public Boolean updateScmUnit(@RequestBody final Unit unit) throws SumoException{
		return scmUnitManagementService.updateSCMUnit(unit);
	}

	@RequestMapping(method = RequestMethod.GET,value="business-cost-center-by-companyId", produces = MediaType.APPLICATION_JSON)
	public List<BusinessCostCenter> getBusinessCostCentersByCompanyId(@RequestParam final int companyId, @RequestParam final String accountType , @RequestParam(required = false)final String businessCostCentreCode) throws SumoException{
		return scmUnitManagementService.getBusinessCostCentersByCompanyId(companyId,accountType,businessCostCentreCode);
	}

	@RequestMapping(method = RequestMethod.GET,value="business-cost-centers-by-type", produces = MediaType.APPLICATION_JSON)
	public List<BusinessCostCenter> getBusinessCostCentersByCompanyId(@RequestParam(required = false) final String businessCostCentreType ) throws SumoException{
		return scmUnitManagementService.getBusinessCostCentersByType(businessCostCentreType);
	}

	@GetMapping(value="fetch-nso-event-assets", produces = MediaType.APPLICATION_JSON)
	public NsoEventAssetsResponse getAssetsToHandover(@RequestParam final int unitId) throws SumoException{
		return scmUnitManagementService.getAssetsToHandover(unitId);
	}

	@RequestMapping(method = RequestMethod.GET,value="get-all-master-units", produces = MediaType.APPLICATION_JSON)
	public Set<UnitBasicDetail> getAllMasterUnits() {
		LOG.info("Getting All units ");
		Set<UnitBasicDetail> sortedUnits = new TreeSet<>(Comparator.comparing(UnitBasicDetail::getName));
		sortedUnits.addAll(masterDataCache.getAllUnits());
		return sortedUnits;
	}
}
