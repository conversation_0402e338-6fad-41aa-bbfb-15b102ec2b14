package com.stpl.tech.scm.service.controller;

import com.google.gson.Gson;
import com.stpl.tech.master.core.UnitClosureStateEnum;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.SchedulerStatusDao;
import com.stpl.tech.scm.data.enums.SchedulerStatus;
import com.stpl.tech.scm.data.model.SchedulerStatusData;
import com.stpl.tech.scm.domain.model.NotificationType;
import com.stpl.tech.scm.domain.model.OrderTransferType;
import com.stpl.tech.scm.domain.model.OrdersDetailsShort;
import com.stpl.tech.scm.domain.model.PlanOrderItem;
import com.stpl.tech.scm.domain.model.PlanOrderItemPrep;
import com.stpl.tech.scm.domain.model.ProductionPlanEvent;
import com.stpl.tech.scm.domain.model.ProductionPlanningData;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.RequestOrderResponse;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.StockTakeSubType;
import com.stpl.tech.scm.domain.model.UnitPlanItemRequest;
import com.stpl.tech.scm.service.annotation.DayClosureCheck;
import com.stpl.tech.scm.service.annotation.UnitClosureCheck;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.service.model.AcknowledgeOrders;
import com.stpl.tech.scm.service.model.DataRequest;
import com.stpl.tech.scm.service.model.PlanOrderList;
import com.stpl.tech.scm.service.scheduled.VendorNotificationResource;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.config.CronTask;
import org.springframework.scheduling.config.ScheduledTask;
import org.springframework.scheduling.config.ScheduledTaskHolder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Created by Rahul Singh on 08-06-2016.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.REQUEST_ORDER_MANAGEMENT_ROOT_CONTEXT)
public class RequestOrderManagementResources extends AbstractSCMResources {

	private final Logger LOG = LoggerFactory.getLogger(RequestOrderManagementResources.class);

	@Autowired
	private RequestOrderManagementService requestOrderManagementService;

	@Autowired
    private SCMNotificationService scmNotificationService;

	@Autowired
    private VendorNotificationResource notificationResource;

	@Autowired
	private ExcelViewGenerator excelViewService;

	@Autowired
	private StockManagementService stockService;

	@Autowired
	private EnvProperties props;

	@RequestMapping(method = RequestMethod.GET, value = "request-order-find", produces = MediaType.APPLICATION_JSON)
	public List<RequestOrder> getRequestOrders(
			@RequestParam final String startDate, @RequestParam final String endDate,
			@RequestParam(required = false) final Integer fulfillingUnitId,
			@RequestParam(required = false) final Integer requestingUnitId,
			@RequestParam(required = false) final SCMOrderStatus status,
			@RequestParam(required = false) final Integer requestOrderId,
			@RequestParam(required = false) final String searchTag) {

		Date start = SCMUtil.getDate(SCMUtil.parseDate(startDate));
		Date end = SCMUtil.getDate(SCMUtil.getNextDate(SCMUtil.parseDate(endDate)));
		return requestOrderManagementService.getRequestOrders(fulfillingUnitId, requestingUnitId, start, end, status,
				requestOrderId, searchTag);
	}

	@RequestMapping(method = RequestMethod.POST, value = "request-orders-find", produces = MediaType.APPLICATION_JSON)
	public List<RequestOrder> getRequestOrder(@RequestBody final List<Integer> requestOrderIds) {
		return requestOrderManagementService.getRequestOrdersByIds(requestOrderIds);
	}

	@RequestMapping(method = RequestMethod.GET, value = "request-order", produces = MediaType.APPLICATION_JSON)
	public RequestOrder getRequestOrder(@RequestParam final int requestOrderId) {
		return requestOrderManagementService.getRequestOrder(requestOrderId);
	}

	@DayClosureCheck
	@UnitClosureCheck(closeState = UnitClosureStateEnum.CLOSED)
	@RequestMapping(method = RequestMethod.POST, value = "request-order", produces = MediaType.APPLICATION_JSON)
	public List<RequestOrderResponse> createRequestOrder(HttpServletRequest request, @RequestBody final RequestOrder requestOrder)
			throws DayCloseInitiatedException, SumoException, DataNotFoundException, TransferOrderCreationException, InventoryUpdateException {
		return requestOrderManagementService.createRequestOrder(requestOrder);
	}


	@RequestMapping(method = RequestMethod.POST, value = "get-last-week-average", produces = MediaType.APPLICATION_JSON)
	public Map<Integer,BigDecimal> getLastWeekAverageQty(@RequestBody List<Integer> productIds , @RequestParam Integer unitId,@RequestParam Boolean isSpecial) {
		return requestOrderManagementService.getLastWeekAverageQty(productIds,unitId,isSpecial);
	}


	@DayClosureCheck
	@UnitClosureCheck(closeState = UnitClosureStateEnum.CLOSED)
	@RequestMapping(method = RequestMethod.POST, value = "multiple-request-order", produces = MediaType.APPLICATION_JSON)
	public List<RequestOrderResponse> createMultipleRequestOrder(HttpServletRequest request, @RequestBody final RequestOrder requestOrder)
			throws DayCloseInitiatedException, SumoException, DataNotFoundException, DataUpdationException, TransferOrderCreationException, InventoryUpdateException {
		return requestOrderManagementService.createMultipleRequestOrder(requestOrder);
	}


	@RequestMapping(method = RequestMethod.GET, value = "notify-vendors-for-special-orders", consumes = MediaType.TEXT_PLAIN, produces = MediaType.APPLICATION_JSON)
	public List<Integer> getRequestOrdersFromReferenceOrder(@RequestParam final String type,
			@RequestParam final String startTime, @RequestParam final String endTime,
			@RequestParam final boolean sendSMS) {
		List<RequestOrder> requestOrderList = requestOrderManagementService.getSpecializedROForNotification(
				NotificationType.valueOf(type), AppUtils.parseDateIST(startTime), AppUtils.parseDateIST(endTime));
		List<Integer> roIds = scmNotificationService.sendVendorRONotification(sendSMS, requestOrderList,
				NotificationType.valueOf(type),false);
		if(roIds != null && roIds.size() > 0) {
			requestOrderManagementService.markRequestOrderNotified(roIds);
		}
		return roIds;
	}

	@RequestMapping(method = RequestMethod.GET, value = "cron-to-send-consolidated-ro-email-to-vendor")
	public boolean runCronToSendConsolidatedROEmailsToVendor() {
		notificationResource.sendConsolidatedROEmailsToVendor(false,null);
		return true;
	}

	@Scheduled(cron = "0 1 6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23 * * *", zone = "GMT+05:30")
	public void cronToSendConsolidatedROEmailsToVendor() throws NoSuchMethodException {
	try{
		Method m =  getClass().getMethod("cronToSendConsolidatedROEmailsToVendor");
		Scheduled scheduled =  m.getAnnotation(Scheduled.class);
		String cronExp = scheduled.cron();
		LOG.info("Calling alternate scheduler from VENDOR NOTIFICATION:::::::::::======>>>>>>>>");
		notificationResource.sendConsolidatedROEmailsToVendor(true,cronExp);
	}catch(Exception e){
		LOG.info("Error in cronToSendConsolidatedROEmailsToVendor : {} ",e.getMessage());
		e.printStackTrace();
	}
	}

	@Scheduled(cron="0 40 05 * * *",zone = "GMT+05:30")
	void setSchedulerData(){
		notificationResource.setSchedulerData();
	}

	@RequestMapping(method = RequestMethod.GET, value = "request-order-by-reference", consumes = MediaType.TEXT_PLAIN, produces = MediaType.APPLICATION_JSON)
	public List<RequestOrder> getRequestOrdersFromReferenceOrder(@RequestParam final int referenceOrderId) {
		return requestOrderManagementService.getRequestOrdersFromReferenceOrder(referenceOrderId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "request-orders-pending", produces = MediaType.APPLICATION_JSON)
	public List<RequestOrder> getPendingRequestOrders(@RequestParam(required = false) final Integer unitId,
			@RequestParam(required = false) final String date) {
		Date fulfilmentDate = SCMUtil.parseDate(date);
		return requestOrderManagementService.getPendingRequestOrders(unitId, fulfilmentDate);
	}

	@RequestMapping(method = RequestMethod.GET, value = "request-orders-pending-short", produces = MediaType.APPLICATION_JSON)
	public List<OrdersDetailsShort> getPendingRoShort(@RequestParam(required = false) final Integer unitId,
															@RequestParam(required = false) final String date) {
		Date fulfilmentDate = SCMUtil.parseDate(date);
		return requestOrderManagementService.getPendingRoShort(unitId, fulfilmentDate);
	}

	@RequestMapping(method = RequestMethod.GET, value = "request-orders-by-fulfillment-date", produces = MediaType.APPLICATION_JSON)
	public List<RequestOrder> getPendingRequestOrdersByFulfilmentDate(@RequestParam final String date,
                                                                      @RequestParam final int fulfillmentUnit,
                                                                      @RequestParam(required = false)OrderTransferType transferType) {
		List<RequestOrder> orders = requestOrderManagementService.getPendingRequestOrders(date, fulfillmentUnit);

        if(transferType==null){
            transferType = OrderTransferType.TRANSFER;
        }

        OrderTransferType finalTransferType = transferType;
        return orders!=null ? orders.stream()
                .filter(requestOrder -> requestOrder.getTransferType().equals(finalTransferType))
                .collect(Collectors.toList()) : Collections.emptyList();
	}

	@RequestMapping(method = RequestMethod.GET, value = "request-orders-by-fulfillment-date-short", produces = MediaType.APPLICATION_JSON)
	public ProductionPlanningData getPendingRequestOrdersByFulfilmentDate(@RequestParam final String date,
																		  @RequestParam final int fulfillmentUnit,
																		  @RequestParam boolean fetchAcknowledged,
																		  @RequestParam(required = false)OrderTransferType transferType,
																		  @RequestParam final String region) {
		ProductionPlanningData data=new ProductionPlanningData();
		List<OrdersDetailsShort> orders = requestOrderManagementService.getPendingRequestOrdersShort(date, fulfillmentUnit, fetchAcknowledged);

//		if(transferType==null){
//			transferType = OrderTransferType.TRANSFER;
//		}

//		OrderTransferType finalTransferType = transferType;
		if(orders != null){
			data.setOrderDetailShort(orders);
		}
//
//		 data.setOrderDetailShort(orders!=null ? orders.stream()
//			 .filter(requestOrder -> requestOrder.getTransferType().equals(finalTransferType))
//			 .collect(Collectors.toList()) : Collections.emptyList());
		 data.setProductionPlanningSummary(requestOrderManagementService.getProductionPlanningSummary(date,region,fulfillmentUnit));
		 data.setUnitWiseSummary(requestOrderManagementService.getUnitWiseGntAndChaayos(date,fulfillmentUnit,data.getProductionPlanningSummary(),region));
		return data;
	}

	@RequestMapping(method = RequestMethod.POST, value = "plans-by-fulfillment-date", produces = MediaType.APPLICATION_JSON)
	public List<ProductionPlanEvent> getPlansByFulfilmentDate(@RequestBody final DataRequest dataRequest) {
		return requestOrderManagementService.getPlansByFulfilmentDate(dataRequest.getStartDate(),
				dataRequest.getEndDate(), dataRequest.getUnitId());
	}

	@RequestMapping(method = RequestMethod.POST, value = "acknowledge-orders")
	public View acknowledgeRequestOrders(@RequestBody AcknowledgeOrders acknowledgeOrders) {
		if (requestOrderManagementService.acknowledgeOrders(acknowledgeOrders.getRequestOrders(),
				acknowledgeOrders.getLastUpdatedBy())) {
			return excelViewService.getRequestOrderView(acknowledgeOrders.getRequestOrders(),
					acknowledgeOrders.getFulfillmentDate(), null);
		} else {
			return null;
		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "start-planning")
	public ProductionPlanEvent startPlanning(@RequestBody PlanOrderList planOrderList) throws Exception {
		try {
			LOG.info("Removing duplicate keys While Start Planning");
			List<Integer> ids = stockService.fetchDuplicateKey(false);
			if (ids != null && ids.size() > 0) {
				stockService.updateDuplicateKeyLatestFlag(ids,false);
				SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
						SlackNotification.SUPPLY_CHAIN, "Number of Duplicate Keys Removed = " + ids.size());
			}
		}
		catch (Exception e){
			LOG.error("Error in Removing duplicate keys", e);
		}
		LOG.info("Request to Start Production Planning on unit {} for fulfillment Date {}", planOrderList.getUnitId(),
			planOrderList.getFulfillmentDate());
		return requestOrderManagementService.startPlanning(planOrderList.getFulfillmentDate(), planOrderList.getList(),
			planOrderList.getLastUpdatedBy(), planOrderList.getUnitId());
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-planning")
	public boolean updatePlanning(@RequestBody List<PlanOrderItem> planOrderItemList,
								  @RequestParam final int eventId) throws Exception {
		LOG.info("Request to Update Production Planning");
		return requestOrderManagementService.updateCalculatedProductionItemsData(eventId, planOrderItemList);
	}

	@RequestMapping(method = RequestMethod.POST, value = "semi-finished/get-items" )
	public PlanOrderItemPrep getPlanItemsForSemiFinishedProduct(@RequestBody RequestOrderItem requestOrderItem , @RequestParam(required = false) Boolean isSkusRequired) throws SumoException, DataNotFoundException {
		LOG.info("Request to get Recipe wise items for product {} and quantity {}", requestOrderItem.getProductId(),
				requestOrderItem.getRequestedQuantity());
		return requestOrderManagementService.getPlanItemsForSemiFinishedProduct(requestOrderItem,isSkusRequired);
	}

	@RequestMapping(method = RequestMethod.POST, value = "prep-plan-submit/{requestUnitId}")
	public PlanOrderItemPrep submitPlanOrderItemPreparation(@RequestBody PlanOrderItemPrep planOrderItemPrep , @PathVariable Integer requestUnitId) throws SumoException, DataNotFoundException {
		LOG.info("Request to submit planOrderItemPrep {}", new Gson().toJson(planOrderItemPrep));
		return requestOrderManagementService.submitPlanOrderItemPreparation(planOrderItemPrep, requestUnitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "prep-plans-get/{requestUnitId}")
	public List<PlanOrderItemPrep> getPlanOrderItemPreparations(@RequestBody Integer itemId, @PathVariable Integer requesUnitId) throws SumoException, DataNotFoundException {
		LOG.info("Request to submit planOrderItemPrep {}", itemId);
		return requestOrderManagementService.getPlanOrderItemPreparations(itemId, requesUnitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "acknowledge-plan-orders")
	public View acknowledgeEventOrders(@RequestBody int eventId) {
		LOG.info("Request to Acknowledge Orders for Production Plan {}", eventId);
		if (requestOrderManagementService.acknowledgeEventOrders(eventId)) {
			ProductionPlanEvent event = requestOrderManagementService.getPlanningEvent(eventId);
			List<RequestOrder> requestOrders = new ArrayList<>();
			for (Integer i : event.getRequestOrders()) {
				requestOrders.add(requestOrderManagementService.getRequestOrder(i));
			}
			return excelViewService.getPlanningView(event, requestOrders);
		}
		return null;

	}

	@RequestMapping(method = RequestMethod.POST, value = "download-plan-orders")
	public View downloadPlanOrders(@RequestBody int eventId) {
		LOG.info("Request to download excel for Production Plan {}", eventId);
		ProductionPlanEvent event = requestOrderManagementService.getPlanningEvent(eventId);
		List<RequestOrder> requestOrders = new ArrayList<>();
		for (Integer i : event.getRequestOrders()) {
			requestOrders.add(requestOrderManagementService.getRequestOrder(i));
		}
		return excelViewService.getPlanningView(event, requestOrders);
	}

	@RequestMapping(method = RequestMethod.POST, value = "download-plan-orders-zip")
	public void downloadPlanOrders(HttpServletResponse response, @RequestBody List<Integer> planIds) throws DataNotFoundException, IOException, SumoException {
		LOG.info("Request to download excel for Production Plan {}", planIds.toString());
		List<ProductionPlanEvent> events = requestOrderManagementService.getPlanningEventBulk(planIds);
		//List<View> finalViews = new ArrayList<>();
		String path = "/data/app/s3/tempFiles/tempZipFiles/";
		List<String> srcFiles = new ArrayList<>();
		for (ProductionPlanEvent event : events) {
			List<RequestOrder> requestOrders = new ArrayList<>();
			for (Integer i : event.getRequestOrders()) {
				requestOrders.add(requestOrderManagementService.getRequestOrder(i));
			}
			excelViewService.getPlanningViewBulk(event,requestOrders,path,srcFiles);
			//finalViews.add(excelViewService.getPlanningView(event, requestOrders));
		}
		fileToZipCreator(response, path ,srcFiles );
	}

	public void fileToZipCreator(HttpServletResponse response, String path, List<String> srcFiles) throws DataNotFoundException, IOException, SumoException{
		File tempZipFile = new File(path + "multiCompressed.zip");
		tempZipFile.createNewFile();
		FileOutputStream fos = new FileOutputStream(tempZipFile);
		ZipOutputStream zipOut = new ZipOutputStream(fos);
		for (String srcFile : srcFiles) {
			File fileToZip = new File(srcFile);
			FileInputStream fis = new FileInputStream(fileToZip);
			ZipEntry zipEntry = new ZipEntry(fileToZip.getName());
			zipOut.putNextEntry(zipEntry);
			byte[] bytes = new byte[1024];
			int length;
			while((length = fis.read(bytes)) >= 0) {
				zipOut.write(bytes, 0, length);
			}
			fis.close();
		}
		zipOut.close();
		fos.close();

		responseHeaderSetter(response, tempZipFile, srcFiles);
	}

	public void responseHeaderSetter(HttpServletResponse response, File tempZipFile , List<String> srcFiles) throws DataNotFoundException, IOException, SumoException{
		response.setContentType(AppConstants.ZIP_MIME_TYPE);
		response.addHeader("Content-Disposition", "attachment; filename=" + tempZipFile.getName());
		byte[] bytesArray =    new byte[(int) tempZipFile.length()];
		response.setContentLength(bytesArray.length);
		try {
			OutputStream outputStream = response.getOutputStream();
			InputStream inputStream = new FileInputStream(tempZipFile);
			int counter = 0;
			while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
				outputStream.write(bytesArray, 0, counter);
				outputStream.flush();
			}
			outputStream.close();
			inputStream.close();
		} catch (IOException e) {
			LOG.error("Encountered error while writing file to response stream",e);
			throw e;
		} finally {
			response.getOutputStream().flush();
			tempZipFile.delete(); // delete the temporary Zip created after completing request
		}
		deleteDirectory(srcFiles);
	}

	boolean deleteDirectory(List<String> filesToBeDeleted) {
		filesToBeDeleted.forEach(filePath ->{
			new File(filePath).delete();
		});
		return true;
	}

	@RequestMapping(method = RequestMethod.POST, value = "download-order")
	public View downloadRO(@RequestBody RequestOrder requestOrder) {
		if (requestOrder != null) {
			return excelViewService.getRequestOrderView(requestOrder);
		} else {
			return null;
		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "download-plan-order")
	public View downloadRO(@RequestBody int roId) {
		if (roId > 0) {
			RequestOrder requestOrder = requestOrderManagementService.getRequestOrder(roId);
			return excelViewService.getRequestOrderView(requestOrder);
		} else {
			return null;
		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "download-plan-order-all")
	public View downloadRO(@RequestBody List<Integer> roIds) {
		if (roIds.size() > 1) {
			List<RequestOrder> requestOrders = requestOrderManagementService.getRequestOrdersByIds(roIds);
			return excelViewService.getRequestOrdersView(requestOrders);
		} else {
			return null;
		}
	}



	@RequestMapping(method = RequestMethod.PUT, value = "request-order-cancel", consumes = MediaType.APPLICATION_JSON)
	public Integer cancelRequestOrder(@RequestBody final Map requestObj) throws SumoException {
		Integer requestOrderId = (Integer) requestObj.get("orderId");
		Integer updatedBy = (Integer) requestObj.get("updatedBy");
		return requestOrderManagementService.cancelRequestOrder(requestOrderId, updatedBy);
	}

	@RequestMapping(method = RequestMethod.GET, value = "special-request-orders-for-day", produces = MediaType.APPLICATION_JSON)
	public Map<Integer, List<RequestOrder>> sendConsolidatedROSMSToVendorForBreads(@RequestParam String requestDate) {
		Date date = SCMUtil.getDate(SCMUtil.parseDate(requestDate));
		return requestOrderManagementService.getSpecialOrdersForDate(date);
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-tag",
			consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public Boolean updateSearchTag(@RequestBody Map<String,String> body){
		if(body.containsKey("roId")){
			return requestOrderManagementService.updateTag(Integer.parseInt(body.get("roId")), body.get("tag"));
		}
		return false;
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload-excess-planning", consumes = MediaType.APPLICATION_JSON)
	public Boolean updateExcessPlanning(@RequestBody final List<PlanOrderItem> list,
										@RequestParam Integer planId) throws IOException, SumoException {
		return requestOrderManagementService.excessPlanning(list, planId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-production-summary")
	public ProductionPlanningData getSummaryData(@RequestParam final String date,
												 @RequestParam final int fulfillmentUnit,
												 @RequestParam final String region) {
		ProductionPlanningData prodPlanningData = new ProductionPlanningData();
		prodPlanningData.setProductionPlanningSummary(requestOrderManagementService.getProductionPlanningSummary(date,
			region, fulfillmentUnit));
		prodPlanningData.setUnitWiseSummary(requestOrderManagementService.getUnitWiseGntAndChaayos(date, fulfillmentUnit,
			prodPlanningData.getProductionPlanningSummary(), region));
		return prodPlanningData;
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-ro-items-by-planId",produces = MediaType.APPLICATION_JSON)
	public List<UnitPlanItemRequest> getRoItemsByPlanId(@RequestParam Integer planId) throws SumoException {
		return requestOrderManagementService.getRoItemsByPlanId(planId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-adjusted-quantities",produces = MediaType.APPLICATION_JSON)
	public Boolean updateAdjustedQuantities(@RequestBody final List<UnitPlanItemRequest> list,@RequestParam String updatedBy) throws SumoException {
		return requestOrderManagementService.updateAdjustedQuantities(list,updatedBy);
	}

	@RequestMapping(method = RequestMethod.POST, value = "get-product-prices-fulfillment-unit",produces = MediaType.APPLICATION_JSON)
	public Map<Integer, Pair<BigDecimal,BigDecimal>> getCurrentPricesForFullfilmentUnit(@RequestBody Map<Integer,Integer> productToSkuMap, @RequestParam Integer fullfilmentUnitId
			, @RequestParam Integer requestingUnitId , @RequestParam Boolean isFA  ) throws SumoException {
		return requestOrderManagementService.getCurrentPriceAtFulfillingUnit(productToSkuMap,fullfilmentUnitId, requestingUnitId,isFA);
	}

	@RequestMapping(method = RequestMethod.POST, value = "get-product-dis-continued-status")
	public Map<Integer, Boolean> getProductDiscontinuedStatus(@RequestBody List<Integer> productIds, @RequestParam Integer fulfilmentUnitId,
															  @RequestParam String fulfilmentDate) {
		return requestOrderManagementService.getProductDiscontinuedStatus(productIds ,fulfilmentUnitId, AppUtils.getDate(fulfilmentDate, "yyyy-MM-dd"));
	}

	@PostMapping(value = "preview-plan-orders")
	public View previewEventOrders(@RequestBody PlanOrderList planOrderList) {
		LOG.info("Request to preview Orders for Production Plan for roids {}", planOrderList.getList());
		List<RequestOrder> requestOrders = new ArrayList<>();
		if(Objects.nonNull(planOrderList.getList()) && Objects.nonNull(planOrderList.getFulfillmentDate())){
			for (Integer roId : planOrderList.getList()) {
				requestOrders.add(requestOrderManagementService.getRequestOrder(roId));
			}
			return excelViewService.previewOrderView(requestOrders,planOrderList.getFulfillmentDate());
		}
		return null;
	}

}
