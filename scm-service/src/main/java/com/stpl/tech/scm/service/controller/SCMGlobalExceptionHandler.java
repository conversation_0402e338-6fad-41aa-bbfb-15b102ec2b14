package com.stpl.tech.scm.service.controller;

import com.stpl.tech.scm.core.exception.SCMError;
import com.stpl.tech.scm.core.exception.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class SCMGlobalExceptionHandler {

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<SCMError> handleValidationException(ValidationException ex) {
        SCMError error = new SCMError("Validation Error", ex.getMessage(), 109);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }


}
