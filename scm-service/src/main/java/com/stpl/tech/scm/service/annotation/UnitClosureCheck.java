package com.stpl.tech.scm.service.annotation;

import com.stpl.tech.master.core.UnitClosureStateEnum;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface UnitClosureCheck{
     UnitClosureStateEnum closeState() default UnitClosureStateEnum.NO;

     boolean bypassCloseState() default false;
}
