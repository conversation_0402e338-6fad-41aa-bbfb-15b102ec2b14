<style>
table#wastageTable tr th, td {
	width: 65px;
}

table#wastageTable tr input.packaging-qty {
	max-width: 75px;
}

div.packaging-div {
	display: block;
	word-break: keep-all;
	margin-bottom: 5px;
}

div.packaging-div span {
	vertical-align: middle;
}

div.packaging-div span.pointer {
	display: inline-block;
	background-color: #26a69a;
	color: #fff;
	border-radius: 20px;
	width: 18px;
	line-height: 18px;
	font-size: 20px;
	text-align: center;
}

input.waste-value {
	color: #000 !important;
}
</style>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
	<div class="col s12">
		<div class="row">
			<div class="col s12 m6 l6">
				<h3>Add Wastage</h3>
			</div>
			<div class="col s12 m6 l6">
				<a class="btn" href="#viewWastage" style="float: right; margin-top: 20px;" ng-click="initializeModal()" modal>View Wastage</a>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col s9 menuItemList">
			<select ui-select2 ng-model="selectedProductId"
				data-placeholder="Enter name of product">
				<option value=""></option>
				<option ng-repeat="product in allProducts | orderBy : 'name'"
					value="{{product.id}}">{{product.name}}</option>
			</select>
		</div>
		<div class="col s3 menuItemList">
			<input type="button" class="btn" value="ADD WASTAGE"
				style="margin-top: -4px;"
				data-ng-click="addNewItem(selectedProductId)" />
			<button class="btn btn-medium" data-ng-click="AddBulkWastage()" data-ng-if="userId == 140199 || userId == 125200 || userId == 120063 || userId == 141530">Bulk Wastage ({{availableInventoryProductIds.length}})</button>
		</div>
		<div class="row">
			<div class="col s12">
				<table id="wastageTable" class="bordered striped"
					style="margin-top: -4px;" data-ng-if="wastedItems.length > 0">
					<tr>
						<th>Product Name</th>
						<th>Unit of Measure</th>
						<th>Wasted Quantity</th>
						<th style="width: 125px;">Packagings</th>
						<th>Reason</th>
						<th>Comment</th>
						<th>Action</th>
					</tr>
					<tr data-ng-repeat="roi in wastedItems track by $index">
						<td><a data-ng-click="showPreview($event, roi.product.productId,'PRODUCT')">{{roi.product.productName}}</a></td>
						<td>{{roi.product.unitOfMeasure}}</td>
						<td><input class="input-field waste-value" type="number"
							ng-model="roi.quantity" min="0"  required disabled>
						</td>
						<td>
							<div class="row packaging-div"
								data-ng-repeat="mapping in roi.productPackagings">
								<input class="input-field packaging-qty" type="number"
									ng-model="mapping.packaging.quantity"
									data-ng-change="changeQuantity(roi,mapping,mapping.packaging.quantity)"
									min="0"> <span class="badge">{{mapping.packaging.packagingName}}</span>
								<span class="close pointer"
									data-ng-click="removeMapping(roi,mapping)">&times;</span>
							</div>
							<span class="left" data-ng-if="roi.inventoryCheck !=undefined && roi.inventoryCheck !=null && roi.inventoryCheck == true" style="color: red;">
                                Wastage Quantity Exceeding Available Stock </span>
							<span class="left" data-ng-if="userId == 140199 || userId == 125200 || userId == 120063" style="color: red;">Available Stock : {{productInventoryMap[roi.product.productId]}}</span>
						</td>
						<td style="margin-top: 0px; padding-top: 0px;">` <select
							class="input-field" ng-model="roi.comment"
							style="margin-top: 0px; height: 2rem;">
								<option value="Wasted" selected>Wasted</option>
								<!--<option value = "Logistics waste">Logistics waste</option>-->
								<option data-ng-if="roi.product.shelfLifeInDays != -1" value="Expired">Expired</option>
						</select>
						</td>
						<td>
							<textarea data-ng-model="roi.enteredComment" style="resize: none"></textarea>
							<span data-ng-if="roi.enteredComment.length>0">{{1000-roi.enteredComment.length}} Characters Remaining</span>
						</td>
						<td>
							<button class="btn" data-ng-click="removeItem($index)">REMOVE</button>
						</td>
					</tr>
				</table>
			</div>
		</div>

		<div class="row" style="margin-bottom: 0px;">
			<div class="col s12">
				<input type="button" class="btn right" value="SUBMIT"
					data-ng-if="wastedItems.length > 0"
					data-ng-click="sendWastageItems(wastedItems)" />
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col s12">
			<h3>Todays Wastage</h3>
			<div class="form-element" style="padding-top: 24px;margin-bottom: 0px;" data-ng-hide="todaysWastage.length == 0">
				<input id="isTodayByProduct" data-ng-model="isTodayByProduct"
					   type="checkbox" />
				<label class="black-text"
					   for="isTodayByProduct">Product Wise</label>
			</div>
			<ul class="collapsible no-border" data-collapsible="expandable" watch data-ng-if="todaysWastage.length>0 && !isTodayByProduct">
				<li class = "row margin0" ng-repeat="roi in todaysWastage track by $index">
					<div class="collapsible-header custom-collection-header col s10">
						<div class="row margin0">
							<div class="col s12">
								<div class="col s3">
									<label>{{roi.generationTime | date:'dd/MM/yyyy hh:mm:ss a'}}</label>
								</div>
								<div class="col s2">
									<label>{{roi.items.length}} {{roi.type}}(s)</label>
								</div>
								<div class="col s5" data-ng-if="roi.linkedGrId != null">
									<label>GR Rejection by {{roi.empName}}</label>
								</div>
								<div class="col s5" data-ng-if="roi.linkedKettleId != null">
									<label>Kettle Order Cancellation by {{roi.empName}}</label>
								</div>
								<div class="col s5" data-ng-if="roi.linkedGrId ==null && roi.linkedKettleId == null">
									<label>Booked Manually by {{roi.empName}}</label>
								</div>
								<div class="col s2">
									<label>{{roi.status}}</label>
								</div>
							</div>
						</div>
					</div>
					<div class="col s2" data-ng-if="roi.status == 'SETTLED' && roi.linkedGrId ==null && roi.linkedKettleId == null">
						<button class="btn btn-small" data-ng-click="cancelWastage(roi)" acl-action="AWVWC">Cancel</button>
					</div>
					<div class="collapsible-body">
						<div class="row">
							<table class="bordered striped" data-ng-if="roi.items.length > 0">
								<tr>
									<th>Product</th>
									<th>Unit of Measure</th>
									<th>Quantity</th>
									<th>Price</th>
									<th>Cost</th>
									<th>Tax</th>
									<th>Total Amount</th>
									<th>Reason</th>
									<th>Comment</th>
								</tr>
								<tr data-ng-repeat="item in roi.items track by $index">
									<td><a data-ng-click="showPreview($event, item.product.productId,'PRODUCT')">{{item.product.productName}}</a></td>
									<td>{{item.product.unitOfMeasure}}</td>
									<td>{{item.quantity}}</td>
									<td>{{item.price}}</td>
									<td>{{item.cost}}</td>
									<td>{{item.tax}}</td>
									<td>{{item.totalAmount}}</td>
									<td>{{item.comment}}</td>
									<td data-ng-if="item.enteredComment != null">{{item.enteredComment}}</td>
									<td data-ng-if="item.enteredComment == null">-</td>
								</tr>
							</table>
						</div>
					</div>
				</li>
			</ul>
			<ul class="collapsible no-border" data-collapsible="expandable" watch data-ng-if="groupedTodayProductWastageList.length>0 && isTodayByProduct">
				<li ng-repeat="groupedProduct in groupedTodayProductWastageList track by $index">
					<div class="collapsible-header custom-collection-header">
						<div class="row margin0">
							<div class="col s12">
								<div class="col s2">
									<label>Product Id : {{groupedProduct.productId}}</label>
								</div>
								<div class="col s3">
									<label>Product Name : {{groupedProduct.details.productName}}</label>
								</div>
								<div class="col s1">
									<label>Total : {{groupedProduct.details.total}}</label>
								</div>
								<div class="col s3">
									<label>Total Amount : {{groupedProduct.details.totalAmount | number :2}}</label>
								</div>
								<div class="col s3">
									<label>Total Quantity : {{groupedProduct.details.totalQuantity | number :2}}({{groupedProduct.details.unitOfMeasure}})</label>
								</div>
							</div>
						</div>
					</div>
					<div class="collapsible-body">
						<div class="row">
							<table class="bordered striped" data-ng-if="groupedProduct.details.productWastageList.length > 0">
								<tr>
									<th>Product</th>
									<th>Sub Category</th>
									<th>Unit of Measure</th>
									<th>Quantity</th>
									<th>Price</th>
									<th>Cost</th>
									<th>Tax</th>
									<th>Total Amount</th>
									<th>Reason</th>
									<th>Comment</th>
								</tr>
								<tr data-ng-repeat="item in groupedProduct.details.productWastageList track by $index">
									<td>{{item.product.productName}}</td>
									<td>{{item.subCategory}}</td>
									<td>{{item.product.unitOfMeasure}}</td>
									<td>{{item.quantity}}</td>
									<td>{{item.price}}</td>
									<td>{{item.cost}}</td>
									<td>{{item.tax}}</td>
									<td>{{item.totalAmount}}</td>
									<td>{{item.comment}}</td>
									<td data-ng-if="item.enteredComment != null">{{item.enteredComment}}</td>
									<td data-ng-if="item.enteredComment == null">-</td>
								</tr>
							</table>
						</div>
					</div>
				</li>
			</ul>
			<p data-ng-if="todaysWastage.length==0">No wastage for today.</p>
		</div>
	</div>
</div>

<div id="viewWastage" class="modal modal-mx-no-transform" style="bottom: 1% !important;width: 90%">
	<div class="modal-content" style="min-height: 89%;">
			<div class="row pull-right">
				<button class="modal-action modal-close waves-effect waves-green btn-flat" data-ng-click="closeWastageView()">&times;</button>
			</div>
			<div class="row col s12">
				<div class="col s3">
					<label>Start date*</label>
					<input input-date type="text" name="startOfWaste" id="startWaste" ng-model="wastageStartDate"
						   container="" format="yyyy-mm-dd" max="{{maxWastageDate}}" data-ng-change="setStartDate(wastageStartDate)"/>
				</div>
				<div class="col s3">
					<label>End date*</label>
					<input input-date type="text" name="endOfWaste" id="endWaste" ng-model="wasatgeEndDate" container="" format="yyyy-mm-dd" min="{{wastageStartDate}}" max="{{maxWastageDate}}"
						   data-ng-change="setEndDate(wasatgeEndDate)"/>
				</div>
				<div class="col s2">
					<div class="form-element" style="padding-top: 24px;margin-bottom: 0px;">
						<input id="isByProduct" data-ng-model="isByProduct"
							   type="checkbox" />
						<label class="black-text"
							   for="isByProduct">Product Wise</label>
					</div>
				</div>
				<div class="col s2">
					<button class="btn btn-medium margin-top-10" ng-click="getWastageEventsForDays()">Get Events</button>
				</div>
				<div class="col s2" data-ng-if="wastageEvents.length > 0">
					<button class="btn btn-medium margin-top-10" ng-click="generateWastageSheet()">Generate Sheet</button>
				</div>
			</div>
			<div class="row" data-ng-if="!isByProduct">
			<div class="col s12">
			<ul class="collapsible no-border" data-collapsible="expandable" watch data-ng-if="wastageEvents.length>0">
				<li ng-repeat="roi in wastageEvents track by $index">
					<div class="collapsible-header custom-collection-header">
						<div class="row margin0">
							<div class="col s12">
								<div class="col s4">
									<label>{{roi.generationTime | date:'dd/MM/yyyy hh:mm:ss'}}</label>
								</div>
								<div class="col s4">
									<label>{{roi.status}}</label>
								</div>
								<div class="col s2">
									<label>{{roi.items.length}}</label>
								</div>
								<div class="col s2" data-ng-if="roi.linkedGrId != null">
									<label>GR Reject</label>
								</div>
								<div class="col s2" data-ng-if="roi.linkedKettleId != null">
									<label>Kettle Order Cancellation</label>
								</div>
								<div class="col s2" data-ng-if="roi.linkedGrId ==null && roi.linkedKettleId == null">
									<label>Booked Manually</label>
								</div>
							</div>
						</div>
					</div>
					<div class="collapsible-body">
						<div class="row">
							<table class="bordered striped" data-ng-if="roi.items.length > 0">
								<tr>
									<th>Product</th>
									<th>Unit of Measure</th>
									<th>Quantity</th>
									<th>Price</th>
									<th>Cost</th>
									<th>Tax</th>
									<th>Total Amount</th>
									<th>Reason</th>
									<th>Comment</th>
								</tr>
								<tr data-ng-repeat="item in roi.items track by $index">
									<td><a data-ng-click="showPreview($event, item.product.productId,'PRODUCT')">{{item.product.productName}}</a></td>
									<td>{{item.product.unitOfMeasure}}</td>
									<td>{{item.quantity}}</td>
									<td>{{item.price}}</td>
									<td>{{item.cost}}</td>
									<td>{{item.tax}}</td>
									<td>{{item.totalAmount}}</td>
									<td>{{item.comment}}</td>
									<td data-ng-if="item.enteredComment != null">{{item.enteredComment}}</td>
									<td data-ng-if="item.enteredComment == null">-</td>
								</tr>
							</table>
						</div>
					</div>
				</li>
			</ul>
		</div>
		</div>
		<div class="row" data-ng-if="isByProduct">
			<div class="col s12">
				<ul class="collapsible no-border" data-collapsible="expandable" watch data-ng-if="groupedProductWastageList.length>0">
					<li ng-repeat="groupedProduct in groupedProductWastageList track by $index">
						<div class="collapsible-header custom-collection-header">
							<div class="row margin0">
								<div class="col s12">
									<div class="col s2">
										<label>Product Id : {{groupedProduct.productId}}</label>
									</div>
									<div class="col s3">
										<label>Product Name : {{groupedProduct.details.productName}}</label>
									</div>
									<div class="col s1">
										<label>Total : {{groupedProduct.details.total}}</label>
									</div>
									<div class="col s3">
										<label>Total Amount : {{groupedProduct.details.totalAmount | number :2}}</label>
									</div>
									<div class="col s3">
										<label>Total Quantity : {{groupedProduct.details.totalQuantity | number :2}}({{groupedProduct.details.unitOfMeasure}})</label>
									</div>
								</div>
							</div>
						</div>
						<div class="collapsible-body">
							<div class="row">
								<table class="bordered striped" data-ng-if="groupedProduct.details.productWastageList.length > 0">
									<tr>
										<th>Product</th>
										<th>Sub Category</th>
										<th>Unit of Measure</th>
										<th>Quantity</th>
										<th>Price</th>
										<th>Cost</th>
										<th>Tax</th>
										<th>Total Amount</th>
										<th>Reason</th>
										<th>Comment</th>
									</tr>
									<tr data-ng-repeat="item in groupedProduct.details.productWastageList track by $index">
										<td>{{item.product.productName}}</td>
										<td>{{item.subCategory}}</td>
										<td>{{item.product.unitOfMeasure}}</td>
										<td>{{item.quantity}}</td>
										<td>{{item.price}}</td>
										<td>{{item.cost}}</td>
										<td>{{item.tax}}</td>
										<td>{{item.totalAmount}}</td>
										<td>{{item.comment}}</td>
										<td data-ng-if="item.enteredComment != null">{{item.enteredComment}}</td>
										<td data-ng-if="item.enteredComment == null">-</td>
									</tr>
								</table>
							</div>
						</div>
					</li>
				</ul>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button
			class="modal-action modal-close waves-effect waves-green btn-flat">Close</button>
	</div>
</div>