<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .custom-listing-li {
        margin-bottom: 20px;
    }
    .highlight-row {
        background-color: #e8f5e8;
        font-weight: bold;
    }
    .highlight-data {
        color: #2e7d32;
        font-weight: bold;
    }
    .orderingColumnHighlight {
        background-color: #fff3e0;
        font-weight: bold;
    }
    .collection-item.list-head {
        background-color: #f5f5f5;
        font-weight: bold;
    }
    .btn-input-group {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px; /* spacing between elements */
    }
    .btn-input-group .btn {
        padding: 2px 6px;
        font-size: 12px;
        line-height: 1;
        height: 28px;
        min-width: 28px;
        border-radius: 4px;
    }
    .btn-input-group .editable-input {
        width: 60px;
        height: 32px;
        text-align: center;
        font-size: 13px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    .editable-input {
        width: 80px;
        text-align: center;
        border: 1px solid #ddd;
        padding: 2px;
    }
    .date-column {
        min-width: 100px;
        text-align: center;
    }
    .product-column {
        min-width: 200px;
        max-width: 200px;
        white-space: normal;
        word-break: break-word;
        overflow-wrap: break-word;
        line-height: 1.2;
    }
    .non-editable-value {
        display: inline-block;
        padding: 4px 8px;
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 3px;
        color: #666;
        font-weight: bold;
        min-width: 60px;
        text-align: center;
    }
    .kitchen-header {
        background-color: #4CAF50;
        margin: 20px 0 10px 0;
        padding: 10px 15px;
        border-radius: 5px;
        font-weight: bold;
        color: white;
    }
    .warehouse-header {
        background-color: #2196F3;
        margin: 20px 0 10px 0;
        padding: 10px 15px;
        border-radius: 5px;
        font-weight: bold;
        color: white;
    }
    .specializedRoProducts-header{
        background-color: #f44336;
        margin: 20px 0 10px 0;
        padding: 10px 15px;
        border-radius: 5px;
        font-weight: bold;
        color: white;
    }
    .packaging-column {
        background-color: #fff3e0;
        font-weight: bold;
    }
</style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4 class="left">Suggestive Ordering 2.0</h4>
                <div class="right-align" style="margin-top: 20px;">
                </div>
            </div>
        </div>
    </div>

    <!-- Brand, Date, Days Selection -->
    <div class="row">
        <div class="col s12" data-ng-show="!showProductsList && !showScmPackagingListDateWise">
            <div class="form-element">
                    <div class="form-element">
                        <div class="row">
                            <div class="col s2 ">
                                <label>Select Brand</label>
                            </div>
                            <div class="col s4">
                                <select data-ng-model="selectedBrandDetails" class='form-control'
                                        data-ng-options="brand as brand.brandName for brand in brandDetails"
                                        data-ng-change="setBrand(selectedBrandDetails)">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col s2"><label>Fulfillment Date</label></div>
                        <div class="col s4"><input input-date type="text" name="fulfillmentDate"
                                   ng-model="fulfillmentDate"
                                   container="" format="yyyy-mm-dd" select-years="1"
                                   min="{{minRefOrderFulfillmentDate}}"
                                   max="{{maxRefOrderFulfillmentDate}}"
                                   data-ng-change="setDates(fulfillmentDate, noOfDays)"/>
                        </div>
                        <div class="col s6"><label class="highlight-data">{{fulfillmentDay}}</label></div>
                    </div>
                    <div class="form-element">
                        <div class="row">
                            <div class="col s2"><label>Ordering Days</label></div>
                            <div class="col s4"><input type="number" min="1" placeholder="No Of Days"
                                                       name="noOfDays"
                                                       id="noOfDays" ng-model="noOfDays"
                                                       ng-disabled="isApiInProgress"
                                                       data-ng-change="setDates(fulfillmentDate, noOfDays)"/></div>
                        </div>
                    </div>
                    <div class="row">
                        <input type="button" class="btn" value="GUIDE ME" data-ng-disabled="isFulfillmentInputInvalidOrLoading()" data-ng-click="getMenuProductsConsumption()"/>
                    </div>
            </div>
        </div>

            <!-- Days Selection Table -->
            <div class="form-element" data-ng-show="dataEntry.length > 0 && !showProductsList && !showScmPackagingListDateWise">
                <div class="row">
                    <label class="highlight-row" style="margin-top: 20px">Ordering Configuration for Days</label>
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr class="collection-item list-head">
                                <th>Day Type</th>
                                <th>Date</th>
                                <th>Ordering Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="entry in dataEntry"
                                data-ng-class="{'red':entry.dayType=='REMAINING_DAY','green':entry.dayType=='ORDERING_DAY'}">
                                <td>{{entry.dayType}}</td>
                                <td>{{entry.date}}</td>
                                <td>
                                    <select data-ng-model="entry.orderingPercentage"
                                            data-ng-disabled="entry.dayType=='REMAINING_DAY'"
                                            data-ng-options="orderPercentage as orderPercentage for orderPercentage in orderingPercentages">
                                    </select>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
</div>

    <!-- Products Consumption Table -->
    <div class="row" data-ng-show="showProductsList">
        <div class="col s12">
            <div class="form-element">
                <h5>Menu Products Consumption Data</h5>

                <!-- Search Box -->
                <div class="row" style="margin-bottom: 20px;">
                    <div class="col s12 m6 l4">
                        <div class="input-field">
                            <i class="material-icons prefix">search</i>
                            <input type="text" id="productSearch" data-ng-model="searchText" data-ng-change="filterProducts()" placeholder="Search products...">
                            <label for="productSearch"></label>
                        </div>
                    </div>
                    <div class="col s12 m6 l8">
                        <p class="grey-text">
                            <span data-ng-if="searchText && filteredMenuProducts.length > 0">
                                Showing {{filteredMenuProducts.length}} products matching "{{searchText}}"
                            </span>
                            <span data-ng-if="searchText && filteredMenuProducts.length === 0">
                                No products found matching "{{searchText}}"
                            </span>
                            <span data-ng-if="!searchText">
                                Total {{filteredMenuProducts.length}} products loaded
                            </span>
                        </p>
                    </div>
                </div>

                <!-- Products Table - Simple List -->
                <div style="overflow-x: auto;">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr class="collection-item list-head">
                                <th class="product-column">Product (Dimension)</th>
                                <th class="date-column">Safety Stock</th>
                                <th data-ng-repeat="date in ::remainingDays" class="date-column">
                                    {{::date | date:'dd-MM'}} (Rem)
                                </th>
                                <th data-ng-repeat="date in ::orderingDays" class="date-column">
                                    {{::date | date:'dd-MM'}} (Ord)
                                </th>
                                <th class="date-column">Final Quantity</th>
                            </tr>
                        </thead>
                    </table>

                    <!-- Simple Scrollable Table - All Products Loaded -->
                    <div style="height: 500px; overflow-y: auto; border: 1px solid #ddd;">
                        <table class="table table-striped table-bordered" style="margin: 0;">
                            <tbody>
                                <tr data-ng-repeat="product in filteredMenuProducts track by product.productName">
                                <td class="product-column">
                                    <strong>{{::product.productName}}</strong><br>
                                    <small>({{::product.dimension}})</small>
                                </td>

                                <!-- Safety Stock Column -->
                                <td class="date-column">
                                    <span class="non-editable-value">{{::product.safetyStockQuantity | number:2}}</span>
                                </td>

                                <!-- Remaining Days Columns -->
                                <td data-ng-repeat="date in ::remainingDays" class="date-column">
                                    <span class="non-editable-value">{{::safeInteger(product.remainingDaysData[date])}}</span>
                                </td>

                                <!-- Ordering Days Columns -->
                                <td data-ng-repeat="date in ::orderingDays" class="date-column orderingColumnHighlight">
                                    <div class="btn-input-group" data-ng-if="product.isEditable">
                                    <button type="button"
                                            class="btn btn-sm btn-outline-secondary"
                                            ng-click="decreaseQuantity(product, date)">
                                        -
                                    </button>

                                    <input type="text"
                                           inputmode="numeric"
                                           pattern="^\d+$"
                                           class="editable-input"
                                           data-ng-model="product.orderingDaysData[date].quantity"
                                           ng-init="product.orderingDaysData[date].quantity = product.orderingDaysData[date].quantity || 0"
                                           ng-blur="onBlurSetZero(product, date)"
                                           data-ng-change="debouncedValidateQuantity(product, date);  calculateFinalQuantity(product)"
                                           step="1" min="0" data-ng-keypress="preventDecimal($event)"/>

                                    <button type="button"
                                            class="btn btn-sm btn-outline-secondary"
                                            ng-click="increaseQuantity(product, date)">
                                        +
                                    </button>
                                    </div>
                                    <span class="non-editable-value" data-ng-if="!product.isEditable">{{product.orderingDaysData[date].quantity || 0}}</span>
                                </td>

                                <!-- Final Quantity -->
                                <td class="date-column highlight-data">
                                    <strong>{{product.finalQuantity | number:2}}</strong>
                                </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="row" style="margin-top: 20px;">
                    <div class="col s12">
                        <input type="button" class="btn" value="BACK"
                               data-ng-click="goBackToSelection()" style="margin-right: 10px;"/>
                        <input type="button" class="btn purple" value="NEXT - GET SCM PRODUCTS (DATE-WISE)"
                               data-ng-click="fetchAllScmData()" style="margin-left: 10px;"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Date-wise SCM Packaging Products Section -->
    <div class="row" data-ng-show="showScmPackagingListDateWise">
        <div class="col s12">
            <div class="form-element">
                <h5>SCM Products Consumption (Categorized)</h5>
                <p class="highlight-data">Kitchen & Warehouse Products with Packaging Information</p>

                <!-- Search Box for SCM Products -->
                <div class="row" style="margin-bottom: 20px;">
                    <div class="col s12 m6 l4">
                        <div class="input-field">
                            <i class="material-icons prefix">search</i>
                            <input type="text" id="scmProductSearch" data-ng-model="scmSearchText" data-ng-change="filterScmProducts()" placeholder="Search SCM products...">
                            <label for="scmProductSearch"></label>
                        </div>
                    </div>
                    <div class="col s12 m6 l8">
                        <p class="grey-text">
                            <span data-ng-if="scmSearchText && (filteredKitchenProductsDateWise.length > 0 || filteredWarehouseProductsDateWise.length > 0)">
                                Showing {{filteredKitchenProductsDateWise.length + filteredWarehouseProductsDateWise.length}} products matching "{{scmSearchText}}"
                                <br><small>Kitchen: {{filteredKitchenProductsDateWise.length}}, Warehouse: {{filteredWarehouseProductsDateWise.length}}</small>
                            </span>
                            <span data-ng-if="scmSearchText && filteredKitchenProductsDateWise.length === 0 && filteredWarehouseProductsDateWise.length === 0">
                                No products found matching "{{scmSearchText}}"
                            </span>
                            <span data-ng-if="!scmSearchText">
                                Total Kitchen: {{filteredKitchenProductsDateWise.length}}, Warehouse: {{filteredWarehouseProductsDateWise.length}} products loaded
                            </span>
                        </p>
                    </div>
                </div>

                <!-- SCM Packaging Table (Categorized by Kitchen/Warehouse) -->
                <div style="overflow-x: auto;">
                    <!-- Kitchen Products Section -->
                    <div data-ng-if="filteredKitchenProductsDateWise.length > 0">
                        <h6 class="category-header kitchen-header">
                            🍳 KITCHEN PRODUCTS ({{filteredKitchenProductsDateWise.length}} items)
                        </h6>

                        <table class="table table-striped table-bordered category-table">
                            <thead>
                                <tr class="collection-item list-head">
                                    <th class="product-column">Product Name</th>
                                    <th>UOM</th>
                                    <th>In Stock</th>
                                    <th>In Transit</th>
                                    <th>Ack RO</th>
                                    <th data-ng-repeat="date in ::remainingDays" class="date-column">
                                        {{::date | date:'dd-MM'}} (Rem)
                                    </th>
                                    <th data-ng-repeat="date in ::orderingDays" class="date-column">
                                        {{::date | date:'dd-MM'}} (Ord)
                                    </th>
                                    <th class="date-column">Final Quantity</th>
                                    <th class="date-column">Ordering Quantity</th>
                                    <th class="date-column">Packaging</th>
                                    <th class="date-column">Expiry Usage</th>
                                </tr>
                            </thead>
                        </table>

                        <!-- Kitchen Products - All Loaded -->
                        <div style="height: 400px; overflow-y: auto; border: 1px solid #ddd;">
                            <table class="table table-striped table-bordered category-table" style="margin: 0;">
                                <tbody>
                                    <tr data-ng-repeat="product in filteredKitchenProductsDateWise track by product.productName">
                                    <td class="product-column">
                                        <strong>{{::product.productName}}</strong>
                                    </td>
                                    <td>{{::product.uom}}</td>

                                    <td>{{::product.stockAtHand.totalStock || '0'}}</td>
                                    <td>{{::product.inTransit.totalStock || '0'}}</td>
                                    <td>
                                        <input type="button" class="btn btn-small" value="RO ({{acknowledgedStockQuantityMap[product.productId] || '0'}})" data-ng-click="showAcknowledgedRoStock(product)" data-target='viewAcknowledgedRoStockModal' modal/>
                                    </td>

                                    <!-- Remaining Days Columns (Non-editable) -->
                                    <td data-ng-repeat="date in remainingDays" class="date-column">
                                        <span class="non-editable-value">{{safeInteger(product.remainingDaysData[date]) | number:6}}</span>
                                    </td>

                                    <!-- Ordering Days Columns (editable) -->
                                    <td data-ng-repeat="date in orderingDays" class="date-column orderingColumnHighlight">
                                        <input type="text"
                                               inputmode="numeric"
                                               pattern="^\d+(\.\d{0,6})?$" placeholder="0.000"
                                               class="editable-input"
                                               data-ng-model="product.orderingDaysData[date]"
                                               data-ng-change="calculateScmFinalQuantityDateWise(product)"
                                               step="0.01" min="0" data-ng-keypress="preventInvalidDecimal($event)"/>
                                    </td>

                                    <!-- Final Quantity (Rounded) -->
                                    <td class="date-column highlight-data">
                                        <strong>{{product.finalQuantity | number:4}}</strong>
                                    </td>
                                    <!-- Ordering Quantity (Rounded) -->
                                    <td class="date-column highlight-data">
                                        <strong>{{product.orderingQuantity | number:4}}</strong>
                                    </td>
                                    <!-- Dynamic Packaging Column -->
                                    <td class="date-column highlight-data packaging-column">
                                        <strong>{{product.packagingQuantity}} {{product.packagingUnit || 'packets'}}</strong>
                                        <br><small>({{product.packagingName}})</small>
                                    </td>
                                    <td class="col s1">
                                        <input type="button" class="btn" value="Exp* Usage" data-ng-click="showExpiryUsage(product)" data-target='viewProductFulfilmentLogsModal' modal/>
                                    </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <br>
                    </div>

                    <div class="row" style="margin-bottom: 20px;">
                        <div class="col xs12">
                            <div class="switch">
                                <label>
                                    Hide Warehouse Products
                                    <input type="checkbox" data-ng-model="showWarehouseProducts" data-ng-change="toggleWarehouseProducts()">
                                    <span class="lever"></span>
                                    Show
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Warehouse Products Section -->
                    <div data-ng-if="filteredWarehouseProductsDateWise.length > 0 && showWarehouseProducts">
                        <h6 class="category-header warehouse-header">
                            🏢 WAREHOUSE PRODUCTS ({{filteredWarehouseProductsDateWise.length}} items)
                        </h6>

                        <table class="table table-striped table-bordered category-table">
                            <thead>
                                <tr class="collection-item list-head">
                                    <th class="product-column">Product Name</th>
                                    <th>UOM</th>
                                    <th>In Stock</th>
                                    <th>In Transit</th>
                                    <th>Ack RO</th>
                                    <th data-ng-repeat="date in ::remainingDays" class="date-column">
                                        {{::date | date:'dd-MM'}} (Rem)
                                    </th>
                                    <th data-ng-repeat="date in ::orderingDays" class="date-column">
                                        {{::date | date:'dd-MM'}} (Ord)
                                    </th>
                                    <th class="date-column">Final Quantity</th>
                                    <th class="date-column">Ordering Quantity</th>
                                    <th class="date-column">Packaging</th>
                                    <th class="date-column">Expiry Usage</th>
                                </tr>
                            </thead>
                        </table>

                        <!-- Warehouse Products - All Loaded -->
                        <div style="height: 400px; overflow-y: auto; border: 1px solid #ddd;">
                            <table class="table table-striped table-bordered category-table" style="margin: 0;">
                                <tbody>
                                    <tr data-ng-repeat="product in filteredWarehouseProductsDateWise track by product.productName">
                                    <td class="product-column">
                                        <strong>{{::product.productName}}</strong>
                                    </td>
                                    <td>{{::product.uom}}</td>

                                    <td>{{::product.stockAtHand.totalStock || '0'}}</td>
                                    <td>{{::product.inTransit.totalStock || '0'}}</td>
                                    <td>
                                        <input type="button" class="btn btn-small" value="RO ({{acknowledgedStockQuantityMap[product.productId] || '0'}})" data-ng-click="showAcknowledgedRoStock(product)" data-target='viewAcknowledgedRoStockModal' modal/>
                                    </td>

                                    <!-- Remaining Days Columns (Non-editable) -->
                                    <td data-ng-repeat="date in remainingDays" class="date-column">
                                        <span class="non-editable-value">{{safeInteger(product.remainingDaysData[date]) | number:6}}</span>
                                    </td>

                                    <!-- Ordering Days Columns (editable) -->
                                    <td data-ng-repeat="date in orderingDays" class="date-column orderingColumnHighlight">
                                        <input type="text"
                                               inputmode="numeric" placeholder="0.000"
                                               pattern="^\d+(\.\d{0,6})?$"
                                               class="editable-input"
                                               data-ng-model="product.orderingDaysData[date]"
                                               data-ng-change="calculateScmFinalQuantityDateWise(product)"
                                               step="0.01" min="0" data-ng-keypress="preventInvalidDecimal($event)"/>
                                    </td>

                                    <!-- Final Quantity (Rounded) -->
                                    <td class="date-column highlight-data">
                                        <strong>{{product.finalQuantity | number:4}}</strong>
                                    </td>

                                    <!-- Ordering Quantity (Rounded) -->
                                    <td class="date-column highlight-data">
                                        <strong>{{product.orderingQuantity | number:4}}</strong>
                                    </td>

                                    <!-- Dynamic Packaging Column -->
                                    <td class="date-column highlight-data packaging-column">
                                        <strong>{{product.packagingQuantity}} {{product.packagingUnit || 'packets'}}</strong>
                                        <br><small>({{product.packagingName}})</small>
                                    </td>
                                    <td class="col s1">
                                        <input type="button" class="btn" value="Exp* Usage" data-ng-click="showExpiryUsage(product)" data-target='viewProductFulfilmentLogsModal' modal/>
                                    </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <br>
                    </div>

                    <div class="row" style="margin-bottom: 20px;">
                        <div class="col xs12">
                            <div class="switch">
                                <label>
                                    Hide Specialized RO Products
                                    <input type="checkbox" data-ng-model="showSpecializedRoProducts" data-ng-change="toggleSpecializedRoProducts()">
                                    <span class="lever"></span>
                                    Show
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- RO Products Section -->
                    <div data-ng-if="specializedRoProducts.length > 0 && showSpecializedRoProducts">
                        <h6 class="category-header specializedRoProducts-header">
                            Specialized RO PRODUCTS ({{specializedRoProducts.length}} items)
                        </h6>

                        <table class="table table-striped table-bordered category-table">
                            <thead>
                            <tr class="collection-item list-head">
                                <th class="product-column">Product Name</th>
                                <th>UOM</th>
                                <th>In Stock</th>
                                <th>In Transit</th>
                                <th>Ack RO</th>
                                <th data-ng-repeat="date in remainingDays" class="date-column">
                                    {{date | date:'dd-MM'}} (Rem)
                                </th>
                                <th data-ng-repeat="date in orderingDays" class="date-column">
                                    {{date | date:'dd-MM'}} (Ord)
                                </th>
                                <th class="date-column">Final Quantity</th>
                                <th class="date-column">Ordering Quantity</th>
                                <th class="date-column">Packaging</th>
                                <th class="date-column">Expiry Usage</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="product in specializedRoProducts | orderBy:'productName' track by product.productName">
                                <td class="product-column">
                                    <strong>{{::product.productName}}</strong>
                                </td>
                                <td>{{::product.uom}}</td>

                                <td>{{::product.stockAtHand.totalStock || '0'}}</td>
                                <td>{{::product.inTransit.totalStock || '0'}}</td>
                                <td>
                                    <input type="button" class="btn btn-small" value="RO ({{acknowledgedStockQuantityMap[product.productId] || '0'}})" data-ng-click="showAcknowledgedRoStock(product)" data-target='viewAcknowledgedRoStockModal' modal/>
                                </td>

                                <!-- Remaining Days Columns (Non-editable) -->
                                <td data-ng-repeat="date in remainingDays" class="date-column">
                                    <span class="non-editable-value">{{product.remainingDaysData[date] | number:0}}</span>
                                </td>

                                <!-- Ordering Days Columns (Non-editable) -->
                                <td data-ng-repeat="date in orderingDays" class="date-column">
                                    <span class="non-editable-value">{{product.orderingDaysData[date] | number:0}}</span>
                                </td>

                                <!-- Final Quantity (Rounded) -->
                                <td class="date-column highlight-data">
                                    <strong>{{product.finalQuantity | number:4}}</strong>
                                </td>

                                <!-- Ordering Quantity (Rounded) -->
                                <td class="date-column highlight-data">
                                    <strong>{{product.orderingQuantity | number:4}}</strong>
                                </td>

                                <!-- Dynamic Packaging Column -->
                                <td class="date-column highlight-data packaging-column">
                                    <strong>{{product.packagingQuantity}} {{product.packagingUnit || 'packets'}}</strong>
                                    <br><small>({{product.packagingName}})</small>
                                </td>
                                <td class="col s1">
                                    <input type="button" class="btn" value="Exp* Usage" data-ng-click="showExpiryUsage(product)" data-target='viewProductFulfilmentLogsModal' modal/>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <br>
                    </div>

                <!-- Comment Section (Like Suggestive Ordering) -->
                    <div class="row">
                        <div class="col s12 m6 l6">
                            <div class="form-element">
                                <label>Comment(optional)</label>
                                <textarea data-ng-model="comment" data-ng-change="saveComment(comment);" ></textarea>
                            </div>
                        </div>
                    </div>

                <!-- SCM Packaging Action Buttons -->
                <div class="row" style="margin-top: 20px;">
                    <div class="col s12">
                        <input type="button" class="btn" value="BACK TO MENU PRODUCTS"
                               data-ng-click="goBackToMenuProductsDateWise()" style="margin-right: 10px;"/>
                        <input type="button" class="btn" value="SUBMIT"
                               data-ng-click="sendReferenceOrder('CREATED')"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="viewProductFulfilmentLogsModal" class="modal">
        <div class="modal-content">
            <h4>Expiry Usage Of {{selectedProductForLogs.productName}}</h4>
            <div class="row">
                <div class="col s12">
                    <ul>
                        <li data-ng-repeat="log in selectedProductLogs track by log"
                            style="background: #a5e9fd;border:1px solid #8fd5f7;padding:5px;margin: 5px 0;">
                            {{log}}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-small modal-action modal-close">Close</button>
        </div>
    </div>

    <div id="viewAcknowledgedRoStockModal" class="modal">
        <div class="modal-content">
            <h4>Acknowledge RO Quantity Of {{selectedProductForAcknowledgedRo.productName}}</h4>
            <div class="row" data-ng-repeat="fulfilmentDate in OrderingDaysFinal track by fulfilmentDate">
                <div class="col s12" style="background: #a5e9fd;border:1px solid #8fd5f7;padding:5px;margin: 5px 0;">
                    Fulfilment Date : {{fulfilmentDate}}
                </div>
                <div class="col s12">
                    <table class="table table-striped table-bordered">
                        <thead style="background-color: #e7e7e7">
                        <th data-ng-repeat="innerFulfilmentDate in getArray(OrderingDaysFinal,$index)">{{innerFulfilmentDate}}</th>
                        </thead>
                        <tbody>
                        <tr>
                            <td data-ng-repeat="innerFulfilmentDate in getArray(OrderingDaysFinal,$index)">
                                <input type="text" style="color: red" data-ng-disabled="true" value="{{acknowledgedStockMap[selectedProductForAcknowledgedRo.productId  + '_' + fulfilmentDate][innerFulfilmentDate] || '-'}}"/>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <hr>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-small modal-action modal-close">Close</button>
        </div>
    </div>

</div>