angular.module("vendorApp").controller('vendorContractSoCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
                  $alertService, $location, authService, $state){

                    $scope.init = function () {
                        $scope.clientInfo = null;
                        $.getJSON('https://api.db-ip.com/v2/free/self', function(data) {
                          $scope.clientInfo = JSON.stringify(data, null, 2);
                        }).then(function(response) {
                          $scope.clientInfo = JSON.parse($scope.clientInfo);
                          $scope.contractInfo = null;
                          $scope.vendor = null;
                          $scope.enteredOTP = null;
                          $scope.thankYou = false;
                          $scope.isValidate= false;
                          $scope.name="";
                          $scope.designation="";
                          $scope.isChecked = false;
                          getAuth();
                        });
          
                      };
          

                    function validateRequest(id) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.vendorManagement.validateSOContractRequest,
                            data: {token: id}
                        }).then(function (response) {
                            $scope.contractInfo = response.data;
                            getVendorDetail($scope.contractInfo.vendorId);
                            console.log($scope.contractInfo);
                            getUrl($scope.contractInfo.unsignedDocumentId);
                        }, function (response) {
                            console.log("Not a valid request");
                            $state.go("vendorError");
                        });
                    }
        
                    function getUrl(id) {
                        $http({
                            method: "GET",
                            url: apiJson.urls.vendorManagement.getUrl+"?documentId="+id
                            }).then(function (response) {
                            $scope.getUnsignedDocumentUrl =  response.data;
                            if (document.getElementById("docPdf")!=null) {
                                document.getElementById("docPdf").data = $scope.getUnsignedDocumentUrl;//+"#toolbar=0"
                            }
                        }, function (response) {
                            console.log("Not a valid request");
                            $state.go("vendorError");
                        });
                    }
        
                    function getAuth() {
                        $http({
                            method: "GET",
                            url: apiJson.urls.vendorManagement.getAuth
                        }).then(function (response) {
                            authService.setAuthorization(response.data);
                            var search = $location.search();
                            if (!appUtil.isEmptyObject(search) && !appUtil.isEmptyObject(search.id)) {
                                validateRequest(encodeURIComponent(search.id));
                            }
                        });
                    }
        
                    function getVendorDetail(vendorId) {
                        $http({
                            method: "GET",
                            url: apiJson.urls.vendorManagement.getVendor + "/" + vendorId
                        }).then(function (response) {
                            $scope.vendor = response.data;
                            console.log($scope.vendor);
                        });
                    }

             $scope.approveSO =  function(isApproved){
                
                if(isApproved===true){
                if($scope.isValidate=== false){
                    alert("Please click on 'I agree with terms and conditions' to accept the contract");
                    return;
                }

                if($scope.name===null || $scope.name.length <= 0) {
                    alert("Please Enter Name to accept the contract'");
                    return;
                }

                if($scope.designation === null || $scope.designation.length <= 0){
                    alert("Please Enter Desgination to accept the contract'");
                    return;
                }
            }

                var payload = {
                    vendorIp : $scope.clientInfo.ipAddress,
                    vendorLoc : $scope.clientInfo.city,
                    vendorContractSoId: $scope.contractInfo.vendorContractId,
                    isApproved : isApproved,
                    vendorName: $scope.name,
                    vendorDesignation : $scope.designation, 
                }



                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.vendorApprovalSo,
                    data : payload
                    }).then(function (response) {
                      if(response.data==true){
                        $scope.thankYou = true;
                      }else{
                        console.log("Something bad occur!");
                       $state.go("vendorError");
                      }
                }, function (response) {
                    console.log("Not a valid request");
                    $state.go("vendorError");
                });


               }

               $scope.setName = function (val) {
                $scope.name = val;
            }
            $scope.setDesignation = function (val) {
                $scope.designation = val;
            }
            $scope.validContract = function (val) {
                $scope.isValidate = val;
                $scope.isChecked = val;
            }

                  }]);