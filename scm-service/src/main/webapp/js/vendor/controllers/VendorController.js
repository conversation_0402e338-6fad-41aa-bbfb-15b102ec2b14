scmApp
.controller('vendorCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
            $alertService, $location, authService, $state) {

            function getVendorDetail(vendorId) {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getVendor + "/" + vendorId
                }).then(function (response) {
                    $scope.getChangesRequestedData(response.data.vendorId);
                    $scope.basicDetail = response.data;
                });
            }

            $scope.getChangesRequestedData = function (vendorId) {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getRequestedChanges,
                    params: {
                        vendorId: vendorId
                    }
                }).then(function (response) {
                    $scope.requestedChangesData = response.data;
                    $scope.detailPersonal = {};

                    response.data.map(function (val) {
                        if (val.tableName === "PERSONAL_DETAIL") {
                            $scope.detailPersonal[val.fieldName] = val;

                        }
                    })
                    $scope.showRequestedChangesLabel = false;
                    if (Object.keys($scope.detailPersonal).length > 0) {
                        $scope.showRequestedChangesLabel = true;
                    }
                });
            }

            $scope.checkForAlreadySubmitted = function () {
                var search = $location.search();
                if (appUtil.isEmptyObject($scope.basicDetail.vendorId)) {
                    if (!appUtil.isEmptyObject(search) && !appUtil.isEmptyObject(search.id)) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.vendorManagement.validateRequest,
                            data: { token: search.id }
                        }).then(function (response) {
                            console.log("response : ", response.data);
                            if (appUtil.isEmptyObject(response.data.vendorId)) {
                                $scope.saveBasicDetails();
                            } else {
                                $alertService.alert("Already Submitted !!", "Details Are Already Filled In Using This Link . Please Refresh The Page", function () { }, false);
                            }
                        });
                    }
                } else {
                    $scope.saveBasicDetails();
                }


            }

            function validateRequest(id) {
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.validateRequest,
                    data: { token: id }
                }).then(function (response) {
                    var result = response.data;
                    $scope.registrationRequest = result;
                    if (result.vendorId != null && result.vendorId > 0 && (result.requestStatus == "INITIATED" || result.requestStatus == "IN_PROCESS")) {
                        getVendorDetail(result.vendorId);
                        checkIfActivated(result.vendorId);
                    }
                }, function (response) {
                    console.log("Not a valid request");
                    $state.go("vendorError");
                });
            }

            function checkIfActivated(vendorId) {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.checkForVendorApprovedAtleastOnce,
                    params: {
                        vendorId: vendorId
                    }
                }).then(function (response) {
                    var result = response.data;
                    if (result) {
                        $scope.isDisabled = true;
                    }
                }, function (response) {
                    console.log("error while finding vendor logs");
                });
            }

            function validateBasicDetails() {
                var flag = validateForm($scope.basicDetailForm.$name);
                return flag;
            }


            $scope.duplicateNameCheck = function (callback) {

                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.checkDuplicateVendorName,
                    params: {
                        vendorName: $scope.basicDetail.entityName,
                        vendorType: $scope.registrationRequest.vendorType,
                        city: $scope.basicDetail.vendorAddress.city,
                        state: $scope.basicDetail.vendorAddress.state
                    }
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Duplicate Vendor Name. Please enter unique Vendor Name");
                        return;
                    } else {
                        callback();
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };


            function checkDisclaimer() {
                if ($scope.basicDetail.disclaimerAccepted == true) {
                    if ($scope.basicDetail.approvalChecked == true) {
                        return true;
                    } else {
                        $toastService.create("Please accept All Terms to proceed.");
                    }

                } else {
                    $toastService.create("Please accept the legal disclaimer to proceed.");
                }
                return false;
            }

            function getAuth() {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getAuth
                }).then(function (response) {
                    authService.setAuthorization(response.data);
                    appUtil.getCities(function (cities) {
                        $scope.cities = cities;
                    });
                    var search = $location.search();
                    if (!appUtil.isEmptyObject(search) && !appUtil.isEmptyObject(search.id)) {
                        validateRequest(encodeURIComponent(search.id));
                    }
                });
            }

            $scope.init = function () {
                $scope.basicDetail = {};
                getAuth();
                $scope.editMode = false;
                $scope.isDisabled = false;
            };


            $scope.changeVendorLocation = function (location) {
                if (!appUtil.isEmptyObject(location)) {
                    location = JSON.parse(location);
                    var address = $scope.basicDetail["vendorAddress"];
                    if (appUtil.isEmptyObject(address)) {
                        address = {};
                    }
                    address["city"] = location.name;
                    address["state"] = location.state.name;
                    address["locationId"] = location.code;
                    address["stateCode"] = location.state.code;
                    address["country"] = location.country.name; //TODO fix this wrt to countries.js

                    $scope.basicDetail["vendorAddress"] = address;
                }
            };

            $scope.saveBasicDetails = function () {

                if (validateBasicDetails()) {
                    if (checkDisclaimer()) {
                        $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                            if (result) {
                                $scope.duplicateNameCheck(saveDetails);
                            }
                        });
                    }
                } else {
                    $toastService.create("Please enter all details properly before submitting");
                }

            };

            function saveDetails() {
                $scope.basicDetail.registrationId = $scope.registrationRequest.id;
                $scope.basicDetail.type = $scope.registrationRequest.vendorType;
                $scope.basicDetail.vendorAddress.addressType = "VENDOR_ADDRESS";
                $scope.basicDetail.vendorAddress.addressContact = $scope.basicDetail.primaryContact;
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.saveBasicDetails,
                    data: $scope.basicDetail
                }).then(function (response) {
                    if (response.data.vendorId != null) {
                        $state.go("vendorCompany", { basicDetail: response.data, requestedChangesData: $scope.requestedChangesData })
                    }
                }, function (response) {
                    $toastService.create("Couldn't save details. Please try again!");
                    console.log("Got error while loading attribute values", response);
                });
            }

            $scope.goBack = function () {

            };
        }
    ]
).controller('vendorContractCreationCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
            $alertService, $location, authService, $state) {

            function validateRequest(id) {
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.validateContractRequest,
                    data: { token: id }
                }).then(function (response) {
                    $scope.workOrderInfo = response.data;
                    console.log($scope.workOrderInfo);
                    getUrl($scope.workOrderInfo.unsignedDocumentId);
                }, function (response) {
                    console.log("Not a valid request ", response.data);
                    if( !appUtil.isEmptyObject(response.data) && !appUtil.isEmptyObject(response.data.errorMsg) ) {
                        $toastService.create(response.data.errorMsg);
                    }
                    $state.go("vendorError");
                });
            }

            function getUrl(id) {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getUrl + "?documentId=" + id
                }).then(function (response) {
                    $scope.getUnsignedDocumentUrl = response.data;
                    if (document.getElementById("docPdf") != null) {
                        document.getElementById("docPdf").data = $scope.getUnsignedDocumentUrl;//+"#toolbar=0"
                    }
                }, function (response) {
                    console.log("Not a valid request");
                    $state.go("vendorError");
                });
            }

            function getAuth() {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getAuth
                }).then(function (response) {
                    authService.setAuthorization(response.data);
                    var search = $location.search();
                    if (!appUtil.isEmptyObject(search) && !appUtil.isEmptyObject(search.id)) {
                        validateRequest(encodeURIComponent(search.id));
                    }
                });
            }

            $scope.resetSignature = function () {
                $scope.signaturePad.clear();
            }
            function dataURItoBlob(dataURI) {
                var parts = dataURI.split(",");
                var mimeType = parts[0].split(":")[1].split(";")[0];
                var byteString = atob(parts[1]);

                // Create a Uint8Array to hold the binary data
                var arrayBuffer = new ArrayBuffer(byteString.length);
                var uint8Array = new Uint8Array(arrayBuffer);

                // Populate the Uint8Array with binary data
                for (var i = 0; i < byteString.length; i++) {
                    uint8Array[i] = byteString.charCodeAt(i);
                }

                // Create a Blob from the Uint8Array and return it
                return new Blob([uint8Array], { type: mimeType });
            }
            $scope.saveSignature = function () {
                if ($scope.signaturePad.isEmpty()) {
                    $toastService.create("Signature Cannot Be Empty");
                    return;
                }
                var ds = document.getElementById("signature-pad");
                var imageURI = $scope.signaturePad.toDataURL();
                var blobObject = dataURItoBlob(imageURI);
                var imageURL = URL.createObjectURL(blobObject);

                var fd = new FormData();
                fd.append('fileType', "image/png");
                fd.append('mimeType', ".png");
                fd.append('file', blobObject);
                fd.append('woId', $scope.workOrderInfo.workOrderId);
                $http({
                    url: apiJson.urls.vendorManagement.digitalSignature,
                    method: 'POST',
                    data: fd,
                    headers: {
                        'Content-Type': undefined
                    },
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Signature Saved Successfully");
                        $scope.signatureId = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    if (response.errorMsg != null) {
                        toastService.create(response.errorMsg);
                    }
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });

            }


            $scope.init = function () {
                $scope.clientInfo = null;
                $scope.signature = null;
                $scope.isSignatureCreated = false;
                $.getJSON('https://api.db-ip.com/v2/free/self', function (data) {
                    $scope.clientInfo = JSON.stringify(data, null, 2);
                }).then(function (response) {
                    $scope.clientInfo = JSON.parse($scope.clientInfo);
                    $scope.workOrderInfo = null;
                    $scope.enteredOTP = null;
                    $scope.thankYou = false;
                    $scope.otpSent = false;
                    getAuth();
                });

            };

            $scope.sendOTP = function () {
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.triggerOTP + "?vendorId=" + $scope.workOrderInfo.vendorId
                }).then(function (response) {
                    if (response.data) {
                        $toastService.create("OTP send to mail!");
                        $scope.otpSent = true;
                    }
                });
            }

            $scope.setOTP = function (val) {
                $scope.enteredOTP = val;
            }
            $scope.setName = function (val) {
                $scope.name = val;
            }
            $scope.setDesignation = function (val) {
                $scope.designation = val;
                if (!$scope.isSignatureCreated) {
                    var canvas = document.getElementById("signature");
                    $scope.signaturePad = new SignaturePad(canvas, {
                        backgroundColor: 'rgb(250,250,250)'
                    });
                    $scope.isSignatureCreated = true;
                }
            }

            $scope.validContract = function (val) {
                $scope.isValidate = val;
            }

            function acceptContract(val) {
                var search = $location.search();
                if (!appUtil.isEmptyObject(search) && !appUtil.isEmptyObject(search.id)) {
                    var token = encodeURIComponent(search.id);
                }
                var payload = {
                    workOrderId: $scope.workOrderInfo.workOrderId,
                    workOrderStatus: val,
                    vendorName: $scope.name,
                    vendorDesignation: $scope.designation,
                    vendorIpAddress: $scope.clientInfo.ipAddress,
                    vendorDigitalSignId: $scope.signatureId,
                    token: token
                }
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.acceptContract,
                    data: payload
                }).then(function (response) {
                    if (response.data) {
                        $toastService.create("Contract " + val);
                        $scope.thankYou = true;
                    }
                });
            }

            $scope.validateOTP = function (val) {
                $alertService.confirm("Are you sure?", null, function (result) {
                    if (result) {
                        if (!$scope.isValidate) {
                            $toastService.create("Accept the terms");
                            return;
                        }
                        if ($scope.enteredOTP !== null && $scope.enteredOTP.length != 4) {
                            $toastService.create("Enter the OTP");
                            return;
                        }
                        var payload = {
                            vendorId: $scope.workOrderInfo.vendorId,
                            otpValue: $scope.enteredOTP
                        }
                        $http({
                            method: "POST",
                            url: apiJson.urls.vendorManagement.validateOTP,
                            data: payload
                        }).then(function (response) {
                            if (response.data && response.status === 200) {
                                $toastService.create("OTP verified Successfully");
                                acceptContract(val);
                            } else {
                                if (response.errorMsg != null) {
                                    $toastService.create(response.errorMsg);
                                } else {
                                    $toastService.create("OTP verified Failed");
                                }
                                return;
                            }
                        });
                    } else {
                        return;
                    }
                });

            }
        }
    ]
).controller('vendorContractApprovalCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
            $alertService, $location, authService, $state) {

            function validateRequest(id) {
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.validateContractRequest,
                    data: { token: id }
                }).then(function (response) {
                    $scope.workOrderInfo = response.data;
                    getVendorDetail($scope.workOrderInfo.vendorId);
                    console.log($scope.workOrderInfo);
                    $scope.downloadDocumentId = $scope.workOrderInfo.signedDocumentId;
                    if($scope.downloadDocumentId == null) {
                        $scope.downloadDocumentId = $scope.workOrderInfo.unsignedDocumentId;
                    }
                    getUrl($scope.downloadDocumentId);
                }, function (response) {
                    console.log("Not a valid request");
                    $state.go("vendorError");
                });
            }

            function getUrl(id) {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getUrl + "?documentId=" + id
                }).then(function (response) {
                    $scope.docId = response.data;
                    if (document.getElementById("docPdf") != null) {
                        document.getElementById("docPdf").data = $scope.docId;//+"#toolbar=0"
                    }
                }, function (response) {
                    console.log("Not a valid request");
                    $state.go("vendorError");
                });
            }

            function getAuth() {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getAuth
                }).then(function (response) {
                    authService.setAuthorization(response.data);
                    var search = $location.search();
                    if (!appUtil.isEmptyObject(search) && !appUtil.isEmptyObject(search.id)) {
                        validateRequest(encodeURIComponent(search.id));
                    }
                });
            }

            function getVendorDetail(vendorId) {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getVendor + "/" + vendorId
                }).then(function (response) {
                    $scope.vendor = response.data;
                    console.log($scope.vendor);
                });
            }

            $scope.resetSignature = function () {
                $scope.signaturePad.clear();
            }
            function dataURItoBlob(dataURI) {
                var parts = dataURI.split(",");
                var mimeType = parts[0].split(":")[1].split(";")[0];
                var byteString = atob(parts[1]);

                // Create a Uint8Array to hold the binary data
                var arrayBuffer = new ArrayBuffer(byteString.length);
                var uint8Array = new Uint8Array(arrayBuffer);

                // Populate the Uint8Array with binary data
                for (var i = 0; i < byteString.length; i++) {
                    uint8Array[i] = byteString.charCodeAt(i);
                }

                // Create a Blob from the Uint8Array and return it
                return new Blob([uint8Array], { type: mimeType });
            }
            $scope.saveSignature = function () {
                if ($scope.signaturePad.isEmpty()) {
                    $toastService.create("Signature Cannot Be Empty");
                    return;
                }
                var ds = document.getElementById("signature-pad");
                var imageURI = $scope.signaturePad.toDataURL();
                var blobObject = dataURItoBlob(imageURI);
                var imageURL = URL.createObjectURL(blobObject);

                var fd = new FormData();
                fd.append('fileType', "image/png");
                fd.append('mimeType', ".png");
                fd.append('file', blobObject);
                fd.append('woId', $scope.workOrderInfo.workOrderId);
                $http({
                    url: apiJson.urls.vendorManagement.digitalSignature,
                    method: 'POST',
                    data: fd,
                    headers: {
                        'Content-Type': undefined
                    },
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Signature Saved Successfully");
                        $scope.signatureId = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    if (response.errorMsg != null) {
                        toastService.create(response.errorMsg);
                    }
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });

            }


            $scope.init = function () {
                $scope.clientInfo = null;
                $scope.signature = null;
                $.getJSON('https://api.db-ip.com/v2/free/self', function (data) {
                    $scope.clientInfo = JSON.stringify(data, null, 2);
                }).then(function (response) {
                    $scope.clientInfo = JSON.parse($scope.clientInfo);
                    $scope.contractInfo = null;
                    $scope.vendor = null;
                    $scope.enteredOTP = null;
                    $scope.thankYou = false;
                    $scope.otpSent = false;
                    $scope.isSignatureCreated = false;
                    getAuth();
                });

            };

            $scope.sendOTP = function () {
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.triggerOTP + "?employeeId=" + $scope.contractInfo.approvalRequestFrom
                }).then(function (response) {
                    if (response.data) {
                        $toastService.create("OTP send to Registered Number");
                        $scope.otpSent = true;
                    }
                });
            }

            $scope.setOTP = function (val) {
                $scope.enteredOTP = val;
                if($scope.enteredOTP != null && $scope.enteredOTP.length == 4) {
                    if (!$scope.isSignatureCreated) {
                        var canvas = document.getElementById("signature");
                        $scope.signaturePad = new SignaturePad(canvas, {
                            backgroundColor: 'rgb(250,250,250)'
                        });
                        $scope.isSignatureCreated = true;
                    }
                    
                }
            }

            $scope.validContract = function (val) {
                $scope.isValidate = val;
            }

            function acceptContract(val) {
                var search = $location.search();
                if (!appUtil.isEmptyObject(search) && !appUtil.isEmptyObject(search.id)) {
                    var token = (encodeURIComponent(search.id));
                }
                var payload = {
                    bypassContractId: $scope.contractInfo.bypassContractId,
                    status: val,
                    authIpAddress: $scope.clientInfo.ipAddress,
                    authDigitalSignID: $scope.signatureId,
                    token: token
                }
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.applyContract,
                    data: payload
                }).then(function (response) {
                    if (response.data) {
                        $toastService.create("Contract " + val);
                        $scope.thankYou = true;
                    }
                });
            }

            $scope.validateOTP = function (val) {
                $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                    if (result) {
                        if (!$scope.isValidate) {
                            $toastService.create("Accept the terms");
                            return;
                        }
                        if ($scope.enteredOTP !== null && $scope.enteredOTP.length != 4) {
                            $toastService.create("Enter the OTP");
                            return;
                        }
                        var payload = {
                            employeeId: $scope.contractInfo.approvalRequestFrom,
                            otpValue: $scope.enteredOTP
                        }
                        $http({
                            method: "POST",
                            url: apiJson.urls.vendorManagement.validateOTP,
                            data: payload
                        }).then(function (response) {
                            if (response.data) {
                                $toastService.create("OTP verified Successfully");
                                acceptContract(val);
                            } else {
                                $toastService.create("OTP verified Failed");
                                return;
                            }
                        });
                    }
                });

            }
        }
    ]
).controller('vendorCompanyCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state', '$stateParams', '$fileUploadService',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
            $alertService, $location, authService, $state, $stateParams, $fileUploadService) {

            $scope.init = function () {
                $scope.basicDetail = $stateParams.basicDetail;
                $scope.requestedChangesData = $stateParams.requestedChangesData;

                $scope.detailCompany = {};

                if ($scope.requestedChangesData != null) {
                    $scope.requestedChangesData.map(function (val) {
                        if (val.tableName === "COMPANY_DETAIL") {
                            $scope.detailCompany[val.fieldName] = val;
                        }
                    })
                    $scope.showRequestedChangesLabel = false;
                    if (Object.keys($scope.detailCompany).length > 0) {
                        $scope.showRequestedChangesLabel = true;
                    }
                    console.log("requested company detail changes ", $scope.detailCompany)
                }

                if (!appUtil.isEmptyObject($scope.basicDetail.vendorId)) {
                    checkIfActivated($scope.basicDetail.vendorId);
                }
                if (appUtil.isEmptyObject($scope.basicDetail)) {
                    $alertService.alert("Link Broken!", "Please click on the link sent in your mail to continue registration process");
                }
                $scope.companyDetail = !appUtil.isEmptyObject($scope.basicDetail.companyDetails) ? $scope.basicDetail.companyDetails : {};
                if (appUtil.isEmptyObject($scope.companyDetail)) {
                    $scope.companyDetail['exemptSupplier'] = "false";
                } else {
                    $scope.companyDetail['exemptSupplier'] = $scope.companyDetail['exemptSupplier'].toString();
                    if (appUtil.isEmptyObject($scope.companyDetail.msmeExpirationDate)) {
                        $scope.companyDetail.msmeExpirationDate = null;
                    } else {
                        $scope.companyDetail.msmeExpirationDate = appUtil.formatDate($scope.companyDetail.msmeExpirationDate, "yyyy-MM-dd");
                    }
                }
                $scope.minDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");


                appUtil.getCities(function (cities) {
                    $scope.cities = cities;
                });
                $scope.editMode = false;
                $scope.companyTypes = ['INDIVIDUAL', 'PRIVATE_LTD', 'PUBLIC_LTD', 'CORPORATE', 'HUF', 'PROPRIETARY', 'LLP', 'TRUST',
                    'BODY_OF_INDIVIDUALS', 'ASSOCIATION_OF_PERSONS', 'LOCAL_AUTHORITY', 'GOVERNMENT_AGENCY', 'PARTNERSHIP', 'AJP'];
                $scope.businessTypes = ["GOODS", "SERVICES", "GOODS_AND_SERVICES"];
                $scope.docUploadType = "COMPANY";
                $scope.changeCompanyType($scope.companyTypes[0]);
                $scope.changeBusinessType($scope.businessTypes[0]);
                $scope.isDisabled = false;
            };


            function checkIfActivated(vendorId) {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.checkForVendorApprovedAtleastOnce,
                    params: {
                        vendorId: vendorId
                    }
                }).then(function (response) {
                    var result = response.data;
                    if (result) {
                        $scope.isDisabled = true;
                    }
                }, function (response) {
                    console.log("error while finding vendor logs");
                });
            }


            $scope.isExemptSupplier = function (value) {
                $scope.companyDetail.exemptSupplier = value;
            };

            $scope.isMsmeRegistered = function (value) {
                $scope.companyDetail.msmeRegistered = value;
                if (!value == 'true') {
                    $scope.companyDetail.msmeDocument = null;
                    $scope.companyDetail.msmeExpirationDate = null;
                }
            };

            $scope.changeBusinessType = function (type) {
                $scope.companyDetail.businessType = type;
                if (type == "GOODS") {
                    $scope.getST = false;
                    $scope.getVat = true;
                } else {
                    $scope.getST = true;
                    $scope.getVat = false;
                }
            };

            $scope.changeCompanyType = function (type) {
                $scope.companyDetail.entityType = type;
                switch (type) {
                    case 'PUBLIC_LTD':
                    case 'PRIVATE_LTD':
                        $scope.getCin = true;
                        $scope.getArc = false;
                        break;
                    case 'LLP':
                        $scope.getArc = true;
                        $scope.getCin = false;
                        break;
                    default:
                        $scope.getArc = false;
                        $scope.getCin = false;
                        break;
                }
            };


            $scope.uploadMSME = function () {
                $fileUploadService.openFileModal("Upload MSME scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "MSME", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.msmeDocument = doc;
                    });
                });
            };

            $scope.uploadCIN = function () {
                $fileUploadService.openFileModal("Upload CIN scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "CIN", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.cinDocument = doc;
                    });
                });
            };

            $scope.uploadPAN = function () {
                $fileUploadService.openFileModal("Upload PAN scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "PAN", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.panDocument = doc;
                    });
                });
            };

            $scope.uploadARC = function () {
                $fileUploadService.openFileModal("Upload ARC scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "ARC", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.arcDocument = doc;
                    });
                });
            };

            $scope.uploadST = function () {
                $fileUploadService.openFileModal("Upload Service Tax Certificate Copy", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "SERVICE_TAX", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.serviceTaxDocument = doc;
                    });
                });
            };

            $scope.uploadVAT = function () {
                $fileUploadService.openFileModal("Upload VAT Certificate Copy", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "VAT", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.vatDocument = doc;
                    });
                });
            };

            $scope.uploadCST = function () {
                $fileUploadService.openFileModal("Upload CST Certificate copy", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "CST", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.cstDocument = doc;
                    });
                });
            };

            function validateCompany(companyForm) {
                return validateForm(companyForm.$name);
            }

            $scope.saveCompanyDetails = function () {
                if ($scope.basicDetail.companyDetails != null) {
                    $scope.saveCompanyDetail();
                } else {
                    checkIfAlreadyFilled();
                }
            };

            function checkIfAlreadyFilled() {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getVendor + "/" + $scope.basicDetail.vendorId
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response.data.companyDetails)) {
                        $alertService.alert("Already Submitted !!", "Details Are Already Filled In Using This Link . " +
                            "Please click on the link sent in your mail to continue registration process", function () { }, false);
                    } else {
                        $scope.duplicatePanCheck();
                    }
                });
            }



            $scope.duplicatePanCheck = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.checkDuplicatePanNumber,
                    params: {
                        vendorType: $scope.basicDetail.type,
                        vendorPan: $scope.companyDetail.pan
                    }
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Duplicate PAN Number. Please enter unique pan number.");
                        return;
                    } else {
                        $scope.saveCompanyDetail();
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };


            function validatePan4thChar(panNumber) {
                switch ($scope.companyDetail.entityType.toUpperCase()) {
                    case 'INDIVIDUAL':
                        return (panNumber[3].toUpperCase() == 'P');
                    case 'PRIVATE_LTD':
                        return (panNumber[3].toUpperCase() == 'C');
                    case 'PUBLIC_LTD':
                        return (panNumber[3].toUpperCase() == 'C');
                    case 'CORPORATE':
                        return (panNumber[3].toUpperCase() == 'C');
                    case 'HUF':
                        return (panNumber[3].toUpperCase() == 'H');
                    case 'PROPRIETARY':
                        return (panNumber[3].toUpperCase() == 'F');
                    case 'LLP':
                        return (panNumber[3].toUpperCase() == 'F');
                    case 'TRUST':
                        return (panNumber[3].toUpperCase() == 'T');
                    case 'BODY_OF_INDIVIDUALS':
                        return (panNumber[3].toUpperCase() == 'B');
                    case 'ASSOCIATION_OF_PERSONS':
                        return (panNumber[3].toUpperCase() == 'A');
                    case 'LOCAL_AUTHORITY':
                        return (panNumber[3].toUpperCase() == 'L');
                    case 'GOVERNMENT_AGENCY':
                        return (panNumber[3].toUpperCase() == 'G');
                    case 'AJP':
                        return (panNumber[3].toUpperCase() == 'J');
                    default:
                        return false;
                }
            }

            function validatingPan() {
                var panNumber = $scope.companyDetail.pan;
                if (appUtil.isEmptyObject(panNumber)) {
                    $toastService.create("please enter pan number");
                    return false;
                }
                if (appUtil.isEmptyObject($scope.companyDetail.entityType)) {
                    $toastService.create("Please select entity type");
                    return false;
                }
                if (!validatePan4thChar(panNumber)) {
                    $toastService.create("Pan Number doesnot match with entity type");
                    return false;
                }
                return true;

            }

            $scope.saveCompanyDetail = function () {
                if ($scope.companyDetail.panDocument == null) {
                    $toastService.create("You need to upload PAN document before submitting Company Details");
                    return;
                }
                if (!validatingPan()) {
                    return;
                }
                if (appUtil.isEmptyObject($scope.companyDetail.msmeRegistered)) {
                    $toastService.create("Please Select the MSME registration..!");
                    return;
                }

                if ($scope.companyDetail.msmeRegistered == 'true' && $scope.companyDetail.msmeDocument == null) {
                    $toastService.create("You need to upload MSME document before submitting Company Details");
                    return;
                }

                if (!$scope.companyDetail.exemptSupplier) {
                    if ($scope.getCin && $scope.companyDetail.cinDocument == null) {
                        $toastService.create("You need to upload CIN document before submitting Company Details");
                        return;
                    }

                    if ($scope.getArc && $scope.companyDetail.arcDocument == null) {
                        $toastService.create("You need to upload ARC document before submitting Company Details");
                        return;
                    }

                    if ($scope.companyDetail.cstDocument == null) {
                        $toastService.create("You need to upload CST document before submitting Company Details");
                        return;
                    }

                    if ($scope.getVat && $scope.companyDetail.vatDocument == null) {
                        $toastService.create("You need to upload VAT document before submitting Company Details");
                        return;
                    }

                    if ($scope.getST && $scope.companyDetail.serviceTaxDocument == null) {
                        $toastService.create("You need to upload Service Tax document before submitting Company Details");
                        return;
                    }
                }

                if (validateCompany($scope.companyDetailForm)) {
                    $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                        if (result) {
                            $scope.companyDetail.vendorDetail = appUtil.createVendor($scope.basicDetail);
                            $scope.companyDetail.companyAddress.addressType = "REGISTERED_ADDRESS";
                            $scope.companyDetail.companyAddress.addressContact = $scope.basicDetail.primaryContact;
                            $http({
                                method: "POST",
                                url: apiJson.urls.vendorManagement.saveCompanyDetails,
                                data: $scope.companyDetail
                            }).then(function (response) {
                                if (response.data.vendorId != null) {
                                    $scope.basicDetail.companyDetails = response.data;
                                    if ($scope.basicDetail.type == 'CUSTOMER') {
                                        $state.go("vendorLocation", { basicDetail: $scope.basicDetail, requestedChangesData: $scope.requestedChangesData })
                                    } else {
                                        $state.go("vendorAccount", { basicDetail: $scope.basicDetail, requestedChangesData: $scope.requestedChangesData })
                                    }

                                }
                            }, function (response) {
                                $toastService.create("Couldn't save details. Please try again!");
                                console.log("Got error while loading attribute values", response);
                            });
                        }
                    });
                } else {
                    $toastService.create("Please fill in all the details correctly");
                }
            };

            $scope.changeCompanyLocation = function (location) {
                if (!appUtil.isEmptyObject(location)) {
                    location = JSON.parse(location);
                    var address = $scope.companyDetail["companyAddress"];
                    if (appUtil.isEmptyObject(address)) {
                        address = {};
                    }
                    address["city"] = location.name;
                    address["state"] = location.state.name;
                    address["country"] = location.country.name;
                    address["locationId"] = location.code;
                    address["stateCode"] = location.state.code;
                    $scope.companyDetail["companyAddress"] = address;
                }
            };


            $scope.goBack = function () {
                $state.go("registerVendor");
            };
        }
    ]
).controller('vendorAccountCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state', '$stateParams', '$fileUploadService',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
            $alertService, $location, authService, $state, $stateParams, $fileUploadService) {

            $scope.init = function () {
                $scope.basicDetail = $stateParams.basicDetail;
                $scope.requestedChangesData = $stateParams.requestedChangesData;

                $scope.detailAccount = {};

                if ($scope.requestedChangesData != null) {
                    $scope.requestedChangesData.map(function (val) {
                        if (val.tableName === "ACCOUNT_DETAIL") {
                            $scope.detailAccount[val.fieldName] = val;
                        }
                    })
                    $scope.showRequestedChangesLabel = false;
                    if (Object.keys($scope.detailAccount).length > 0) {
                        $scope.showRequestedChangesLabel = true;
                    }
                    console.log("requested account detail changes ", $scope.detailAccount)
                }

                if (appUtil.isEmptyObject($scope.basicDetail)) {
                    $alertService.alert("Link Broken!", "Please click on the link sent in your mail to continue registration process");
                }

                $scope.accountDetail = !appUtil.isEmptyObject($scope.basicDetail.accountDetails) ? $scope.basicDetail.accountDetails : {};
                appUtil.getCities(function (cities) {
                    $scope.cities = cities;
                });

                $scope.editMode = false;
                $scope.accountTypes = ["SAVINGS", "CURRENT"];
                $scope.docUploadType = "ACCOUNT";
                $scope.changeAccountType($scope.accountTypes[0]);
            };

            $scope.changeAccountType = function (type) {
                $scope.accountDetail.kindOfAccount = type;
            };

            $scope.checkIfAlreadyFilled = function () {
                if (appUtil.isEmptyObject($scope.basicDetail.accountDetails)) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.vendorManagement.getVendor + "/" + $scope.basicDetail.vendorId
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data.accountDetails)) {
                            $alertService.alert("Already Submitted !!", "Details Are Already Filled In Using This Link . " +
                                "Please click on the link sent in your mail to continue registration process", function () { }, false);
                        } else {
                            $scope.saveAccountDetails();
                        }
                    });
                } else {
                    $scope.saveAccountDetails();
                }

            }

            $scope.uploadCheque = function () {
                $fileUploadService.openFileModal("Upload Cancelled Cheque scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "CHEQUE", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.accountDetail.cancelledCheque = doc;
                    });
                });
            };

            function validateAccountDetails() {
                return validateForm($scope.accountDetailForm.$name);
            }

            $scope.saveAccountDetails = function () {

                if ($scope.accountDetail.cancelledCheque == null) {
                    $toastService.create("You need to upload Cancelled Cheque Scanned Document before submitting Account Details");
                    return;
                }

                if (validateAccountDetails()) {
                    $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                        if (result) {
                            $scope.accountDetail.vendorDetail = appUtil.createVendor($scope.basicDetail);
                            $scope.accountDetail.paymentCycle = "MONTHLY";
                            $scope.accountDetail.accountType = "PRIMARY";
                            $http({
                                method: "POST",
                                url: apiJson.urls.vendorManagement.saveAccountDetails,
                                data: $scope.accountDetail
                            }).then(function (response) {
                                if (response.data.vendorId != null) {
                                    $scope.basicDetail.accountDetails = response.data;
                                    $state.go("vendorLocation", { basicDetail: $scope.basicDetail, requestedChangesData: $scope.requestedChangesData })
                                }
                            }, function (response) {
                                $toastService.create("Couldn't save details. Please try again!");
                                console.log("Got error while loading attribute values", response);
                            });
                        }
                    });
                } else {
                    $toastService.create("Please fill in all the details correctly");
                }
            };

            $scope.goBack = function () {
                $state.go("vendorCompany");
            };
        }
    ]
).controller('vendorLocationCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state', '$stateParams', '$fileUploadService',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
            $alertService, $location, authService, $state, $stateParams, $fileUploadService) {

            $scope.init = function () {
                $scope.basicDetail = $stateParams.basicDetail;
                $scope.location = {};
                $scope.addressTypes = ["DELIVERY", "DELIVERY_BILLING", "BILLING"];
                $scope.selectedAddressType = "DELIVERY_BILLING";
                if (appUtil.isEmptyObject($scope.basicDetail)) {
                    $alertService.alert("Link Broken!", "Please click on the link sent in your mail to continue registration process");
                }
                $scope.requestedChangesData = $stateParams.requestedChangesData;

                $scope.detailLocation = {};

                if ($scope.requestedChangesData != null) {
                    $scope.requestedChangesData.map(function (val) {
                        if (val.tableName === "DISPATCH_LOCATION") {
                            if ($scope.detailLocation[val.dispatchId] == null) {
                                $scope.detailLocation[val.dispatchId] = {};
                            }
                            $scope.detailLocation[val.dispatchId][val.fieldName] = val;
                        }
                    })
                    $scope.showRequestedChangesLabel = false;
                    if (Object.keys($scope.detailLocation).length > 0) {
                        $scope.showRequestedChangesLabel = true;
                    }
                    console.log("requested location detail changes ", $scope.detailLocation)
                }

                $scope.dispatchLocations = !appUtil.isEmptyObject($scope.basicDetail.dispatchLocations)
                    ? $scope.basicDetail.dispatchLocations : [];

                appUtil.getCities(function (cities) {
                    $scope.cities = cities;
                });

                $scope.editMode = false;
                $scope.gstStatusTypes = ["REGISTERED", "UNREGISTERED", "APPLIED_FOR"];
                $scope.notificationTypes = ["SMS", "EMAIL"];
                $scope.docUploadType = "GSTIN";

            };

            $scope.checkIfAlreadyFilled = function () {
                if (appUtil.isEmptyObject($scope.basicDetail.dispatchLocations)) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.vendorManagement.getVendor + "/" + $scope.basicDetail.vendorId
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data.dispatchLocations)) {
                            $alertService.alert("Already Submitted !!", "Details Are Already Filled In Using This Link . " +
                                "Please click on the link sent in your mail to continue registration process", function () { }, false);
                        } else {
                            $scope.saveLocations();
                        }
                    });
                } else {
                    $scope.saveLocations();
                }

            }

            $scope.changeAddressType = function (type) {
                $scope.selectedAddressType = type;
            }


            $scope.uploadGSTIN = function () {
                $fileUploadService.openFileModal("Upload GSTIN scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "GSTIN", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.location.gstinDocument = doc;
                    });
                });
            };

            function validateLocation() {

                if ($scope.location.gstStatus == "REGISTERED") {
                    if ($scope.location.gstinDocument == null) {
                        $toastService.create("Please upload GSTIN document");
                        return false;
                    }
                    if ($scope.location.gstin == null) {
                        $toastService.create("Please provide GSTIN number");
                        return false;
                    }
                    if($scope.location.gstin.substring(0,2) != $scope.location.address.stateCode )
                    {
                        $toastService.create("GSTIN not valid for the State provided. Please enter valid GSTIN");
                        return false;
                    }
                    if(!appUtil.isEmptyObject($scope.basicDetail.companyDetails) && !appUtil.isEmptyObject($scope.basicDetail.companyDetails.companyDetails) && $scope.location.gstin.substring(2,12).toUpperCase() != $scope.basicDetail.companyDetails.companyDetails.pan.toUpperCase() )
                    {
                        $toastService.create("GSTIN middle charaters doesnot match with your pan card number . Please enter valid GSTIN");
                        return false;
                    }
                }

                if (!appUtil.isEmptyObject($scope.location.notificationType) && $scope.location.notificationType.length > 0) {
                    return validateForm($scope.locationDetailForm.$name);
                } else {
                    $toastService.create("No notification type selected.Please select properly!");
                    return false;
                }
            }

            function containsLocation(find, locations) {
                locations.filter(function (location) {
                    return location.locationName == find.locationName;
                }).length > 0;
            }

            $scope.addToLocations = function (location) {
                if (validateLocation()) {
                    location.address.addressType = "DISPATCH_ADDRESS";
                    if (!containsLocation(location, $scope.dispatchLocations)) {
                        location.locationType = $scope.selectedAddressType;
                        $scope.dispatchLocations.push(angular.copy(location));
                        $scope.location = {};
                    } else {
                        $toastService.create("Location already added");
                    }
                } else {
                    $toastService.create("Please make sure you have entered correct location details");
                }
            };

            $scope.changeTaxStatus = function (status) {
                $scope.location.applyTax = status;
            };

            $scope.edit = function (index) {
                if (index != null && index != -1) {
                    $scope.location = angular.copy($scope.dispatchLocations[index]);
                    $scope.dispatchLocations.splice(index, 1);
                    $scope.dispatchLocations.push($scope.location);
                    // $scope.removeLocation(index);
                }
            };

            $scope.removeLocation = function (index) {
                if ($scope.dispatchLocations[index].dispatchId != null) {
                    if ($scope.removedLocations == undefined || $scope.removedLocations == null) {
                        $scope.removedLocations = [];
                    }
                    $scope.removedLocations.push($scope.dispatchLocations[index].dispatchId);
                }
                $scope.dispatchLocations.splice(index, 1);
            };

            $scope.changeGstStatus = function (status) {
                if (appUtil.isEmptyObject(status) || status != "REGISTERED") {
                    delete $scope.location.gstin;
                }
            };

            $scope.saveLocations = function () {
                if ($scope.dispatchLocations.length != 0 && $scope.basicDetail != null && $scope.basicDetail.vendorId != null) {
                    $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                        if (result) {
                            var reqObj = {
                                vendorId: $scope.basicDetail.vendorId,
                                locations: $scope.dispatchLocations
                            };
                            if ($scope.removedLocations != undefined) {
                                reqObj.removedLocations = $scope.removedLocations;
                            }
                            $http({
                                method: "POST",
                                url: apiJson.urls.vendorManagement.saveLocations,
                                data: reqObj
                            }).then(function (response) {
                                if (response.data.vendorId != null) {
                                    $scope.basicDetail.accountDetails = response.data;
                                    $state.go("vendorSuccess");
                                } else {
                                    $toastService.create("Got error while saving locations. Please try again!");
                                }
                            }, function (response) {
                                $toastService.create("Couldn't save details. Please try again!");
                                console.log("Got error while saving location details", response);
                            });
                        }
                    });
                } else {
                    $toastService.create("Please make sure you have more than one locations entered to save.");
                }
            };

            $scope.changeDispatchLocation = function (location) {
                if (!appUtil.isEmptyObject(location)) {
                    location = JSON.parse(location);
                    var address = $scope.location["address"];
                    if (appUtil.isEmptyObject(address)) {
                        address = {};
                    }
                    address["city"] = location.name;
                    address["state"] = location.state.name;
                    address["country"] = location.country.name;
                    address["locationId"] = location.code;
                    address["stateCode"] = location.state.code;
                    $scope.location["address"] = address;
                }
            };

            $scope.addToNotificationTypes = function (type, checked) {
                if ($scope.location == undefined || $scope.location == null) {
                    $scope.location = {};
                }
                if (appUtil.isEmptyObject($scope.location.notificationType)) {
                    $scope.location.notificationType = [];
                }

                var index = $scope.location.notificationType.indexOf(type);
                if (checked && index == -1) { //if checked and not found
                    $scope.location.notificationType.push(type);
                }
                if (!checked && index != -1) { //if not checked and found
                    $scope.location.notificationType.splice(index, 1);
                }
            };

            $scope.goBack = function () {
                $state.go("vendorAccount");
            };
        }
    ]
).controller('vendorCompletionCtrl', ['$rootScope', '$scope', '$toastService', '$location',
        function ($rootScope, $scope, $toastService, $location) {
            $scope.init = function () {
                $scope.showSuccess = $location.url() == "/vendorSuccess";
                $scope.showError = $location.url() == "/vendorError";
            };
        }
    ]
);
