

angular.module('scmApp').service('SCMDebounceService', ['$timeout', function ($timeout) {

    // Default settings
    var defaults = {
        delay: 300,
        leading: true,
        trailing: false,
        maxWait: null
    };

    // Create debounced function
    this.create = function (fn, options) {
        options = options || {};

        var delay = (typeof options.delay === 'number') ? options.delay : defaults.delay;
        var leading = (typeof options.leading === 'boolean') ? options.leading : defaults.leading;
        var trailing = (typeof options.trailing === 'boolean') ? options.trailing : defaults.trailing;
        var maxWait = (typeof options.maxWait === 'number') ? options.maxWait : defaults.maxWait;

        var timerPromise = null;
        var lastArgs = null;
        var lastThis = null;
        var lastInvokeTime = null;
        var result;
        var maxWaitTimer = null;

        function invoke() {
            lastInvokeTime = Date.now ? Date.now() : new Date().getTime();
            result = fn.apply(lastThis, lastArgs);
            lastArgs = lastThis = null;
        }

        function startTimer() {
            if (timerPromise) {
                $timeout.cancel(timerPromise);
            }
            timerPromise = $timeout(function () {
                timerPromise = null;
                if (trailing && lastArgs) {
                    invoke();
                }
                if (maxWaitTimer) {
                    $timeout.cancel(maxWaitTimer);
                    maxWaitTimer = null;
                }
            }, delay);
        }

        function scheduleMaxWait() {
            if (maxWait == null) { return; }
            if (maxWaitTimer) { $timeout.cancel(maxWaitTimer); }

            var now = Date.now ? Date.now() : new Date().getTime();
            var timeSinceLastInvoke = lastInvokeTime ? (now - lastInvokeTime) : Infinity;
            var remaining = (timeSinceLastInvoke >= maxWait) ? 0 : (maxWait - timeSinceLastInvoke);

            maxWaitTimer = $timeout(function () {
                if (timerPromise) {
                    $timeout.cancel(timerPromise);
                    timerPromise = null;
                }
                if (lastArgs) {
                    invoke();
                }
                maxWaitTimer = null;
            }, remaining);
        }

        function debounced() {
            lastArgs = Array.prototype.slice.call(arguments);
            lastThis = this;

            var shouldInvokeLeading = leading && !timerPromise;
            if (shouldInvokeLeading) {
                invoke();
            }

            startTimer();
            if (maxWait != null) {
                scheduleMaxWait();
            }
            return result;
        }

        debounced.cancel = function () {
            if (timerPromise) {
                $timeout.cancel(timerPromise);
                timerPromise = null;
            }
            if (maxWaitTimer) {
                $timeout.cancel(maxWaitTimer);
                maxWaitTimer = null;
            }
            lastArgs = lastThis = null;
        };

        debounced.flush = function () {
            if (timerPromise) {
                $timeout.cancel(timerPromise);
                timerPromise = null;
                if (lastArgs) {
                    invoke();
                }
            }
            if (maxWaitTimer) {
                $timeout.cancel(maxWaitTimer);
                maxWaitTimer = null;
            }
        };

        return debounced;
    };
}]
);
