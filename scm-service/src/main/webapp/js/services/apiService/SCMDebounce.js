angular.module('SCMDebounce', [])
.directive('ngDebounceClick', ['SCMDebounceService', function(SCMDebounceService) {
    return {
        restrict: 'A',
        scope: {
            ngDebounceClick: '&',
            debounceDelay: '@',
            leading: '@',
            trailing: '@'
        },
        link: function(scope, element) {
            var delay = parseInt(scope.debounceDelay);
            var leading = (scope.leading != null) ? scope.leading === 'true' : true;
            var trailing = (scope.trailing != null) ? scope.trailing === 'true' : false;
            var handler = SCMDebounceService.create(function() {
                scope.$apply(scope.ngDebounceClick);
            }, {
                delay: delay,
                leading: leading,
                trailing: trailing
            });

            element.on('click', handler);

            scope.$on('$destroy', function() {
                element.off('click', handler);
                handler.cancel && handler.cancel();
            });
        }
    };
}]);
