angular.module('scmApp').service('toast', ['$rootScope', '$timeout', '$window', function($rootScope, $timeout, $window) {
    $rootScope.toasts = [];
    var defaultDuration = 6000;

    function createToast(message, type, duration) {
        if (duration === undefined || duration === null) {
            duration = defaultDuration;
        }

        var toast = { message: message, type: type };
        $rootScope.toasts.push(toast);

        var toastDuration = angular.isNumber(duration) ? duration : defaultDuration;

        // Show Materialize toast if available
        if ($window.Materialize) {
            $window.Materialize.toast(message, toastDuration);
        }

        if (toastDuration !== Infinity) {
            $timeout(function() {
                var index = $rootScope.toasts.indexOf(toast);
                if (index !== -1) {
                    $rootScope.toasts[index].fadeOut = true;

                    $timeout(function() {
                        $rootScope.$applyAsync(function() {
                            $rootScope.toasts.splice(index, 1);
                        });
                    }, 500);
                }
            }, toastDuration);
        }
    }

    this.create = function(message, duration, callback) {
        createToast(message, 'create', duration);
        if (typeof callback === 'function') {
            callback();
        }
    };

    this.success = function(message, duration) {
        createToast(message, 'success', duration);
    };

    this.error = function(message, duration) {
        createToast(message, 'error', duration);
    };

    this.warning = function(message, duration) {
        createToast(message, 'warning', duration);
    };
}]);
