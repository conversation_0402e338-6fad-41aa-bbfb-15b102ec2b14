angular.module('scmApp').controller(
		'viewAllBccCtrl',
		['$rootScope', '$scope','$state', 'apiJson', '$http', '$stateParams', 'appUtil','metaDataService','$fileUploadService',
	        '$toastService','$alertService','Popeye', function ($rootScope, $scope, $state, apiJson, $http, $stateParams, appUtil, metaDataService,
	                                $fileUploadService,$toastService, $alertService, Popeye) {
	   	
    	$scope.init = function(){
    		$scope.soData = $stateParams.soData;
    		$scope.budgetCheck = true;
    		 $scope.receivings = $scope.calculateReceivings($stateParams.bccItems)
    	}
    	
    	 $scope.removeItemFromRcv = function (index,rcv, key){
             rcv.items.splice(index,1);
             if(rcv.items.length==0){
                 delete $scope.receivings[key];
             }
             if(Object.keys($scope.receivings).length==0){
                 $scope.receivings = undefined;
             }
         };

         $scope.calculateReceivings =  function(srItems) {
         	$scope.bccList = [];
             var receivings = {};
             for(var key in srItems){
                 addToMap(receivings,srItems[key]);
             }
             return receivings;
         }

         function addToMap(map, obj) {
             var key = obj.company.id + "_" + obj.state.id;
             if(appUtil.isEmptyObject(map[key])){
                 map[key] = {
                     company: obj.company,
                     location: obj.location,
                     state: obj.state,
                     items: []
                 };
             }
             map[key].items.push(obj);
             $scope.bccList.push(obj);
         }
    	
        $scope.prepareServiceItems = function(){
                for(var x = 0; x < $scope.bccList.length ; x++){
                    var item = {
                       // ascCode: $scope.bccList[x].code,
                        costElementId: $scope.bccList[x].costElementId,
                        costElementName: $scope.bccList[x].costElementName,
                        serviceDescription: $scope.bccList[x].serviceDescription,
                        requestedQuantity: $scope.bccList[x].receivedQuantity,
                        unitOfMeasure: $scope.bccList[x].unitOfMeasure,
                        unitPrice: $scope.bccList[x].unitPrice,
                        totalCost: $scope.bccList[x].totalCost,
                        totalTax: $scope.bccList[x].totalTax,
                        amountPaid: $scope.bccList[x].totalAmount,
                        taxRate: $scope.bccList[x].taxRate,
                        businessCostCenterId: $scope.bccList[x].businessCostCenterId,
                        businessCostCenterName: $scope.bccList[x].businessCostCenterName,
                        type: $scope.bccList[x].type ,
                        state: $scope.bccList[x].company.id + "_" + $scope.bccList[x].state.id
                    };
                    
                    if($scope.serviceBccItems==null){
                        $scope.serviceBccItems=[];
                    }
                    $scope.serviceBccItems.push(item);
                }
            }
            
            $scope.submit = function(){
            	$scope.getDepartmentSOModal();
            };
            
            $scope.submitData = function(){
            	if($scope.budgetCheck){
            		 $toastService.create("Budget Exceeded!!!");
            		return;
            	}
            	//$scope.prepareServiceItems();
                $alertService.confirm("Are you sure on saving Service Order?","",function(result){
                   if(result){
                       sendRequestForSO();
                   }
                });
            };
            
            function prepareRcvngs(receivings){
                var allRcvngs = [];
                if(!appUtil.isEmptyObject(receivings)){
                    for(var key in receivings){
                        var rcv = receivings[key];
                        allRcvngs.push({
                        	dispatchLocationId:$scope.soData.dispatchLocationId,
                            vendorId:$scope.soData.vendorId,
                            costCenterId: $scope.soData.costCenterId,
                            userId:appUtil.getCurrentUser().userId,
                            type:  $scope.soData.type,
                            items: rcv.items
                        });
                    }
                }
                return allRcvngs;
            }
            
            function sendRequestForSO(){
            	 var reqObj = prepareRcvngs($scope.receivings);

              /*  var reqObj = {
                    dispatchLocationId:$scope.soData.dispatchLocationId,
                    vendorId:$scope.soData.vendorId,
                    costCenterId: $scope.soData.costCenterId,
                    userId:appUtil.getCurrentUser().userId,
                    type:  $scope.soData.type,
                    items:items
                };*/

                if(reqObj.length>0){
                    $http({
                        method:"POST",
                        url:apiJson.urls.serviceOrderManagement.createServiceOrder,
                        data:reqObj
                    }).then(function(response){
                        if(response.data!=null){
                            $scope.init();
                            $alertService.alert("Congratulations!!",
                                "Service Order with ID: <b>" + response.data + "</b> created successfully! <br>",
                                function(){
                                    $state.go("menu.viewSO",{createdSO:response.data,viewSO:true});
                                }
                            );
                        }else{
                            $toastService.create("Service Order could not be created due to some error!!");
                        }
                    },function(error){
                        console.log(error);
                        $toastService.create("Service Order could not be created due to some error!!");
                    });
                }
            }
            
            $scope.getDepartmentSOModal = function () {
                var modalInstance = Popeye.openModal({
                    templateUrl: 'departmentSOModal.html',
                    controller: 'departmentSOModalCtrl',
                    size: 'lg',
                    windowClass: 'my-modal-popup',
                    resolve: {
                    	receivingList : function () {
                            return $scope.bccList;
                        }
                    },
                    click: true,
                    keyboard: false
                });
                
                modalInstance.closed.then(function(check) {
                        $scope.budgetCheck = check;
                        $scope.submitData();
                });
            };
            
        }]
    ).controller('departmentSO1ModalCtrl', ['$scope', 'appUtil', 'Popeye', 'receivingList',
        function ($scope, appUtil, Popeye, receivingList) {

    	
    	$scope.init = function (){
    		$scope.summaryDepartmentList = [];
    		$scope.list = receivingList;
    		$scope.summaryList();
    	}
    	
    	$scope.summaryList = function () {
    		var map = new Map();
            for(var x = 0; x < $scope.list.length; x++){
            	if(map.has($scope.list[x].departmentName)){
            		var listData = map.get($scope.list[x].departmentName);
            		$scope.list[x].totalAmountDup = listData.totalAmountDup + $scope.list[x].totalAmount;
            		$scope.list[x].totalTaxDup = listData.totalTaxDup + $scope.list[x].totalTax;
            		$scope.list[x].totalCostDup = listData.totalCostDup + $scope.list[x].totalCost;
            		map.delete($scope.list[x].departmentName);
            		map.set($scope.list[x].departmentName,$scope.list[x]);
            	}
            	else{
            		$scope.list[x].totalAmountDup = $scope.list[x].totalAmount;
                	$scope.list[x].totalTaxDup = $scope.list[x].totalTax;
                	$scope.list[x].totalCostDup = $scope.list[x].totalCost;
                		 map.set($scope.list[x].departmentName,$scope.list[x]);
            	}
            }
            
            for (var value in map.values()) {
            	$scope.summaryDepartmentList.push(value);
            }
           }
    	
    	$scope.submit = function(){
    		$scope.closeModal(false);
    	}
    	
            $scope.closeModal = function closeModal(check) {
                Popeye.closeCurrentModal(check);
            }
            
        }
    ]
);
