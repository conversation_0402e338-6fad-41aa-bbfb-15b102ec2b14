angular.module('scmApp').controller('monkDayCloseCtrl', ['$scope', '$http', '$toastService', 'appUtil', 'apiJson',
    function($scope, $http, $toastService, appUtil, apiJson) {
        
        $scope.monkDayCloseEvent = null;
        $scope.availableStatuses = [];
        $scope.canUpdateMonkDown = false; // This can be set based on user permissions
        $scope.currentBusinessDate = null;
        
        // Helper function to format date safely without timezone issues
        $scope.formatDateSafely = function(date) {
            if (!date) return '';
            if (typeof date === 'string') return date; // Already formatted
            
            var year = date.getFullYear();
            var month = ('0' + (date.getMonth() + 1)).slice(-2);
            var day = ('0' + date.getDate()).slice(-2);
            return year + '-' + month + '-' + day;
        };
        
        $scope.init = function() {
            // Set current business date for display (readonly)
            var today = new Date();
            $scope.currentBusinessDate = $scope.formatDateSafely(today);
            
            // Set fallback statuses immediately
            $scope.setFallbackStatuses();
            
            // Wait for apiJson to be ready, then make API calls
            $scope.$watch('apiJson', function(apiJson) {
                if (apiJson && apiJson.urls && apiJson.urls.monkDayClose) {
                    console.log('apiJson is ready, making API calls');
                    // Load available monk statuses (will override fallback if API works)
                    $scope.loadAvailableStatuses();
                    
                    // Load current monk day close event
                    $scope.refreshMonkStatus();
                }
            }, true); // Deep watch to catch nested changes
            
            // Fallback: Try to make API calls after a delay if watch doesn't trigger
            setTimeout(function() {
                if (apiJson && apiJson.urls && apiJson.urls.monkDayClose) {
                    console.log('Fallback: apiJson is ready, making API calls');
                    $scope.loadAvailableStatuses();
                    $scope.refreshMonkStatus();
                } else {
                    console.log('Fallback: apiJson still not ready, using fallback statuses');
                }
            }, 1000);
        };
        
        $scope.loadAvailableStatuses = function() {
            console.log('loadAvailableStatuses called');
            
            if (!apiJson || !apiJson.urls || !apiJson.urls.monkDayClose) {
                console.warn('apiJson not ready, using fallback statuses');
                $scope.setFallbackStatuses();
                return;
            }
            
            // First try to load from API
            $http.get(apiJson.urls.monkDayClose.getMonkStatuses)
                .then(function(response) {
                    if (response.data.success) {
                        // Add "Select Status" placeholder to the API response
                        $scope.availableStatuses = [
                            { name: '', displayName: 'Select Status' }
                        ].concat(response.data.payload);
                        console.log('Statuses loaded from API:', $scope.availableStatuses);
                    } else {
                        console.warn('API failed, using fallback statuses:', response.data.message);
                        $scope.setFallbackStatuses();
                    }
                })
                .catch(function(error) {
                    console.error('Error loading monk statuses from API, using fallback:', error);
                    $scope.setFallbackStatuses();
                });
        };
        
        $scope.setFallbackStatuses = function() {
            // Show Select Status placeholder and Monk Down option
            $scope.availableStatuses = [
                { name: '', displayName: 'Select Status' },
                { name: 'MONK_DOWN', displayName: 'Monk Down' }
            ];
        };
        
        $scope.refreshMonkStatus = function() {
            var unitId = appUtil.getCurrentUser().unitId;
            
            // Use current business date for the API call
            $http.get(apiJson.urls.monkDayClose.getEvent, {
                params: {
                    unitId: unitId,
                    businessDate: $scope.currentBusinessDate
                }
            })
            .then(function(response) {
                if (response.data.success) {
                    $scope.monkDayCloseEvent = response.data.payload;
                    if ($scope.monkDayCloseEvent && $scope.monkDayCloseEvent.monkStatuses) {
                        // Initialize selected status to empty (shows "Select Status")
                        $scope.monkDayCloseEvent.monkStatuses.forEach(function(monkStatus) {
                            monkStatus.selectedStatus = '';
                        });
                    }
                } else {
                    $scope.monkDayCloseEvent = null;
                    // Don't show toast for "not found" - the UI message handles this
                    if (response.data.message && !response.data.message.includes('No monk day close event found')) {
                        $toastService.create(response.data.message);
                    }
                }
            })
            .catch(function(error) {
                console.error('Error refreshing monk status:', error);
                $toastService.create('Error refreshing monk status');
                $scope.monkDayCloseEvent = null;
            });
        };
        
        $scope.updateMonkStatus = function(monkStatus) {
            if (!monkStatus.selectedStatus || monkStatus.selectedStatus === '' || monkStatus.selectedStatus === monkStatus.monkStatus) {
                $toastService.create('Please select a valid status');
                return; // No change
            }
            
            // Check if trying to update a broken monk without permission
            if (monkStatus.monkStatus === 'MONK_DOWN' && !$scope.canUpdateMonkDown) {
                $toastService.create('You do not have permission to update broken monk status');
                monkStatus.selectedStatus = monkStatus.monkStatus; // Reset selection
                return;
            }
            
            // Directly update the monk status
            $scope.performMonkStatusUpdate(monkStatus);
        };
        
        $scope.performMonkStatusUpdate = function(monkStatus) {
            $http.put(apiJson.urls.monkDayClose.updateMonkStatus, {}, {
                params: {
                    monkStatusDayCloseId: monkStatus.monkStatusDayCloseId,
                    monkStatus: monkStatus.selectedStatus
                }
            })
            .then(function(response) {
                if (response.data.success) {
                    monkStatus.monkStatus = monkStatus.selectedStatus;
                    $toastService.create('Monk status updated successfully');
                } else {
                    $toastService.create(response.data.message || 'Failed to update monk status');
                    monkStatus.selectedStatus = monkStatus.monkStatus; // Reset selection
                }
            })
            .catch(function(error) {
                console.error('Error updating monk status:', error);
                $toastService.create('Error updating monk status');
                monkStatus.selectedStatus = monkStatus.monkStatus; // Reset selection
            });
        };
        
        $scope.getMonkStatusDisplayName = function(status) {
            // Handle all possible statuses
            if (status === 'MONK_DOWN') {
                return 'Monk Down';
            } else if (status === 'FAILED') {
                return 'Failed';
            } else if (status === 'INITIATED') {
                return 'Initiated';
            } else if (status === 'COMPLETED') {
                return 'Completed';
            }
            return status; // Fallback to original status
        };
    }
]);