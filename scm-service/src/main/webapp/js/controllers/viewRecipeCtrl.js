angular
    .module('scmApp')
    .controller(
        'viewRecipeCtrl',
        [
            '$rootScope',
            '$scope',
            'apiJson',
            '$http',
            'appUtil',
            '$toastService',
            '$timeout',
            '$window',
            'previewModalService',
            '$alertService',
            function ($rootScope, $scope, apiJson, $http, appUtil, $toastService,$timeout,$window,previewModalService, $alertService) {
                $scope.init = function () {
                    $scope.selectedQuantity = null;
                    $scope.selectedProduct = null;
                    $scope.prepPlanData=null;
                    $scope.showPrepPlan=false;
                    $scope.showPreview = previewModalService.showPreview;
                    $scope.getProdUnitToSku(appUtil.getActiveScmProducts());
                }

                $scope.getPlanForProduct = function () {
                    if ($scope.selectedProduct == null || $scope.selectedQuantity == null) {
                        $toastService.create("select product and quantity");
                        return;
                    }
                    $http({
                        method: "GET",
                        url: apiJson.urls.productManagement.planDetailForProduct,
                        params: {
                            productId: $scope.selectedProduct.productId,
                            unitId: appUtil.getCurrentUser().unitId
                        }
                    }).then(function success(response) {
                        if (response.data != null) {
                            console.log(response.data);
                            $scope.productPlan= response.data;
                            $scope.selectedProduct.id = $scope.productPlan.id;
                        }else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    }, function error(response) {
                        $scope.reset();
                        if (response.data.errorMessage != null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMessage, null, true);
                        } else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    });

                };

                $scope.getProdUnitToSku = function(activeScmProducts) {
                    $http({
                        method : "GET",
                        url : apiJson.urls.filter.availableProdfromUnitToSku,
                        params : {
                            unitId : appUtil.getCurrentUser().unitId
                        }
                    }).then(function success(response) {
                        if (response.data != null && response.data.length > 0) {
                            console.log(response.data);
                            $scope.scmProductDetails = $scope.getFilteredProds(activeScmProducts,response.data);
                        } else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };


                $scope.reset =  function(){
                    $scope.prepPlanData = null;
                    $scope.selectedQuantity = null;
                    $scope.printPlanData =  null;
                }
                $scope.getFilteredProds = function(activeScmProducts,ProdIds) {
                    var result = [];
                    angular.forEach(ProdIds, function(prod){
                        angular.forEach(activeScmProducts, function(scmProd){
                            if(scmProd.productId == prod) {
                                result.push(scmProd);
                            }
                        });
                    });
                    return result;
                };
                $scope.bySemiFinshed = function (value) {
                    return value.recipeRequired == true && value.autoProduction == false;
                };


                $scope.getRecipe = function () {
                    if ($scope.selectedProduct == null || $scope.selectedQuantity == null) {
                        $toastService.create("select product and quantity");
                        return;
                    }

                    $http({
                        url: apiJson.urls.requestOrderManagement.getPlanItemsForSemiFinishedProduct,
                        method: 'POST',
                        data: {
                            id: $scope.selectedProduct.id,
                            productId: $scope.selectedProduct.productId,
                            productName: $scope.selectedProduct.productName,
                            requestedQuantity: $scope.selectedQuantity,
                            requestedAbsoluteQuantity: $scope.selectedQuantity,
                            unitId: appUtil.getCurrentUser().unitId
                        },
                        params:{
                            isSkusRequired : true
                        },
                        headers: {
                            'Content-type': 'application/json'
                        }
                    }).success(function (data) {
                        if (data !== null) {
                            $scope.prepPlanData = data;
                            console.log($scope.prepPlanData);
                            $scope.showPrepPlan = true;
                            $scope.selectedProduct.printCount = $scope.selectedProduct.printCount === null ? 1 : $scope.selectedProduct.printCount + 1;

                            $scope.formatPlan($scope.prepPlanData);
                        } else {
                            $toastService.create("Error while getting plan data.");
                        }
                    }).error(function (err) {
                        console.log("Error while getting data", err);
                    });
                };

                $scope.formatPlan = function (plan) {
                    var index = 0;
                    var set = true;
                    plan.recipeNotes = plan.recipeNotes != null ? plan.recipeNotes.replace(/\n/g, "<br />") : plan.recipeNotes;
                    plan.planOrderItemPrepItems.map(function (item) {
                        item.recipeNotes = item.recipeNotes != null ? item.recipeNotes.replace(/\n/g, "<br />") : item.recipeNotes;
                        if (item.planOrderItemPrepItems != null && item.planOrderItemPrepItems.length > 0) {
                            set = true;
                        }
                        if (set) {
                            index++;
                            set = false;
                        }
                        item.stepIndex = index;
                        if (item.planOrderItemPrepItems != null && item.planOrderItemPrepItems.length > 0) {
                            index++;
                        }
                    });
                };

                $scope.formatRecipeOutput = function(prepPlanData) {
                    if (!prepPlanData) {
                        return '';
                    }

                    var areSameUom = prepPlanData.productUom === prepPlanData.outputUom;
                    var areSameConversion = Number(prepPlanData.productConversion) === Number(prepPlanData.outputConversion);

                    if (areSameUom && areSameConversion) {
                        return prepPlanData.productConversion + ' ' + prepPlanData.productUom;
                    } else {
                        return prepPlanData.productConversion + ' ' + prepPlanData.productUom +
                            ' (' + prepPlanData.outputConversion + ' ' + prepPlanData.outputUom + ')';
                    }
                };


                $scope.savePlanForSemiFinished = function () {
                    $scope.prepPlanData.requestedBy = {id: appUtil.getCurrentUser().userId, code: "", name: ""};
                    console.log($scope.prepPlanData);
                    //appUtil.getCurrentUser().unitId
                    $http({
                        url: apiJson.urls.requestOrderManagement.submitPrepPlan + '/' + appUtil.getCurrentUser().unitId,
                        method: 'POST',
                        data: $scope.prepPlanData,
                        headers: {
                            'Content-type': 'application/json'
                        }
                    }).success(function (data) {
                        if (data !== null) {
                            //$scope.prepPlanData = data;
                            $scope.showPrepPlan = false;
                            // console.log($scope.prepPlanData);
                            $timeout(function () {
                                $scope.$apply(function () {
                                    $scope.printPlanData = angular.copy($scope.prepPlanData);
                                    console.log($scope.printPlanData);
                                    $scope.printPlanData.planOrderItem.unitOfMeasure = $scope.selectedProduct.unitOfMeasure;
                                    $scope.formatPlan($scope.printPlanData);
                                });
                                $window.print();
                            }, 0);
                        } else {
                            $toastService.create("Error while saving plan data.");
                        }
                    }).error(function (err) {
                        console.log("Error while saving data", err);
                    });
                };
            }]);
