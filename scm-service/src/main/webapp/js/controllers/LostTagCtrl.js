angular.module('scmApp').controller('LostTagCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil',
    '$location', 'fileService', '$alertService','$toastService','metaDataService','$timeout','$filter',
    function ($rootScope, $scope, apiJson, $http, appUtil, $location, fileService, $alertService,$toastService, metaDataService,$timeout, $filter) {

        $scope.init = function () {
            $scope.List = [];
            $scope.approvalList = [];
            $scope.currentUserId = appUtil.getCurrentUser().userId;
            $scope.currentUnit = appUtil.getUnitData();
            $scope.getPendingApprovals($scope.currentUnit.id);
        };

        $scope.getPendingApprovals = function (unitId) {
            var imageBaseUrl;
            if (window.location.href.indexOf("orient") >= 0) {
                imageBaseUrl = "https://d8xnaajozedwc.cloudfront.net/";
            } else if (window.location.href.indexOf("internal") >= 0) {
                imageBaseUrl = "https://d8xnaajozedwc.cloudfront.net/";
            } else if (window.location.href.indexOf("sumo") >= 0) {
                imageBaseUrl = "https://d8xnaajozedwc.cloudfront.net/";
            } else if (window.location.href.indexOf("uat.kettle.chaayos") >= 0) {
                imageBaseUrl = "http://d3mikkh4u78xq4.cloudfront.net/";
            } else {
                imageBaseUrl = "http://d2h0i672lsq59f.cloudfront.net/";
            }
//            //
//            imageBaseUrl = "https://d8xnaajozedwc.cloudfront.net/";
//            //
            var skuBaseUrl = imageBaseUrl + "sku_image/";
            $scope.imageURL = skuBaseUrl;
            $http({
                url: apiJson.urls.stockManagement.checkPendingApprovals,
                method: 'GET',
                params: {
                    id:unitId
                }
            }).success(function (response) {
                $scope.approvalList = response["LOST_TAG"];
            }).error(function (response) {
                if (response.errorMsg != null) {
                    $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                } else {
                    $toastService.create("Could not fetch pending approvals.");
                }
            });
        };

        $scope.processRequest = function (requestId, res) {
            $http({
                url: apiJson.urls.stockManagement.processRequest,
                method: 'POST',
                params: {
                    requestId: requestId,
                    userId: $scope.currentUserId,
                    res: res
                }
            }).success(function (response) {
                if(response == true){
                    $toastService.create("Tag Lost Request Acknowledged");
                }
                else if(response == false){
                    $toastService.create("Could Not Process Request");
                }
                $scope.getPendingApprovals($scope.currentUnit.id);
            }).error(function (response) {
                if (response.errorMsg != null) {
                    $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                } else {
                    $toastService.create("Error In Processing Request");
                }
            });
        };

    }]) ;